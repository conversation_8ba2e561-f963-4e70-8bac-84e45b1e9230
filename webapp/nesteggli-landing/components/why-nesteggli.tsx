import { Card, CardContent } from "@/components/ui/card"
import { Coins, Target, TrendingUp, CreditCard, Sparkles } from "lucide-react"

const features = [
  {
    title: "Save as you spend",
    description: "Round-ups and micro-transfers make saving effortless.",
    icon: Coins,
  },
  {
    title: "Choose how to grow",
    description: "Fixed, balanced, or growth portfolios to match your goals.",
    icon: TrendingUp,
  },
  {
    title: "Set goals and track progress",
    description: "Visualize your progress and stay motivated.",
    icon: Target,
  },
  {
    title: "No bank switch needed",
    description: "Works with your existing cards and accounts.",
    icon: CreditCard,
  },
  {
    title: "Designed for your pace",
    description: "Customize your saving style to match your lifestyle.",
    icon: Sparkles,
  },
]

export default function WhyNesteggli() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Why nesteggli?</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            We make saving money as easy as spending it, with smart features designed around your life.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="overflow-hidden border-none shadow-md hover:shadow-lg transition-shadow duration-300"
            >
              <CardContent className="p-6">
                <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
