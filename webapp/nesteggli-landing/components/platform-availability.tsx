import { <PERSON>, CardContent } from "@/components/ui/card"
import { Monitor, Smartphone } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function PlatformAvailability() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Platform Availability</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            nesteggli will be available on multiple platforms to fit your lifestyle.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <Card className="border-none shadow-md">
            <CardContent className="p-6 text-center">
              <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Monitor className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Web App</h3>
              <p className="text-muted-foreground mb-4">Access nesteggli from any browser on any device.</p>
              <span className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                Coming Soon
              </span>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardContent className="p-6 text-center">
              <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Smartphone className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">iOS App</h3>
              <p className="text-muted-foreground mb-4">Save and invest on the go with our iOS app.</p>
              <span className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                Coming Soon
              </span>
            </CardContent>
          </Card>

          <Card className="border-none shadow-md">
            <CardContent className="p-6 text-center">
              <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                <Smartphone className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Android App</h3>
              <p className="text-muted-foreground mb-4">Manage your savings with our Android app.</p>
              <span className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium">
                Coming Soon
              </span>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12 max-w-md mx-auto">
          <Card className="border-none shadow-md">
            <CardContent className="p-6">
              <h3 className="text-xl font-semibold mb-4 text-center">Get notified when we launch</h3>
              <div className="flex space-x-2">
                <Input placeholder="Your email address" type="email" />
                <Button>Notify Me</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}
