import { Card, CardContent } from "@/components/ui/card"
import { CreditC<PERSON>, Settings, TrendingUp } from "lucide-react"

const steps = [
  {
    title: "Link your existing card",
    description: "Connect your Visa, Mastercard, or other payment cards securely.",
    icon: CreditCard,
  },
  {
    title: "Choose your saving style",
    description: "Select round-up, percentage of spend, or recurring contributions.",
    icon: Settings,
  },
  {
    title: "Watch your savings grow",
    description: "Sit back and watch your nest egg grow over time.",
    icon: TrendingUp,
  },
]

export default function HowItWorks() {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Getting started with nesteggli is simple and takes just minutes.
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-8 max-w-4xl mx-auto">
          {steps.map((step, index) => (
            <Card key={index} className="flex-1 border-none shadow-md">
              <CardContent className="p-6 text-center">
                <div className="mx-auto h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
                  <step.icon className="h-8 w-8 text-primary" />
                </div>
                <div className="h-10 w-10 rounded-full bg-primary text-white font-bold flex items-center justify-center mx-auto -mt-12 mb-4 border-4 border-white">
                  {index + 1}
                </div>
                <h3 className="text-xl font-semibold mb-2">{step.title}</h3>
                <p className="text-muted-foreground">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
