import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function Newsletter() {
  return (
    <section className="py-16 bg-primary/10" id="waitlist">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Be the first to build your nest egg</h2>
          <p className="text-xl text-muted-foreground mb-8">
            Join our waitlist to be notified when nesteg<PERSON> launches and start growing your savings effortlessly.
          </p>

          <form className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <Input type="email" placeholder="Your email address" className="flex-grow" required />
            <Button type="submit" className="whitespace-nowrap">
              Notify me when it launches
            </Button>
          </form>

          <p className="text-sm text-muted-foreground mt-4">
            We respect your privacy and will never share your information.
          </p>
        </div>
      </div>
    </section>
  )
}
