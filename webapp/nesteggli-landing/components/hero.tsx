import {But<PERSON>} from "@/components/ui/button"
import Link from "next/link"
import Image from "next/image"

import mobileScreen from "@/assets/mobile-screen.png";

export default function Hero() {
    return (
        <section className="pt-24 pb-16 md:pt-32 md:pb-24">
            <div className="container mx-auto px-4">
                <div className="flex flex-col md:flex-row items-center">
                    <div className="md:w-1/2 space-y-6 text-center md:text-left mb-10 md:mb-0">
                        <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
                            Turn spare change into <span className="text-primary">smart savings.</span>
                        </h1>
                        <p className="text-xl text-muted-foreground">Your nest egg starts with your next coffee.</p>
                        <div className="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                            <Button asChild size="lg" className="text-md">
                                <Link href="#waitlist">Join the Waitlist</Link>
                            </Button>
                            <Button asChild variant="outline" size="lg" className="text-md">
                                <Link href="/app" target="_blank">
                                    Check the Demo
                                </Link>
                            </Button>
                        </div>
                    </div>
                    <div className="md:w-1/2 relative w-full">
                        <div className="relative h-[400px] w-full md:h-[500px]">
                            <div
                                className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-2xl -rotate-3 transform transition-all duration-500 hover:rotate-0"></div>
                            <div className="relative flex items-center justify-center md:absolute md:inset-0">
                                <Image
                                    src={mobileScreen}
                                    alt="nesteggli app on phone"
                                    width={208}
                                    height={384}
                                    className="object-contain drop-shadow-2xl w-52 h-96 md:w-[220px] md:h-[400px]"
                                    priority
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    )
}
