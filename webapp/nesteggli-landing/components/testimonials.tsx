import { <PERSON>, CardContent } from "@/components/ui/card"
import { Quote } from "lucide-react"

const testimonials = [
  {
    quote: "So simple I didn't notice I was saving — and then I was.",
    author: "<PERSON>, 32",
  },
  {
    quote: "<PERSON><PERSON><PERSON> turned coffee into my holiday budget.",
    author: "<PERSON><PERSON>, 38",
  },
  {
    quote: "I've tried other saving apps, but this one actually fits into my life.",
    author: "<PERSON>, 29",
  },
]

export default function Testimonials() {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Early Users Say</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Hear from people who are already growing their nest eggs with us.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="border-none shadow-md">
              <CardContent className="p-6">
                <Quote className="h-8 w-8 text-primary/40 mb-4" />
                <p className="text-lg mb-4 italic">"{testimonial.quote}"</p>
                <p className="font-medium">— {testimonial.author}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
