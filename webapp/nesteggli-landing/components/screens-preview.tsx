import Image from "next/image"
import { Card } from "@/components/ui/card"

import dashboard from "@/assets/1.png"
import goals from "@/assets/2.png"
import investment from "@/assets/3.png"

const screens = [
  {
    title: "Dashboard",
    description: "Track your savings at a glance",
    image: dashboard,
  },
  {
    title: "Goals",
    description: "Set and monitor your saving goals",
    image:  goals,
  },
  {
    title: "Investments",
    description: "Watch your investments grow over time",
    image:  investment,
  },
]

export default function ScreensPreview() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">App Preview</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Take a peek at what nesteggli will look like on your device.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-8">
          {screens.map((screen, index) => (
            <div key={index} className="relative">
              <Card className="flex justify-center overflow-hidden border-none shadow-lg">
                <div className="relative h-[500px] w-[250px]">
                  <Image src={screen.image || "/placeholder.svg"} alt={screen.title} fill className="object-cover" />
                </div>
              </Card>
              <div className="mt-4 text-center">
                <h3 className="font-semibold text-lg">{screen.title}</h3>
                <p className="text-sm text-muted-foreground">{screen.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
