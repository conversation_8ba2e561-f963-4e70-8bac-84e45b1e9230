"use client"

import { Accordion, Accordion<PERSON>ontent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

const faqs = [
  {
    question: "How does rounding up work?",
    answer:
      "When you make a purchase, we round up to the nearest franc or euro and transfer the difference to your nesteggli account. For example, if you spend CHF 3.50 on coffee, we'll round up to CHF 4.00 and save CHF 0.50 for you.",
  },
  {
    question: "Can I pause or change my goal?",
    answer:
      "You can pause your savings at any time, adjust your contribution amounts, or change your saving goals through the app. We understand that flexibility is important.",
  },
  {
    question: "What investment strategies are available?",
    answer:
      "nesteggli offers three main investment strategies: Fixed (low risk, stable returns), Balanced (moderate risk and growth), and Growth (higher risk, potential for greater returns). You can choose the strategy that best matches your risk tolerance and goals.",
  },
  {
    question: "Is this available in Switzerland and Germany?",
    answer:
      "Yes! nesteggli will launch first in Switzerland and Germany, with plans to expand to other European countries in the near future. Stay tuned for announcements about availability in your region.",
  },
  {
    question: "How secure is nesteggli?",
    answer:
      "Security is our top priority. We use bank-level encryption to protect your data and transactions. Your funds are held in segregated accounts, and we comply with all relevant financial regulations in the countries where we operate.",
  },
]

export default function Faq() {
  return (
    <section className="py-16" id="faq">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Find answers to common questions about nesteggli.
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">{faq.question}</AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  )
}
