import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, BarChart3, Globe, TrendingUp, Users } from "lucide-react"
import Link from "next/link"

export default function InvestorsPage() {
  return (
    <div className="container max-w-4xl mx-auto px-4 py-24">
      <h1 className="text-4xl md:text-5xl font-bold text-center mb-8">Investor Relations</h1>

      <div className="space-y-12">
        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Investment Opportunity</h2>
            <p className="text-lg text-muted-foreground mb-6">
              nesteggli is revolutionizing how people save and invest their money through an innovative, user-friendly
              platform that turns everyday transactions into meaningful savings. We're seeking strategic partners who
              share our vision for financial empowerment.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <div className="flex items-start space-x-4">
                <TrendingUp className="h-8 w-8 text-primary" />
                <div>
                  <h3 className="font-semibold text-lg">Market Growth</h3>
                  <p className="text-muted-foreground">
                    Targeting the rapidly expanding micro-savings and investment market across Europe.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <Users className="h-8 w-8 text-primary" />
                <div>
                  <h3 className="font-semibold text-lg">User Acquisition</h3>
                  <p className="text-muted-foreground">
                    Strong early adoption metrics with efficient customer acquisition strategy.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <Globe className="h-8 w-8 text-primary" />
                <div>
                  <h3 className="font-semibold text-lg">Expansion Plans</h3>
                  <p className="text-muted-foreground">
                    Initial launch in Switzerland and Germany with planned expansion across Europe.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-4">
                <BarChart3 className="h-8 w-8 text-primary" />
                <div>
                  <h3 className="font-semibold text-lg">Revenue Model</h3>
                  <p className="text-muted-foreground">
                    Multiple revenue streams including subscription fees and portfolio management.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Current Funding Round</h2>
            <p className="text-lg text-muted-foreground mb-6">
              We're currently in our Series A funding round, seeking strategic investors to help us scale our platform
              and expand our market reach.
            </p>
            <div className="bg-muted p-6 rounded-lg">
              <h3 className="font-semibold text-lg mb-2">Key Investment Details:</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Round: Series A</li>
                <li>• Target: CHF 5 million</li>
                <li>• Use of funds: Product development, market expansion, team growth</li>
                <li>• Minimum investment: CHF 250,000</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Request Investor Deck</h2>
            <p className="text-lg text-muted-foreground mb-6">
              Qualified investors can request our detailed investor deck and financial projections by completing the
              form below.
            </p>
            <form className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-1">
                    Full Name
                  </label>
                  <input
                    id="name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label htmlFor="company" className="block text-sm font-medium mb-1">
                    Company
                  </label>
                  <input
                    id="company"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Your company"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium mb-1">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  placeholder="Your email"
                />
              </div>
              <div>
                <label htmlFor="investment" className="block text-sm font-medium mb-1">
                  Investment Capacity
                </label>
                <select id="investment" className="w-full px-3 py-2 border border-gray-300 rounded-md">
                  <option>Select investment capacity</option>
                  <option>CHF 250,000 - 500,000</option>
                  <option>CHF 500,000 - 1,000,000</option>
                  <option>CHF 1,000,000+</option>
                </select>
              </div>
              <Button type="submit" className="w-full">
                Request Investor Deck
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="text-center">
          <h2 className="text-2xl font-semibold mb-4">Contact Our Investment Team</h2>
          <p className="text-lg text-muted-foreground mb-6">
            For direct inquiries about investment opportunities, please contact our investment relations team.
          </p>
          <Link href="/contact">
            <Button className="inline-flex items-center">
              Contact Us <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
