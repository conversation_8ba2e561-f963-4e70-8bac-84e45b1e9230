import { Card, CardContent } from "@/components/ui/card"

export default function AboutPage() {
  return (
    <div className="container max-w-4xl mx-auto px-4 py-24">
      <h1 className="text-4xl md:text-5xl font-bold text-center mb-8">About nesteggli</h1>

      <div className="space-y-8">
        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Our Story</h2>
            <p className="text-lg text-muted-foreground mb-4">
              We are a group of friends from Zurich who decided to disrupt the Fintech industry in Switzerland by
              helping people across Europe save their money in a smarter way.
            </p>
            <p className="text-lg text-muted-foreground">
              Our journey began when we realized that traditional banking wasn't meeting the needs of modern savers. We
              wanted to create a solution that makes saving effortless, engaging, and rewarding.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Our Mission</h2>
            <p className="text-lg text-muted-foreground">
              At nesteggli, we believe that financial security should be accessible to everyone. Our mission is to
              empower people to build their financial future through simple, automated contributions that fit seamlessly
              into their daily lives.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Our Values</h2>
            <ul className="space-y-4 text-lg text-muted-foreground">
              <li>
                <span className="font-medium text-primary">Simplicity</span> - We make saving money as easy as spending
                it.
              </li>
              <li>
                <span className="font-medium text-primary">Transparency</span> - We believe in clear communication and
                no hidden fees.
              </li>
              <li>
                <span className="font-medium text-primary">Growth</span> - We're committed to helping our users grow
                their wealth over time.
              </li>
              <li>
                <span className="font-medium text-primary">Security</span> - We prioritize the safety and privacy of our
                users' financial data.
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-2xl font-semibold mb-4">Our Team</h2>
            <p className="text-lg text-muted-foreground mb-4">
              Our team brings together expertise in finance, technology, and user experience design. We're passionate
              about creating a product that makes a real difference in people's financial lives.
            </p>
            <p className="text-lg text-muted-foreground">
              Based in Zurich, we're proud of our Swiss heritage and commitment to quality, while maintaining a global
              perspective on financial innovation.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
