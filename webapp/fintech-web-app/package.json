{"name": "fintech-web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test:e2e": "playwright test", "test:e2e:full-config": "playwright test --config=playwright.config.full.ts", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:full": "just test-e2e-full", "test:e2e:simple": "playwright test", "test:e2e:auth0": "playwright test tests/e2e/auth0-real-flow.spec.ts --reporter=list", "test:e2e:quick": "just test-e2e", "dev:start": "just dev-start", "dev:services": "just services-start", "dev:api": "just api-start", "backend:start": "just backend-start", "backend:stop": "just backend-stop", "test:all": "just test-all", "clean": "just clean", "export": "next export"}, "dependencies": {"@auth0/nextjs-auth0": "^4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "express": "^5.1.0", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.513.0", "next": "^13.5.7", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.53.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^13.5.7", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.4.49", "prettier": "^3.6.0", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.4", "typescript": "^5"}}