version: '3.8'

services:
  localauth0:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.2
    ports:
      - "3001:3000"  # Expose on 3001 to avoid conflict with Next.js app
    environment:
      - AUTH0_AUDIENCE=my-api
      - AUTH0_CLIENT_ID=my-client-id
      - AUTH0_CLIENT_SECRET=my-client-secret
      - AUTH0_DOMAIN=localhost:3001
      - AUTH0_ISSUER=http://localhost:3001
      - AUTH0_BASE_URL=http://localhost:3001
      # Configure test user
      - AUTH0_USERS=[{"email":"<EMAIL>","password":"Test1234","user_id":"auth0|123456789","name":"Test User","roles":["user"],"permissions":["read:profile","read:dashboard","read:investments"]}]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fintech-network

  # Optional: Add a database for testing
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=fintech_test
      - POSTGRES_USER=fintech
      - POSTGRES_PASSWORD=fintech123
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - fintech-network

networks:
  fintech-network:
    driver: bridge

volumes:
  postgres_data:
