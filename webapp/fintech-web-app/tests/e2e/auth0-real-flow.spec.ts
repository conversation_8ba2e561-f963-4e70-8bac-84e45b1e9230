import { test, expect } from '@playwright/test';

test.describe('Real Auth0 Authentication E2E Tests', () => {
  test.setTimeout(60000); // Increase timeout for real auth flows

  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);
  });

  test('should complete full Auth0 user registration and onboarding flow', async ({ page }) => {
    // Step 1: Navigate through marketing screens to reach login
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
      timeout: 5000,
    });

    const nextButton = page.locator('button:has-text("Next")');

    // Navigate through all marketing screens (6 screens total, so 5 clicks to reach the last one)
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    // Step 2: Click "Get Started" to go to login screen
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Step 3: Should see login options, click on Auth0 login
    await expect(page.locator('text=Welcome Back')).toBeVisible({ timeout: 5000 });

    const auth0Button = page.locator('button:has-text("Continue with Auth0")');
    await expect(auth0Button).toBeVisible();
    await auth0Button.click();

    // Step 4: Should be redirected to Auth0 mock login page
    await page.waitForTimeout(3000);

    // Check if we're on the Auth0 login page
    await expect(page.url()).toContain('localhost:3002');

    // Step 5: Register a new user
    // Generate unique email for this test run
    const timestamp = Date.now();
    const testEmail = `e2etest${timestamp}@example.com`;
    const testPassword = 'E2ETest1234!';
    const testName = 'E2E Test User';

    // Click on "Need an account? Sign up" link to show registration form
    const signUpLink = page.locator('a:has-text("Need an account? Sign up")');
    await expect(signUpLink).toBeVisible({ timeout: 10000 });
    await signUpLink.click();
    await page.waitForTimeout(1000);

    // Fill in registration form (use the registration form specifically)
    await page.locator('#registerForm input[name="email"]').fill(testEmail);
    await page.locator('#registerForm input[name="password"]').fill(testPassword);
    await page.locator('#registerForm input[name="name"]').fill(testName);

    // Submit registration form
    const signUpButton = page.locator('button[type="submit"]:has-text("Sign Up")');
    await expect(signUpButton).toBeVisible();
    await signUpButton.click();

    // Step 6: Should be redirected back to our app after successful registration
    await page.waitForTimeout(5000);

    // Should be back on our domain
    await expect(page.url()).toContain('localhost:3000');

    // Should see dashboard or authenticated content
    const welcomeText = page.locator('text=Welcome back,, text=Welcome,, text=Dashboard, text=Total Balance');
    await expect(welcomeText.first()).toBeVisible({ timeout: 10000 });

    // Step 7: Test frontend API access with real Auth0 token
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/auth/me');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });

    expect(response.status).toBe(200);
    expect(response.data.authenticated).toBe(true);
    expect(response.data.user.email).toBe(testEmail);
    expect(response.data.user.name).toBe(testName);

    // Step 8: Test logout functionality
    const logoutButton = page.locator('button:has-text("Log Out"), button:has-text("Logout"), a:has-text("Log Out")');
    if (await logoutButton.isVisible()) {
      await logoutButton.click();
      await page.waitForTimeout(3000);

      // Should be back to marketing page
      await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
        timeout: 10000,
      });

      // Verify we're no longer authenticated
      const loggedOutResponse = await page.evaluate(async () => {
        try {
          const res = await fetch('/api/auth/me');
          const data = await res.json();
          return { status: res.status, data };
        } catch (error) {
          return { error: error.message };
        }
      });

      expect(loggedOutResponse.status).toBe(401);
      expect(loggedOutResponse.data.authenticated).toBe(false);
    }
  });

  test('should handle existing user login with Auth0', async ({ page }) => {
    // Step 1: Navigate to login
    const nextButton = page.locator('button:has-text("Next")');
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    const getStartedButton = page.locator('button:has-text("Get Started")');
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Step 2: Click Auth0 login
    const auth0Button = page.locator('button:has-text("Continue with Auth0")');
    await auth0Button.click();
    await page.waitForTimeout(3000);

    // Step 3: Login with pre-configured test user (use the login form specifically)
    await expect(page.locator('form[action="/login"] input[name="email"]')).toBeVisible({ timeout: 10000 });
    await page.locator('form[action="/login"] input[name="email"]').fill('<EMAIL>');
    await page.locator('form[action="/login"] input[name="password"]').fill('Test1234');

    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await submitButton.click();

    // Step 4: Should be redirected back and authenticated
    await page.waitForTimeout(5000);
    await expect(page.url()).toContain('localhost:3000');

    // Should see authenticated content
    const welcomeText = page.locator('text=Welcome back,, text=Welcome,, text=Dashboard, text=Total Balance');
    await expect(welcomeText.first()).toBeVisible({ timeout: 10000 });

    // Step 5: Verify API access
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/auth/me');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });

    expect(response.status).toBe(200);
    expect(response.data.authenticated).toBe(true);
    expect(response.data.user.email).toBe('<EMAIL>');
  });
});
