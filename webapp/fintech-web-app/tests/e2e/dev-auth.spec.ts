import { test, expect } from '@playwright/test';

test.describe('Development Authentication E2E Tests', () => {
  test.setTimeout(30000);

  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);
  });

  test('should complete full development authentication flow', async ({ page }) => {
    // Step 1: Verify we start on marketing page (unauthenticated)
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
      timeout: 5000,
    });

    // Step 2: Navigate through marketing screens to reach login
    const nextButton = page.locator('button:has-text("Next")');

    // Navigate through all marketing screens (6 screens total, so 5 clicks to reach the last one)
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    // Step 3: Click final "Get Started" to go to login screen
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Step 4: Should now be on the development login screen
    await expect(page.locator('text=Welcome Back')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Choose how you\'d like to sign in')).toBeVisible();

    // Step 5: Click on "Use Development Login"
    const devLoginButton = page.locator('button:has-text("Use Development Login")');
    await expect(devLoginButton).toBeVisible();
    await devLoginButton.click();
    await page.waitForTimeout(1000);

    // Step 6: Should see the development login form
    await expect(page.locator('text=Development Login')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();

    // Step 7: Fill in demo credentials using the "Use Demo Credentials" button
    const demoCredentialsButton = page.locator('button:has-text("Use Demo Credentials")');
    await expect(demoCredentialsButton).toBeVisible();
    await demoCredentialsButton.click();
    await page.waitForTimeout(500);

    // Verify the fields are filled
    await expect(page.locator('input[type="email"]')).toHaveValue('<EMAIL>');
    await expect(page.locator('input[type="password"]')).toHaveValue('demo123');

    // Step 8: Submit the login form
    const signInButton = page.locator('button:has-text("Sign In")');
    await expect(signInButton).toBeVisible();
    await signInButton.click();

    // Step 9: Should be redirected to dashboard after successful login
    // Wait for page reload and dashboard content
    await page.waitForTimeout(3000);
    
    // Verify we're now authenticated and seeing dashboard content
    await expect(page.locator('text=Welcome back,')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Total Balance')).toBeVisible();
    await expect(page.locator('h3:has-text("Today\'s Savings")')).toBeVisible();

    // Step 10: Test navigation to profile
    const profileButton = page.locator('button:has-text("Profile")');
    await expect(profileButton).toBeVisible();
    await profileButton.click();
    await page.waitForTimeout(2000);

    // Verify profile page loads
    await expect(page.locator('h1:has-text("Profile")')).toBeVisible();
    await expect(page.locator('text=<EMAIL>').first()).toBeVisible(); // Should show our email

    // Step 11: Test logout functionality
    const logoutButton = page.locator('button:has-text("Log Out")');
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();
    await page.waitForTimeout(3000);

    // Step 12: Should be back to marketing page after logout
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
      timeout: 10000,
    });

    // Verify we're no longer authenticated
    await expect(page.locator('text=Welcome back,')).not.toBeVisible();
  });

  test('should handle registration flow', async ({ page }) => {
    // Navigate to login screen
    const nextButton = page.locator('button:has-text("Next")');
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    const getStartedButton = page.locator('button:has-text("Get Started")');
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Click on development login
    const devLoginButton = page.locator('button:has-text("Use Development Login")');
    await devLoginButton.click();
    await page.waitForTimeout(1000);

    // Click on "Need an account? Create one"
    const createAccountButton = page.locator('button:has-text("Need an account? Create one")');
    await expect(createAccountButton).toBeVisible();
    await createAccountButton.click();
    await page.waitForTimeout(500);

    // Should now show "Create Account" form
    await expect(page.getByText('Create Account').first()).toBeVisible();
    await expect(page.locator('text=Enter any email and password to create a development account')).toBeVisible();

    // Fill in new user credentials
    await page.locator('input[type="email"]').fill('<EMAIL>');
    await page.locator('input[type="password"]').fill('newpass123');

    // Submit registration
    const createButton = page.locator('button:has-text("Create Account")');
    await createButton.click();
    await page.waitForTimeout(3000);

    // Should be logged in and see dashboard
    await expect(page.locator('text=Welcome back,')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Total Balance')).toBeVisible();

    // Check profile shows new user email
    const profileButton = page.locator('button:has-text("Profile")');
    await profileButton.click();
    await page.waitForTimeout(2000);
    await expect(page.locator('text=<EMAIL>').first()).toBeVisible();
  });

  test('should handle Auth0 login option', async ({ page }) => {
    // Navigate to login screen
    const nextButton = page.locator('button:has-text("Next")');
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    const getStartedButton = page.locator('button:has-text("Get Started")');
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Should see both login options
    await expect(page.getByText('Secure Login').first()).toBeVisible();
    await expect(page.locator('text=Login with Auth0 (Production Ready)')).toBeVisible();
    await expect(page.getByText('Development Mode').first()).toBeVisible();
    await expect(page.locator('text=Quick login for local development')).toBeVisible();

    // Auth0 button should be present
    const auth0Button = page.locator('button:has-text("Continue with Auth0")');
    await expect(auth0Button).toBeVisible();
    await expect(auth0Button).toBeEnabled();

    // Note: We don't actually click it in this test since it would redirect to Auth0
    // This test just verifies the UI is present and functional
  });
});
