import { test, expect } from '@playwright/test';

test.describe('FinTech App E2E Tests', () => {
  // Increase timeout for Auth0 WASM app loading
  test.setTimeout(30000);

  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load
    await page.waitForLoadState('domcontentloaded');
    // Wait a bit for any initial renders
    await page.waitForTimeout(1000);
  });

  test('should display marketing page correctly', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Your money works harder for you')).toBeVisible();

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Check navigation buttons are present
    await expect(nextButton).toBeVisible();
    await expect(backButton).toBeVisible();

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(500);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Go back to first screen
    await backButton.click();
    await page.waitForTimeout(500);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content for unauthenticated users
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).not.toBeVisible();
  });

  test('should complete successful login flow', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    await navigateToGetStarted(page);

    // Should now be on the last screen with "Get Started" button
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Mock the authentication endpoints BEFORE clicking login
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user']
        })
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345'
        })
      });
    });

    // Mock the login redirect to simulate Auth0 callback
    await page.route('**/api/auth/login', route => {
      // Simulate successful Auth0 callback by redirecting back to home
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'http://localhost:3000/'
        }
      });
    });

    // Click "Get Started" to initiate login
    await getStartedButton.click();

    // Should be redirected back to the app and show dashboard
    await page.waitForURL('http://localhost:3000/', { timeout: 10000 });

    // Wait for auth check to complete and dashboard to render
    // The homepage automatically switches to dashboard when authenticated
    await expect(page.locator('text=Current Balance')).toBeVisible({ timeout: 15000 });
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();
    await expect(page.locator('text=€12.30')).toBeVisible();
  });

  test('should handle failed login with wrong credentials', async ({ page }) => {
    // Mock the authentication endpoints to simulate failed login
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' })
      });
    });

    // Mock the login redirect to simulate Auth0 error
    await page.route('**/api/auth/login', route => {
      // Simulate failed Auth0 login by redirecting back with error
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'http://localhost:3000/?error=access_denied'
        }
      });
    });

    // Navigate to last marketing screen and click "Get Started"
    await navigateToGetStarted(page);

    // Click "Get Started" to initiate login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();

    // Should be redirected back to the app with error
    await page.waitForURL(/localhost:3000/, { timeout: 10000 });

    // Wait for auth check to complete
    await page.waitForTimeout(2000);

    // Should still show marketing screen (not authenticated)
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });

    // Should NOT show dashboard content
    await expect(page.locator('text=Current Balance')).not.toBeVisible();
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
  });

  test('should handle logout flow correctly', async ({ page }) => {
    // First mock authenticated state
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user']
        })
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345'
        })
      });
    });

    // Navigate to profile page
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(2000);

    // Should show profile content - use more specific selector
    await expect(page.locator('h1:has-text("Profile")')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('h2:has-text("User Information")')).toBeVisible();

    // Mock logout endpoint
    await page.route('**/api/auth/logout', route => {
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'http://localhost:3000/'
        }
      });
    });

    // Mock unauthenticated state after logout
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' })
      });
    });

    // Click logout button
    const logoutButton = page.locator('button:has-text("Logout")');
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();

    // Should be redirected to homepage and show marketing screen
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await page.waitForTimeout(2000);
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content after logout
    await expect(page.locator('h3:has-text("Current Balance")')).not.toBeVisible();
  });

  test('should access dashboard after authentication', async ({ page }) => {
    // Mock authenticated state
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user']
        })
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345'
        })
      });
    });

    // Navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForLoadState('domcontentloaded');

    // Wait for auth check and data loading to complete
    await page.waitForTimeout(3000);

    // Should show dashboard content with more specific selectors
    await expect(page.locator('h1:has-text("Dashboard")')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('h3:has-text("Current Balance")')).toBeVisible();
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('h3:has-text("Today\'s Savings")')).toBeVisible();
    await expect(page.locator('text=€12.30')).toBeVisible();
  });
});

// Helper function to navigate to the last marketing screen
async function navigateToGetStarted(page: any) {
  const nextButton = page.locator('button:has-text("Next")');

  // Navigate through all 5 screens to reach the last one
  for (let i = 0; i < 5; i++) {
    await expect(nextButton).toBeVisible();
    await nextButton.click();
    await page.waitForTimeout(300);
  }
}

// Helper function to authenticate a user using mocked endpoints
async function authenticateUser(page: any) {
  // Mock authenticated state
  await page.route('**/api/auth/me', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        sub: 'auth0|test123',
        email: '<EMAIL>',
        name: 'Test User',
        email_verified: true,
        permissions: ['read:profile', 'read:dashboard', 'read:investments'],
        roles: ['user']
      })
    });
  });

  // Mock the token endpoint
  await page.route('**/api/auth/token', route => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        access_token: 'mock-access-token-12345'
      })
    });
  });

  // Mock the login redirect
  await page.route('**/api/auth/login', route => {
    route.fulfill({
      status: 302,
      headers: {
        'Location': 'http://localhost:3000/'
      }
    });
  });

  // Navigate to homepage and trigger authentication
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(2000);
}
