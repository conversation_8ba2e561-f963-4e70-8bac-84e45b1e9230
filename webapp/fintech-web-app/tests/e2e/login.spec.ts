import { test, expect } from '@playwright/test';

test.describe('Local Auth0 Mock E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load
    await page.waitForLoadState('domcontentloaded');
    // Wait a bit for any initial renders
    await page.waitForTimeout(1000);
  });

  test('should display homepage and marketing elements', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Your money works harder for you')).toBeVisible();

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Check navigation buttons are present
    await expect(nextButton).toBeVisible();
    await expect(backButton).toBeVisible();

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(500);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Go back to first screen
    await backButton.click();
    await page.waitForTimeout(500);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();
  });

  test('should navigate to Get Started and test Auth0 integration', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');

    // Navigate through all 5 screens to reach the last one
    for (let i = 0; i < 5; i++) {
      await expect(nextButton).toBeVisible();
      await nextButton.click();
      await page.waitForTimeout(300);
    }

    // Should now be on the last screen with "Get Started" button
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Mock the Auth0 login route to test the redirect (avoid 302 for WebKit compatibility)
    await page.route('**/api/auth/login', route => {
      // Simulate Auth0 redirect with a simple response instead of 302
      route.fulfill({
        status: 200,
        contentType: 'text/html',
        body: '<html><body>Redirecting to Auth0...</body></html>'
      });
    });

    // Click "Get Started" to initiate Auth0 login
    await getStartedButton.click();

    // Wait for the redirect to be processed
    await page.waitForTimeout(1000);

    // In a real Auth0 integration, user would be redirected to Auth0
    // For testing purposes, we verify the redirect was attempted
    const currentUrl = page.url();

    // The URL should either be the Auth0 mock or the original page (depending on how the mock is handled)
    // This test verifies that the "Get Started" button triggers the Auth0 flow
    expect(currentUrl).toBeDefined();
  });

  test('should handle Auth0 login redirect properly', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(300);
      attempts++;
    }

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Mock Auth0 redirect by intercepting the navigation (avoid 302 for WebKit compatibility)
    await page.route('**/api/auth/login', route => {
      // Simulate Auth0 redirect with a simple response instead of 302
      route.fulfill({
        status: 200,
        contentType: 'text/html',
        body: '<html><body>Redirecting to Auth0...</body></html>'
      });
    });

    await getStartedButton.click();

    // Should be redirected away from the app (to Auth0)
    await page.waitForTimeout(1000);

    // In a real scenario, user would be on Auth0 login page
    // For testing, we verify the redirect was initiated
    const currentUrl = page.url();
    const isRedirectedToAuth = currentUrl.includes('auth0') || currentUrl.includes('login') || currentUrl !== 'http://localhost:3000/';

    expect(isRedirectedToAuth).toBeTruthy();
  });

  test('should show marketing screen for unauthenticated users', async ({ page }) => {
    // Should show marketing screen by default (first screen)
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });

    // Should not show dashboard content
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).not.toBeVisible();

    // Should not show navigation buttons for authenticated users
    await expect(page.locator('button:has-text("Profile")')).not.toBeVisible();
    await expect(page.locator('button:has-text("Cards")')).not.toBeVisible();

    // Should show marketing navigation
    await expect(page.locator('button:has-text("Next")')).toBeVisible();
    await expect(page.locator('button:has-text("Back")')).toBeVisible();
  });

  test('should load profile page without Auth0 protection (temporarily disabled)', async ({ page }) => {
    // Try to directly access profile without logging in
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);

    // Since Auth0 protection is temporarily disabled, the profile page should load
    // In a real Auth0 setup, this would redirect to login
    await expect(page.locator('text=Profile')).toBeVisible({ timeout: 5000 });

    // The page should show the profile content
    const currentUrl = page.url();
    expect(currentUrl).toBe('http://localhost:3000/profile');
  });

  test('should handle navigation between marketing screens', async ({ page }) => {
    // Test marketing screen navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(300);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Navigate back
    await backButton.click();
    await page.waitForTimeout(300);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
  });

  test('should show marketing screen when Auth0 is disabled', async ({ page }) => {
    // Since Auth0 is temporarily disabled, we should always see marketing screen
    // regardless of any localStorage auth state

    // Try to set mock auth state (this won't work since Auth0 is disabled)
    await page.addInitScript(() => {
      window.localStorage.setItem('auth0.user', JSON.stringify({
        sub: 'auth0|123456789',
        email: '<EMAIL>',
        name: 'Test User',
        email_verified: true
      }));
    });

    // Navigate to homepage instead of reload (WebKit compatibility)
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);

    // Should still show marketing screen since Auth0 is disabled
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });

    // Should not show dashboard content
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).not.toBeVisible();

    // Should show marketing navigation
    await expect(page.locator('button:has-text("Next")')).toBeVisible();
    await expect(page.locator('button:has-text("Back")')).toBeVisible();
  });

  test('should test profile page functionality without Auth0', async ({ page }) => {
    // Navigate directly to profile page
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(1000);

    // Profile page should load (Auth0 protection is disabled)
    await expect(page.locator('text=Profile')).toBeVisible({ timeout: 5000 });

    // Check if logout button exists (it should since we're on profile page)
    const logoutButton = page.locator('button:has-text("Logout")');

    // The logout button might be visible even without Auth0 authentication
    if (await logoutButton.isVisible()) {
      // Mock logout redirect
      await page.route('**/api/auth/logout', route => {
        route.fulfill({
          status: 302,
          headers: {
            'Location': 'http://localhost:3000/'
          }
        });
      });

      await logoutButton.click();
      await page.waitForTimeout(1000);

      // Should be redirected or logout initiated
      const currentUrl = page.url();
      expect(currentUrl).toBeDefined();
    } else {
      // If no logout button, just verify we're on the profile page
      expect(page.url()).toBe('http://localhost:3000/profile');
    }
  });

  // New comprehensive tests for local Auth0 integration
  test('should complete full authentication flow with local Auth0', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');

    // Navigate through all 5 screens to reach the last one
    for (let i = 0; i < 5; i++) {
      await expect(nextButton).toBeVisible();
      await nextButton.click();
      await page.waitForTimeout(300);
    }

    // Should now be on the last screen with "Get Started" button
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Click "Get Started" to initiate Auth0 login
    await getStartedButton.click();

    // Should be redirected to local Auth0 mock login page
    await page.waitForURL(/localhost:3001/, { timeout: 10000 });

    // Fill in login credentials
    await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
    await page.fill('input[name="password"], input[type="password"]', 'Test1234');

    // Submit login form
    await page.click('button[type="submit"], input[type="submit"]');

    // Should be redirected back to the app and show dashboard
    await page.waitForURL('http://localhost:3000/', { timeout: 10000 });

    // Should now show dashboard content instead of marketing
    await expect(page.locator('text=Current Balance')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();
  });

  test('should access dashboard with proper permissions', async ({ page }) => {
    // First authenticate
    await authenticateUser(page);

    // Navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForLoadState('domcontentloaded');

    // Should show dashboard content
    await expect(page.locator('text=Dashboard')).toBeVisible();
    await expect(page.locator('text=Current Balance')).toBeVisible();
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();
    await expect(page.locator('text=Recent Transactions')).toBeVisible();
  });

  test('should access profile page and display user info', async ({ page }) => {
    // First authenticate
    await authenticateUser(page);

    // Navigate to profile
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');

    // Should show profile content
    await expect(page.locator('text=Profile')).toBeVisible();
    await expect(page.locator('text=User Information')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    await expect(page.locator('text=Test User')).toBeVisible();
    await expect(page.locator('text=Permissions & Roles')).toBeVisible();

    // Should show permissions
    await expect(page.locator('text=read:profile')).toBeVisible();
    await expect(page.locator('text=read:dashboard')).toBeVisible();
    await expect(page.locator('text=read:investments')).toBeVisible();
  });

  test('should access investments page with portfolio data', async ({ page }) => {
    // First authenticate
    await authenticateUser(page);

    // Navigate to investments
    await page.goto('http://localhost:3000/investments');
    await page.waitForLoadState('domcontentloaded');

    // Should show investments content
    await expect(page.locator('text=Investments')).toBeVisible();
    await expect(page.locator('text=Total Portfolio Value')).toBeVisible();
    await expect(page.locator('text=Your Holdings')).toBeVisible();

    // Should show some stock data
    await expect(page.locator('text=Apple Inc.')).toBeVisible();
    await expect(page.locator('text=AAPL')).toBeVisible();
    await expect(page.locator('text=Microsoft Corporation')).toBeVisible();
  });

  test('should handle logout flow correctly', async ({ page }) => {
    // First authenticate
    await authenticateUser(page);

    // Navigate to profile
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');

    // Click logout button
    const logoutButton = page.locator('button:has-text("Logout")');
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();

    // Should be redirected to homepage and show marketing screen
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content
    await expect(page.locator('text=Current Balance')).not.toBeVisible();
  });

  test('should protect routes when not authenticated', async ({ page }) => {
    // Try to access dashboard without authentication
    await page.goto('http://localhost:3000/dashboard');

    // Should be redirected to homepage (marketing screen)
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Try to access profile without authentication
    await page.goto('http://localhost:3000/profile');

    // Should be redirected to homepage (marketing screen)
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Try to access investments without authentication
    await page.goto('http://localhost:3000/investments');

    // Should be redirected to homepage (marketing screen)
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();
  });

  test('should refresh access token', async ({ page }) => {
    // First authenticate
    await authenticateUser(page);

    // Navigate to profile
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');

    // Should show access token section
    await expect(page.locator('text=Access Token')).toBeVisible();

    // Click refresh token button
    const refreshButton = page.locator('button:has-text("Refresh Token")');
    await expect(refreshButton).toBeVisible();

    // Mock the token refresh response
    await page.route('**/api/auth/token', route => {
      if (route.request().method() === 'POST') {
        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({ access_token: 'new-mock-token-12345' })
        });
      } else {
        route.continue();
      }
    });

    // Handle the alert dialog
    page.on('dialog', dialog => dialog.accept());

    await refreshButton.click();

    // Wait for the refresh to complete
    await page.waitForTimeout(1000);
  });
});

// Helper function to authenticate a user
async function authenticateUser(page: any) {
  // Navigate to homepage
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('domcontentloaded');

  // Navigate to last marketing screen
  const nextButton = page.locator('button:has-text("Next")');
  for (let i = 0; i < 5; i++) {
    await nextButton.click();
    await page.waitForTimeout(300);
  }

  // Click "Get Started"
  const getStartedButton = page.locator('button:has-text("Get Started")');
  await getStartedButton.click();

  // Wait for redirect to Auth0 mock
  await page.waitForURL(/localhost:3001/, { timeout: 10000 });

  // Fill in credentials
  await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
  await page.fill('input[name="password"], input[type="password"]', 'Test1234');

  // Submit form
  await page.click('button[type="submit"], input[type="submit"]');

  // Wait for redirect back to app
  await page.waitForURL('http://localhost:3000/', { timeout: 10000 });
  await page.waitForTimeout(1000);
}
