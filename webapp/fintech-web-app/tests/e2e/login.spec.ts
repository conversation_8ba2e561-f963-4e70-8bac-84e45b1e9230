import { test, expect } from '@playwright/test';

test.describe('Auth0 Login Flow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load
    await page.waitForLoadState('networkidle');
  });

  test('should display homepage and marketing elements', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Get Started')).toBeVisible({ timeout: 5000 });

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Navigate through marketing screens
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(500);

      // Check if back button is now enabled
      await expect(backButton).toBeEnabled();

      // Go back to first screen
      await backButton.click();
      await page.waitForTimeout(500);
    }

    // Navigate to the last marketing screen to find "Get Started"
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(500);
      attempts++;
    }

    // Should now see "Get Started" button
    await expect(page.locator('button:has-text("Get Started")')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(300);
      attempts++;
    }

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();

    // Wait for Auth0 redirect or login page
    await page.waitForURL(/auth0|login/, { timeout: 5000 });

    // For mocked Auth0, we expect to be redirected to Auth0 login
    // In a real test, you would fill in Auth0 credentials here
    // For now, we'll simulate successful login by going directly to callback

    // Mock successful Auth0 callback
    await page.goto('http://localhost:3000/api/auth/callback?code=mock-code&state=mock-state');

    // Wait for redirect to dashboard
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await page.waitForLoadState('networkidle');

    // Should now be on dashboard (authenticated state)
    // Check for dashboard elements
    await expect(page.locator('text=€1,847.50, text=Balance, text=Today\'s Savings')).toBeVisible({ timeout: 5000 });

    // Check for logout functionality in profile
    const profileButton = page.locator('button:has-text("Profile")');
    if (await profileButton.isVisible()) {
      await profileButton.click();
      await page.waitForTimeout(500);
      await expect(page.locator('button:has-text("Logout")')).toBeVisible();
    }
  });

  test('should handle Auth0 login redirect properly', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(300);
      attempts++;
    }

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Mock Auth0 redirect by intercepting the navigation
    await page.route('**/api/auth/login', route => {
      // Simulate Auth0 redirect
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'https://mock-auth0.com/login?redirect_uri=http://localhost:3000/api/auth/callback'
        }
      });
    });

    await getStartedButton.click();

    // Should be redirected away from the app (to Auth0)
    await page.waitForTimeout(1000);

    // In a real scenario, user would be on Auth0 login page
    // For testing, we verify the redirect was initiated
    const currentUrl = page.url();
    const isRedirectedToAuth = currentUrl.includes('auth0') || currentUrl.includes('login') || currentUrl !== 'http://localhost:3000/';

    expect(isRedirectedToAuth).toBeTruthy();
  });

  test('should fail login with blank credentials', async ({ page }) => {
    // Click login button
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    // Wait for login form
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Leave fields blank and try to submit
    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for validation
    await page.waitForTimeout(1000);

    // Should still be on login page
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Check for validation errors
    const validationError = page.locator('[data-testid="validation-error"], .error, :text("required"), :text("Please"), :text("field")').first();
    await expect(validationError).toBeVisible({ timeout: 3000 });
  });

  test('should protect dashboard from unauthenticated access', async ({ page }) => {
    // Try to directly access protected route without logging in
    await page.goto('http://localhost:3000/dashboard');

    // Should be redirected to login or homepage
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const isProtected = !currentUrl.includes('dashboard') || 
                       currentUrl.includes('login') || 
                       currentUrl === 'http://localhost:3000/';
    
    expect(isProtected).toBeTruthy();

    // Should not see protected content
    const protectedContent = page.locator('[data-testid="dashboard-content"], :text("Welcome"), :text("Your account")');
    await expect(protectedContent).not.toBeVisible();
  });

  test('should protect profile page from unauthenticated access', async ({ page }) => {
    // Try to directly access profile without logging in
    await page.goto('http://localhost:3000/profile');

    // Should be redirected or blocked
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const isProtected = !currentUrl.includes('profile') || 
                       currentUrl.includes('login') || 
                       currentUrl === 'http://localhost:3000/';
    
    expect(isProtected).toBeTruthy();
  });

  test('should maintain authentication state across page navigation', async ({ page }) => {
    // First login with valid credentials
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('correct-password');

    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for successful login
    await page.waitForURL(/dashboard|profile|app/, { timeout: 10000 });

    // Navigate to different pages and ensure auth state is maintained
    await page.goto('http://localhost:3000/profile');
    await page.waitForTimeout(1000);
    
    // Should still be authenticated
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout")').first();
    const userMenu = page.locator('[data-testid="user-menu"]').first();
    
    await expect(logoutButton.or(userMenu)).toBeVisible();

    // Navigate back to homepage
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(1000);

    // Should still show authenticated state
    await expect(logoutButton.or(userMenu)).toBeVisible();
  });

  test('should successfully logout and clear authentication state', async ({ page }) => {
    // First login
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('correct-password');

    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for successful login
    await page.waitForURL(/dashboard|profile|app/, { timeout: 10000 });

    // Find and click logout button
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout"), button:has-text("Sign out")').first();
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();

    // Wait for logout to complete
    await page.waitForTimeout(2000);

    // Should be redirected to homepage or login
    const currentUrl = page.url();
    const isLoggedOut = currentUrl === 'http://localhost:3000/' || currentUrl.includes('login');
    expect(isLoggedOut).toBeTruthy();

    // Should not see authenticated elements
    await expect(logoutButton).not.toBeVisible();
    
    // Login button should be visible again
    const newLoginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await expect(newLoginButton).toBeVisible();

    // Try to access protected route - should be blocked
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForTimeout(2000);
    
    const finalUrl = page.url();
    const isStillProtected = !finalUrl.includes('dashboard') || 
                            finalUrl.includes('login') || 
                            finalUrl === 'http://localhost:3000/';
    expect(isStillProtected).toBeTruthy();
  });
});
