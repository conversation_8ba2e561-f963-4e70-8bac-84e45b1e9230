import { test, expect } from '@playwright/test';

test.describe('FinTech App E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load
    await page.waitForLoadState('domcontentloaded');
    // Wait a bit for any initial renders
    await page.waitForTimeout(1000);
  });

  test('should display marketing page correctly', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Your money works harder for you')).toBeVisible();

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Check navigation buttons are present
    await expect(nextButton).toBeVisible();
    await expect(backButton).toBeVisible();

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(500);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Go back to first screen
    await backButton.click();
    await page.waitForTimeout(500);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content for unauthenticated users
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).not.toBeVisible();
  });

  test('should complete successful login flow', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    await navigateToGetStarted(page);

    // Should now be on the last screen with "Get Started" button
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Click "Get Started" to initiate Auth0 login
    await getStartedButton.click();

    // Should be redirected to local Auth0 mock login page
    await page.waitForURL(/localhost:3001/, { timeout: 10000 });

    // Wait for the Auth0 mock to fully load (it's a WASM app)
    await page.waitForTimeout(3000);

    // Wait for login form to be available and fill in correct credentials
    await page.waitForSelector('input[type="email"], input[name="email"], input[placeholder*="email" i]', { timeout: 10000 });
    await page.fill('input[type="email"], input[name="email"], input[placeholder*="email" i]', '<EMAIL>');

    await page.waitForSelector('input[type="password"], input[name="password"], input[placeholder*="password" i]', { timeout: 5000 });
    await page.fill('input[type="password"], input[name="password"], input[placeholder*="password" i]', 'Test1234');

    // Submit login form
    await page.click('button[type="submit"], input[type="submit"], button:has-text("Login"), button:has-text("Sign In")');

    // Should be redirected back to the app and show dashboard
    await page.waitForURL('http://localhost:3000/', { timeout: 10000 });

    // Should now show dashboard content instead of marketing
    await expect(page.locator('text=Current Balance')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();
  });

  test('should handle failed login with wrong credentials', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    await navigateToGetStarted(page);

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();

    // Should be redirected to local Auth0 mock login page
    await page.waitForURL(/localhost:3001/, { timeout: 10000 });

    // Fill in wrong login credentials
    await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
    await page.fill('input[name="password"], input[type="password"]', 'WrongPassword');

    // Submit login form
    await page.click('button[type="submit"], input[type="submit"]');

    // Should show error message or stay on login page
    // Wait a bit for the response
    await page.waitForTimeout(2000);

    // Should either show error message or stay on Auth0 login page
    const currentUrl = page.url();
    const isStillOnAuth0 = currentUrl.includes('localhost:3001');
    const hasErrorMessage = await page.locator('text=Invalid credentials, text=Login failed, text=Error').isVisible();

    // Either should stay on Auth0 page or show error
    expect(isStillOnAuth0 || hasErrorMessage).toBeTruthy();

    // Should NOT be redirected back to app dashboard
    if (!isStillOnAuth0) {
      await expect(page.locator('text=Current Balance')).not.toBeVisible();
    }
  });

  test('should handle logout flow correctly', async ({ page }) => {
    // First authenticate user
    await authenticateUser(page);

    // Navigate to profile page
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('domcontentloaded');

    // Should show profile content
    await expect(page.locator('text=Profile')).toBeVisible({ timeout: 5000 });

    // Click logout button
    const logoutButton = page.locator('button:has-text("Logout")');
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();

    // Should be redirected to homepage and show marketing screen
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content after logout
    await expect(page.locator('text=Current Balance')).not.toBeVisible();
  });

  test('should access dashboard after authentication', async ({ page }) => {
    // First authenticate user
    await authenticateUser(page);

    // Navigate to dashboard
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForLoadState('domcontentloaded');

    // Should show dashboard content
    await expect(page.locator('text=Dashboard')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Current Balance')).toBeVisible();
    await expect(page.locator('text=€1,847.50')).toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();
  });
});

// Helper function to navigate to the last marketing screen
async function navigateToGetStarted(page: any) {
  const nextButton = page.locator('button:has-text("Next")');

  // Navigate through all 5 screens to reach the last one
  for (let i = 0; i < 5; i++) {
    await expect(nextButton).toBeVisible();
    await nextButton.click();
    await page.waitForTimeout(300);
  }
}

// Helper function to authenticate a user
async function authenticateUser(page: any) {
  // Navigate to homepage
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('domcontentloaded');

  // Navigate to last marketing screen
  await navigateToGetStarted(page);

  // Click "Get Started"
  const getStartedButton = page.locator('button:has-text("Get Started")');
  await getStartedButton.click();

  // Wait for redirect to Auth0 mock
  await page.waitForURL(/localhost:3001/, { timeout: 10000 });

  // Fill in credentials
  await page.fill('input[name="email"], input[type="email"]', '<EMAIL>');
  await page.fill('input[name="password"], input[type="password"]', 'Test1234');

  // Submit form
  await page.click('button[type="submit"], input[type="submit"]');

  // Wait for redirect back to app
  await page.waitForURL('http://localhost:3000/', { timeout: 10000 });
  await page.waitForTimeout(1000);
}
