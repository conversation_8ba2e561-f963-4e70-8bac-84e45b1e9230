import { test, expect } from '@playwright/test';

test.describe('Auth0 Login Flow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load (use domcontentloaded instead of networkidle)
    await page.waitForLoadState('domcontentloaded');
    // Wait a bit for any initial renders
    await page.waitForTimeout(1000);
  });

  test('should display homepage and marketing elements', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Get Started')).toBeVisible({ timeout: 5000 });

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Navigate through marketing screens
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(500);

      // Check if back button is now enabled
      await expect(backButton).toBeEnabled();

      // Go back to first screen
      await backButton.click();
      await page.waitForTimeout(500);
    }

    // Navigate to the last marketing screen to find "Get Started"
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(500);
      attempts++;
    }

    // Should now see "Get Started" button
    await expect(page.locator('button:has-text("Get Started")')).toBeVisible();
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(300);
      attempts++;
    }

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();

    // Wait for Auth0 redirect or login page
    await page.waitForURL(/auth0|login/, { timeout: 5000 });

    // For mocked Auth0, we expect to be redirected to Auth0 login
    // In a real test, you would fill in Auth0 credentials here
    // For now, we'll simulate successful login by going directly to callback

    // Mock successful Auth0 callback
    await page.goto('http://localhost:3000/api/auth/callback?code=mock-code&state=mock-state');

    // Wait for redirect to dashboard
    await page.waitForURL('http://localhost:3000/', { timeout: 5000 });
    await page.waitForLoadState('networkidle');

    // Should now be on dashboard (authenticated state)
    // Check for dashboard elements
    await expect(page.locator('text=€1,847.50, text=Balance, text=Today\'s Savings')).toBeVisible({ timeout: 5000 });

    // Check for logout functionality in profile
    const profileButton = page.locator('button:has-text("Profile")');
    if (await profileButton.isVisible()) {
      await profileButton.click();
      await page.waitForTimeout(500);
      await expect(page.locator('button:has-text("Logout")')).toBeVisible();
    }
  });

  test('should handle Auth0 login redirect properly', async ({ page }) => {
    // Navigate to last marketing screen and click "Get Started"
    const nextButton = page.locator('button:has-text("Next")');
    let attempts = 0;
    while (attempts < 5 && await nextButton.isVisible()) {
      await nextButton.click();
      await page.waitForTimeout(300);
      attempts++;
    }

    // Click "Get Started" to initiate Auth0 login
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();

    // Mock Auth0 redirect by intercepting the navigation
    await page.route('**/api/auth/login', route => {
      // Simulate Auth0 redirect
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'https://mock-auth0.com/login?redirect_uri=http://localhost:3000/api/auth/callback'
        }
      });
    });

    await getStartedButton.click();

    // Should be redirected away from the app (to Auth0)
    await page.waitForTimeout(1000);

    // In a real scenario, user would be on Auth0 login page
    // For testing, we verify the redirect was initiated
    const currentUrl = page.url();
    const isRedirectedToAuth = currentUrl.includes('auth0') || currentUrl.includes('login') || currentUrl !== 'http://localhost:3000/';

    expect(isRedirectedToAuth).toBeTruthy();
  });

  test('should show marketing screen for unauthenticated users', async ({ page }) => {
    // Should show marketing screen by default
    await expect(page.locator('text=Get Started')).toBeVisible({ timeout: 5000 });

    // Should not show dashboard content
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator('text=Today\'s Savings')).not.toBeVisible();

    // Should not show navigation buttons for authenticated users
    await expect(page.locator('button:has-text("Profile")')).not.toBeVisible();
    await expect(page.locator('button:has-text("Cards")')).not.toBeVisible();
  });

  test('should protect profile route from unauthenticated access', async ({ page }) => {
    // Try to directly access profile without logging in
    await page.goto('http://localhost:3000/profile');
    await page.waitForLoadState('networkidle');

    // Should be redirected to Auth0 login
    await page.waitForTimeout(1000);

    const currentUrl = page.url();
    const isRedirectedToAuth = currentUrl.includes('auth0') || currentUrl.includes('login') || currentUrl !== 'http://localhost:3000/profile';

    expect(isRedirectedToAuth).toBeTruthy();
  });

  test('should handle navigation between marketing screens', async ({ page }) => {
    // Test marketing screen navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(300);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Navigate back
    await backButton.click();
    await page.waitForTimeout(300);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
  });

  test('should simulate successful Auth0 authentication flow', async ({ page }) => {
    // Mock successful authentication by setting Auth0 session
    await page.addInitScript(() => {
      // Mock Auth0 user in localStorage or sessionStorage
      window.localStorage.setItem('auth0.user', JSON.stringify({
        sub: 'auth0|123456789',
        email: '<EMAIL>',
        name: 'Test User',
        email_verified: true
      }));
    });

    // Reload page to pick up the mocked auth state
    await page.reload();
    await page.waitForLoadState('networkidle');

    // Should now show dashboard instead of marketing screen
    await expect(page.locator('text=€1,847.50')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Today\'s Savings')).toBeVisible();

    // Should show navigation for authenticated users
    await expect(page.locator('button:has-text("Profile")')).toBeVisible();
    await expect(page.locator('button:has-text("Cards")')).toBeVisible();

    // Should not show marketing screen
    await expect(page.locator('text=Get Started')).not.toBeVisible();
  });

  test('should handle logout flow correctly', async ({ page }) => {
    // Mock authenticated state
    await page.addInitScript(() => {
      window.localStorage.setItem('auth0.user', JSON.stringify({
        sub: 'auth0|123456789',
        email: '<EMAIL>',
        name: 'Test User'
      }));
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // Navigate to profile
    const profileButton = page.locator('button:has-text("Profile")');
    await expect(profileButton).toBeVisible();
    await profileButton.click();
    await page.waitForTimeout(500);

    // Find and click logout button
    const logoutButton = page.locator('button:has-text("Logout")');
    await expect(logoutButton).toBeVisible();

    // Mock logout redirect
    await page.route('**/api/auth/logout', route => {
      route.fulfill({
        status: 302,
        headers: {
          'Location': 'http://localhost:3000/'
        }
      });
    });

    await logoutButton.click();

    // Should be redirected to Auth0 logout
    await page.waitForTimeout(1000);

    // Verify logout was initiated
    const currentUrl = page.url();
    const isLogoutInitiated = currentUrl.includes('logout') || currentUrl === 'http://localhost:3000/';
    expect(isLogoutInitiated).toBeTruthy();
  });
});
