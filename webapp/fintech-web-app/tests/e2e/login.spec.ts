import { test, expect } from '@playwright/test';

test.describe('FinTech App E2E Tests', () => {
  // Increase timeout for Auth0 WASM app loading
  test.setTimeout(30000);

  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
    // Wait for page to load
    await page.waitForLoadState('domcontentloaded');
    // Wait a bit for any initial renders
    await page.waitForTimeout(1000);
  });

  test('should display marketing page correctly', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);

    // Check for marketing screen elements
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
      timeout: 5000,
    });
    await expect(page.locator('text=Your money works harder for you')).toBeVisible();

    // Test marketing flow navigation
    const nextButton = page.locator('button:has-text("Next")');
    const backButton = page.locator('button:has-text("Back")');

    // Check navigation buttons are present
    await expect(nextButton).toBeVisible();
    await expect(backButton).toBeVisible();

    // Initially back button should be disabled
    await expect(backButton).toBeDisabled();

    // Navigate forward
    await nextButton.click();
    await page.waitForTimeout(500);

    // Back button should now be enabled
    await expect(backButton).toBeEnabled();

    // Go back to first screen
    await backButton.click();
    await page.waitForTimeout(500);

    // Should be back to first screen
    await expect(backButton).toBeDisabled();
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible();

    // Should not show dashboard content for unauthenticated users
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
    await expect(page.locator("text=Today's Savings")).not.toBeVisible();
  });

  test('should show dashboard when authenticated', async ({ page }) => {
    // Mock the authentication endpoints
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user'],
        }),
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345',
        }),
      });
    });

    // Navigate to homepage first, then dashboard content should appear when authenticated
    await page.goto('http://localhost:3000/');
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(3000);

    // Should show dashboard content (homepage when authenticated)
    await expect(page.locator('text=Welcome back,')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=Total Balance')).toBeVisible();
    await expect(page.locator('h3:has-text("Today\'s Savings")')).toBeVisible();
    await expect(page.locator('text=Daily Savings Feed')).toBeVisible();
  });

  test('should handle failed login with wrong credentials', async ({ page }) => {
    // Mock the authentication endpoints to simulate failed login
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Unauthorized' }),
      });
    });

    // Navigate to homepage first (which should show marketing when not authenticated)
    await page.goto('http://localhost:3000/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);

    // Should show marketing screen (not authenticated)
    await expect(page.locator('text=Save effortlessly as you spend')).toBeVisible({
      timeout: 5000,
    });

    // Should NOT show dashboard content
    await expect(page.locator('h3:has-text("Current Balance")')).not.toBeVisible();
    await expect(page.locator('text=€1,847.50')).not.toBeVisible();
  });

  test('should show logout button when authenticated', async ({ page }) => {
    // Mock authenticated state
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user'],
        }),
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345',
        }),
      });
    });

    // Navigate to homepage first, then to profile via navigation
    await page.goto('http://localhost:3000/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);

    // Click on profile navigation button
    await page.locator('button:has-text("Profile")').click();
    await page.waitForTimeout(2000);

    // Should show profile content
    await expect(page.locator('h1:has-text("Profile")')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=Account Summary')).toBeVisible();

    // Verify logout button is present and clickable
    const logoutButton = page.locator('button:has-text("Log Out")');
    await expect(logoutButton).toBeVisible();
    await expect(logoutButton).toBeEnabled();

    // Verify the button has the correct text
    await expect(logoutButton).toContainText('Log Out');
  });

  test('should access investments page after authentication', async ({ page }) => {
    // Mock authenticated state
    await page.route('**/api/auth/me', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          sub: 'auth0|test123',
          email: '<EMAIL>',
          name: 'Test User',
          email_verified: true,
          permissions: ['read:profile', 'read:dashboard', 'read:investments'],
          roles: ['user'],
        }),
      });
    });

    // Mock the token endpoint
    await page.route('**/api/auth/token', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          access_token: 'mock-access-token-12345',
        }),
      });
    });

    // Navigate to homepage first, then to investments via navigation
    await page.goto('http://localhost:3000/', { waitUntil: 'domcontentloaded' });
    await page.waitForTimeout(3000);

    // Click on investments navigation button
    await page.locator('button:has-text("Investments")').click();
    await page.waitForTimeout(2000);

    // Should show investments content
    await expect(page.locator('h1:has-text("Investments")')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('h3:has-text("Total Portfolio Value")')).toBeVisible();
    await expect(page.locator('text=€15,420.50')).toBeVisible();
    await expect(page.locator('h3:has-text("Total Gain/Loss")')).toBeVisible();
    await expect(page.locator('text=€1,250.30')).toBeVisible();
  });
});

// Helper function to navigate to the last marketing screen
async function navigateToGetStarted(page: any) {
  const nextButton = page.locator('button:has-text("Next")');

  // Navigate through all 5 screens to reach the last one
  for (let i = 0; i < 5; i++) {
    await expect(nextButton).toBeVisible();
    await nextButton.click();
    await page.waitForTimeout(300);
  }
}

// Helper function to authenticate a user using mocked endpoints
async function authenticateUser(page: any) {
  // Mock authenticated state
  await page.route('**/api/auth/me', (route: any) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        sub: 'auth0|test123',
        email: '<EMAIL>',
        name: 'Test User',
        email_verified: true,
        permissions: ['read:profile', 'read:dashboard', 'read:investments'],
        roles: ['user'],
      }),
    });
  });

  // Mock the token endpoint
  await page.route('**/api/auth/token', (route: any) => {
    route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        access_token: 'mock-access-token-12345',
      }),
    });
  });

  // Navigate to homepage with mocked auth
  await page.goto('http://localhost:3000');
  await page.waitForLoadState('domcontentloaded');
  await page.waitForTimeout(3000);
}
