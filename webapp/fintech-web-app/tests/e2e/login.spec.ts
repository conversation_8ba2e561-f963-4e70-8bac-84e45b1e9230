import { test, expect } from '@playwright/test';

test.describe('Auth0 Login Flow E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the homepage before each test
    await page.goto('http://localhost:3000');
  });

  test('should display homepage and marketing elements', async ({ page }) => {
    // Assert homepage loads correctly
    await expect(page).toHaveTitle(/fintech/i);
    
    // Check for marketing elements - adjust selectors based on your app structure
    const marketingSection = page.locator('[data-testid="marketing-section"]').first();
    if (await marketingSection.isVisible()) {
      await expect(marketingSection).toBeVisible();
      
      // Scroll through teaser cards if they exist
      const teaserCards = page.locator('[data-testid="teaser-card"]');
      const cardCount = await teaserCards.count();
      if (cardCount > 0) {
        for (let i = 0; i < Math.min(cardCount, 3); i++) {
          await teaserCards.nth(i).scrollIntoViewIfNeeded();
          await expect(teaserCards.nth(i)).toBeVisible();
        }
      }
      
      // Click "Learn more" or similar links if present
      const learnMoreButton = page.locator('button:has-text("Learn more"), a:has-text("Learn more")').first();
      if (await learnMoreButton.isVisible()) {
        await learnMoreButton.click();
        // Wait for any animations or content changes
        await page.waitForTimeout(1000);
      }
    }
  });

  test('should successfully login with valid credentials', async ({ page }) => {
    // Find and click the login button
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await expect(loginButton).toBeVisible();
    await loginButton.click();

    // Wait for login form to appear
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Fill in valid credentials
    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('correct-password');

    // Submit the form
    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for redirect to dashboard/protected area
    await page.waitForURL(/dashboard|profile|app/, { timeout: 10000 });

    // Assert user is on protected page
    await expect(page).toHaveURL(/dashboard|profile|app/);

    // Check for welcome message or protected content
    const welcomeMessage = page.locator('[data-testid="welcome-message"], :text("Welcome"), :text("Dashboard"), :text("Your account")').first();
    await expect(welcomeMessage).toBeVisible({ timeout: 5000 });

    // Verify user is authenticated by checking for logout button or user menu
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout"), button:has-text("Sign out")').first();
    const userMenu = page.locator('[data-testid="user-menu"], [data-testid="user-profile"]').first();
    
    // At least one of these should be visible when authenticated
    await expect(logoutButton.or(userMenu)).toBeVisible();
  });

  test('should fail login with invalid credentials', async ({ page }) => {
    // Click login button
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    // Wait for login form
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Try invalid credentials
    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('wrong-password');

    // Submit the form
    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait a moment for any error processing
    await page.waitForTimeout(2000);

    // Assert that login failed - user should still be on login page or see error
    const currentUrl = page.url();
    const isStillOnLogin = currentUrl.includes('login') || currentUrl === 'http://localhost:3000/';
    
    if (isStillOnLogin) {
      // Check for error message
      const errorMessage = page.locator('[data-testid="error-message"], .error, :text("Invalid"), :text("incorrect"), :text("failed")').first();
      await expect(errorMessage).toBeVisible({ timeout: 3000 });
    } else {
      // If redirected, ensure it's not to a protected area
      await expect(page).not.toHaveURL(/dashboard|profile|app/);
    }

    // Ensure no authenticated elements are visible
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout")');
    const userMenu = page.locator('[data-testid="user-menu"]');
    
    await expect(logoutButton).not.toBeVisible();
    await expect(userMenu).not.toBeVisible();
  });

  test('should fail login with blank credentials', async ({ page }) => {
    // Click login button
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    // Wait for login form
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Leave fields blank and try to submit
    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for validation
    await page.waitForTimeout(1000);

    // Should still be on login page
    await expect(page.locator('[data-testid="login-form"], form')).toBeVisible();

    // Check for validation errors
    const validationError = page.locator('[data-testid="validation-error"], .error, :text("required"), :text("Please"), :text("field")').first();
    await expect(validationError).toBeVisible({ timeout: 3000 });
  });

  test('should protect dashboard from unauthenticated access', async ({ page }) => {
    // Try to directly access protected route without logging in
    await page.goto('http://localhost:3000/dashboard');

    // Should be redirected to login or homepage
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const isProtected = !currentUrl.includes('dashboard') || 
                       currentUrl.includes('login') || 
                       currentUrl === 'http://localhost:3000/';
    
    expect(isProtected).toBeTruthy();

    // Should not see protected content
    const protectedContent = page.locator('[data-testid="dashboard-content"], :text("Welcome"), :text("Your account")');
    await expect(protectedContent).not.toBeVisible();
  });

  test('should protect profile page from unauthenticated access', async ({ page }) => {
    // Try to directly access profile without logging in
    await page.goto('http://localhost:3000/profile');

    // Should be redirected or blocked
    await page.waitForTimeout(2000);
    
    const currentUrl = page.url();
    const isProtected = !currentUrl.includes('profile') || 
                       currentUrl.includes('login') || 
                       currentUrl === 'http://localhost:3000/';
    
    expect(isProtected).toBeTruthy();
  });

  test('should maintain authentication state across page navigation', async ({ page }) => {
    // First login with valid credentials
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('correct-password');

    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for successful login
    await page.waitForURL(/dashboard|profile|app/, { timeout: 10000 });

    // Navigate to different pages and ensure auth state is maintained
    await page.goto('http://localhost:3000/profile');
    await page.waitForTimeout(1000);
    
    // Should still be authenticated
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout")').first();
    const userMenu = page.locator('[data-testid="user-menu"]').first();
    
    await expect(logoutButton.or(userMenu)).toBeVisible();

    // Navigate back to homepage
    await page.goto('http://localhost:3000/');
    await page.waitForTimeout(1000);

    // Should still show authenticated state
    await expect(logoutButton.or(userMenu)).toBeVisible();
  });

  test('should successfully logout and clear authentication state', async ({ page }) => {
    // First login
    const loginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await loginButton.click();

    const emailInput = page.locator('[data-testid="email-input"], input[type="email"], input[name="email"]');
    const passwordInput = page.locator('[data-testid="password-input"], input[type="password"], input[name="password"]');
    
    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('correct-password');

    const submitButton = page.locator('[data-testid="submit-button"], button[type="submit"], button:has-text("Sign in"), button:has-text("Login")');
    await submitButton.click();

    // Wait for successful login
    await page.waitForURL(/dashboard|profile|app/, { timeout: 10000 });

    // Find and click logout button
    const logoutButton = page.locator('[data-testid="logout-button"], button:has-text("Logout"), button:has-text("Sign out")').first();
    await expect(logoutButton).toBeVisible();
    await logoutButton.click();

    // Wait for logout to complete
    await page.waitForTimeout(2000);

    // Should be redirected to homepage or login
    const currentUrl = page.url();
    const isLoggedOut = currentUrl === 'http://localhost:3000/' || currentUrl.includes('login');
    expect(isLoggedOut).toBeTruthy();

    // Should not see authenticated elements
    await expect(logoutButton).not.toBeVisible();
    
    // Login button should be visible again
    const newLoginButton = page.locator('[data-testid="login-button"], button:has-text("Login"), a:has-text("Login")').first();
    await expect(newLoginButton).toBeVisible();

    // Try to access protected route - should be blocked
    await page.goto('http://localhost:3000/dashboard');
    await page.waitForTimeout(2000);
    
    const finalUrl = page.url();
    const isStillProtected = !finalUrl.includes('dashboard') || 
                            finalUrl.includes('login') || 
                            finalUrl === 'http://localhost:3000/';
    expect(isStillProtected).toBeTruthy();
  });
});
