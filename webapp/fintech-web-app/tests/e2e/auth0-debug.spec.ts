import { test, expect } from '@playwright/test';

test.describe('Auth0 Debug Tests', () => {
  test.setTimeout(30000);

  test('should redirect to Auth0 mock server', async ({ page }) => {
    // Navigate to the homepage
    await page.goto('http://localhost:3000');
    await page.waitForLoadState('domcontentloaded');
    
    // Navigate through marketing screens to reach login
    const nextButton = page.locator('button:has-text("Next")');
    
    // Navigate through all marketing screens (6 screens total, so 5 clicks to reach the last one)
    for (let i = 0; i < 5; i++) {
      await nextButton.click();
      await page.waitForTimeout(500);
    }

    // Click "Get Started" to go to login screen
    const getStartedButton = page.locator('button:has-text("Get Started")');
    await expect(getStartedButton).toBeVisible();
    await getStartedButton.click();
    await page.waitForTimeout(2000);

    // Should see login options
    await expect(page.locator('text=Welcome Back')).toBeVisible({ timeout: 5000 });
    
    // Click on Auth0 login
    const auth0Button = page.locator('button:has-text("Continue with Auth0")');
    await expect(auth0Button).toBeVisible();
    
    console.log('Current URL before Auth0 click:', page.url());
    
    // Click and wait for navigation
    await Promise.all([
      page.waitForNavigation({ timeout: 10000 }),
      auth0Button.click()
    ]);
    
    console.log('Current URL after Auth0 click:', page.url());
    
    // Should be redirected to Auth0 mock server
    await expect(page.url()).toContain('localhost:3002');
    
    // Should see the Auth0 login form
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('input[name="email"]').first()).toBeVisible();
    await expect(page.locator('input[name="password"]').first()).toBeVisible();
    
    console.log('Auth0 login form is visible!');
  });

  test('should complete Auth0 login flow', async ({ page }) => {
    // Go directly to Auth0 mock server
    await page.goto('http://localhost:3002/authorize?client_id=my-client-id&response_type=code&redirect_uri=http://localhost:3000/api/auth/callback&scope=openid%20profile%20email&state=test');
    
    // Should see the Auth0 login form
    await expect(page.locator('h2:has-text("Sign In")')).toBeVisible({ timeout: 5000 });
    
    // Fill in login form with existing user
    await page.locator('input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[name="password"]').first().fill('Test1234');
    
    // Submit the form
    const submitButton = page.locator('button[type="submit"]:has-text("Sign In")');
    await expect(submitButton).toBeVisible();
    
    console.log('Submitting login form...');
    
    await Promise.all([
      page.waitForNavigation({ timeout: 10000 }),
      submitButton.click()
    ]);
    
    console.log('Current URL after login:', page.url());
    
    // Should be redirected back to our app
    await expect(page.url()).toContain('localhost:3000');
    
    // Wait a bit for the app to process the auth callback
    await page.waitForTimeout(3000);
    
    console.log('Final URL:', page.url());
    
    // Check if we're authenticated by calling the API
    const response = await page.evaluate(async () => {
      try {
        const res = await fetch('/api/auth/me');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('Auth API response:', response);
    
    // Should be authenticated
    expect(response.status).toBe(200);
    expect(response.data.authenticated).toBe(true);
  });

  test('should test Auth0 mock server directly', async ({ page }) => {
    // Test the Auth0 mock server endpoints directly
    
    // Test OpenID configuration
    const configResponse = await page.evaluate(async () => {
      try {
        const res = await fetch('http://localhost:3002/.well-known/openid_configuration');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('OpenID config:', configResponse);
    expect(configResponse.status).toBe(200);
    expect(configResponse.data.issuer).toBe('http://localhost:3002');
    
    // Test JWKS endpoint
    const jwksResponse = await page.evaluate(async () => {
      try {
        const res = await fetch('http://localhost:3002/.well-known/jwks.json');
        const data = await res.json();
        return { status: res.status, data };
      } catch (error) {
        return { error: error.message };
      }
    });
    
    console.log('JWKS:', jwksResponse);
    expect(jwksResponse.status).toBe(200);
    expect(jwksResponse.data.keys).toBeDefined();
    expect(jwksResponse.data.keys.length).toBeGreaterThan(0);
  });
});
