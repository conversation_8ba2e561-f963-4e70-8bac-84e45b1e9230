'use client';

import { useState } from 'react';
import { useUser } from '@auth0/nextjs-auth0';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { apiClient } from '@/lib/api';
import LoginButton from '../components/auth/LoginButton';

interface ApiResponse {
  message?: string;
  userData?: any;
  accessToken?: string;
  error?: string;
}

export default function DemoPage() {
  const { user, isLoading } = useUser();
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null);
  const [isCallLoading, setIsCallLoading] = useState(false);

  const callProtectedApi = async () => {
    setIsCallLoading(true);
    try {
      const response = await fetch('/api/protected');
      const data = await response.json();
      setApiResponse(data);
    } catch (error) {
      setApiResponse({ error: 'Failed to call API' });
    } finally {
      setIsCallLoading(false);
    }
  };

  const callBackendApi = async () => {
    setIsCallLoading(true);
    try {
      const response = await apiClient.get('/me');
      setApiResponse({
        message: 'Backend API call',
        userData: response.data,
        error: response.error,
      });
    } catch (error) {
      setApiResponse({ error: 'Failed to call backend API' });
    } finally {
      setIsCallLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">API Demo</h1>
            <p className="text-gray-600">Test Auth0 integration with protected APIs</p>
          </div>
          <LoginButton />
        </div>

        {!user ? (
          <Card>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>Please log in to test the protected API endpoints.</CardDescription>
            </CardHeader>
            <CardContent>
              <LoginButton />
            </CardContent>
          </Card>
        ) : (
          <>
            <Card>
              <CardHeader>
                <CardTitle>User Information</CardTitle>
                <CardDescription>Data from Auth0</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="font-medium">Name:</span>
                  <span>{user.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Email:</span>
                  <span>{user.email}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Email Verified:</span>
                  <Badge variant={user.email_verified ? 'default' : 'destructive'}>
                    {user.email_verified ? 'Verified' : 'Not Verified'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">User ID:</span>
                  <code className="text-sm bg-muted px-2 py-1 rounded">{user.sub}</code>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>API Testing</CardTitle>
                <CardDescription>
                  Test protected API endpoints with your Auth0 token
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <Button onClick={callProtectedApi} disabled={isCallLoading} variant="outline">
                    {isCallLoading ? 'Loading...' : 'Call Next.js Protected API'}
                  </Button>
                  <Button onClick={callBackendApi} disabled={isCallLoading} variant="outline">
                    {isCallLoading ? 'Loading...' : 'Call Backend API'}
                  </Button>
                </div>

                {apiResponse && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">API Response:</h4>
                    <pre className="text-sm overflow-auto">
                      {JSON.stringify(apiResponse, null, 2)}
                    </pre>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}
