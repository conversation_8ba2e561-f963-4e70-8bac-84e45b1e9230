'use client';

import { useAuth } from '@/lib/auth';
import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface DashboardData {
  balance: string;
  todaysSavings: string;
  monthlyGoal: string;
  investments: {
    total: string;
    performance: string;
  };
  recentTransactions: Array<{
    id: string;
    description: string;
    amount: string;
    date: string;
    type: 'credit' | 'debit';
  }>;
}

export default function Dashboard() {
  const { user, isLoading, hasPermission, logout } = useAuth();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [dataLoading, setDataLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/');
      return;
    }

    if (user && !hasPermission('read:dashboard')) {
      router.push('/unauthorized');
      return;
    }

    if (user) {
      loadDashboardData();
    }
  }, [user, isLoading, hasPermission, router]);

  const loadDashboardData = useCallback(async () => {
    try {
      setDataLoading(true);
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      setDashboardData({
        balance: '€1,847.50',
        todaysSavings: '€12.30',
        monthlyGoal: '€500.00',
        investments: {
          total: '€3,245.67',
          performance: '+5.2%',
        },
        recentTransactions: [
          {
            id: '1',
            description: 'Coffee Shop',
            amount: '€4.50',
            date: '2024-01-15',
            type: 'debit',
          },
          {
            id: '2',
            description: 'Salary Deposit',
            amount: '€2,500.00',
            date: '2024-01-15',
            type: 'credit',
          },
          {
            id: '3',
            description: 'Grocery Store',
            amount: '€67.89',
            date: '2024-01-14',
            type: 'debit',
          },
        ],
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setDataLoading(false);
    }
  }, []);

  if (isLoading || dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">Welcome, {user.name}</span>
              <button
                onClick={() => router.push('/profile')}
                className="text-blue-600 hover:text-blue-800"
              >
                Profile
              </button>
              <button
                onClick={() => router.push('/investments')}
                className="text-blue-600 hover:text-blue-800"
              >
                Investments
              </button>
              <button
                onClick={logout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Balance Card */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Current Balance</h3>
            <p className="text-3xl font-bold text-green-600">{dashboardData?.balance}</p>
          </div>

          {/* Today's Savings */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Today&apos;s Savings</h3>
            <p className="text-3xl font-bold text-blue-600">{dashboardData?.todaysSavings}</p>
          </div>

          {/* Monthly Goal */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Monthly Goal</h3>
            <p className="text-3xl font-bold text-purple-600">{dashboardData?.monthlyGoal}</p>
          </div>
        </div>

        {/* Investments Overview */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Investments Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Total Investment Value</p>
              <p className="text-2xl font-bold text-gray-900">{dashboardData?.investments.total}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Performance</p>
              <p className="text-2xl font-bold text-green-600">
                {dashboardData?.investments.performance}
              </p>
            </div>
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Recent Transactions</h3>
          <div className="space-y-4">
            {dashboardData?.recentTransactions.map(transaction => (
              <div
                key={transaction.id}
                className="flex justify-between items-center py-2 border-b border-gray-200"
              >
                <div>
                  <p className="font-medium text-gray-900">{transaction.description}</p>
                  <p className="text-sm text-gray-600">{transaction.date}</p>
                </div>
                <p
                  className={`font-semibold ${
                    transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {transaction.type === 'credit' ? '+' : '-'}
                  {transaction.amount}
                </p>
              </div>
            ))}
          </div>
        </div>
      </main>
    </div>
  );
}
