import { withPageAuthRequired } from '@auth0/nextjs-auth0';
import UserProfile from '../components/auth/UserProfile';
import LoginButton from '../components/auth/LoginButton';

export default withPageAuthRequired(function Profile() {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Profile</h1>
          <LoginButton />
        </div>
        <UserProfile />
      </div>
    </div>
  );
});
