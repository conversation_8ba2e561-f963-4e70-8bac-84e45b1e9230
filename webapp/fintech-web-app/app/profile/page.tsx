'use client';

import { useAuth } from '@/lib/auth';
import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

export default function Profile() {
  const { user, isLoading, hasPermission, logout, getAccessToken } = useAuth();
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/');
      return;
    }

    if (user && !hasPermission('read:profile')) {
      router.push('/unauthorized');
      return;
    }

    if (user) {
      loadAccessToken();
    }
  }, [user, isLoading, hasPermission, router]);

  const loadAccessToken = useCallback(async () => {
    const token = await getAccessToken();
    setAccessToken(token);
  }, [getAccessToken]);

  const refreshToken = async () => {
    try {
      const response = await fetch('/api/auth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: 'mock-refresh-token', // In real app, this would be stored
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAccessToken(data.access_token);
        alert('Token refreshed successfully!');
      } else {
        alert('Failed to refresh token');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      alert('Failed to refresh token');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Profile</h1>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/dashboard')}
                className="text-blue-600 hover:text-blue-800"
              >
                Dashboard
              </button>
              <button
                onClick={() => router.push('/investments')}
                className="text-blue-600 hover:text-blue-800"
              >
                Investments
              </button>
              <button
                onClick={logout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* User Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">User Information</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Name</label>
                <p className="mt-1 text-sm text-gray-900">{user.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">User ID</label>
                <p className="mt-1 text-sm text-gray-900 font-mono">{user.sub}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email Verified</label>
                <p className="mt-1 text-sm text-gray-900">
                  {user.email_verified ? (
                    <span className="text-green-600">✓ Verified</span>
                  ) : (
                    <span className="text-red-600">✗ Not Verified</span>
                  )}
                </p>
              </div>
            </div>
          </div>

          {/* Permissions & Roles */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Permissions & Roles</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                <div className="space-y-1">
                  {user.permissions?.map(permission => (
                    <span
                      key={permission}
                      className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-2"
                    >
                      {permission}
                    </span>
                  )) || <p className="text-sm text-gray-500">No permissions assigned</p>}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Roles</label>
                <div className="space-y-1">
                  {user.roles?.map(role => (
                    <span
                      key={role}
                      className="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded mr-2"
                    >
                      {role}
                    </span>
                  )) || <p className="text-sm text-gray-500">No roles assigned</p>}
                </div>
              </div>
            </div>
          </div>

          {/* Access Token */}
          <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Access Token</h2>
              <button
                onClick={refreshToken}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Refresh Token
              </button>
            </div>
            <div className="bg-gray-50 rounded-md p-4">
              <p className="text-xs font-mono text-gray-700 break-all">
                {accessToken || 'No token available'}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
