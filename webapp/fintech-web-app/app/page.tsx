"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useUser } from '@auth0/nextjs-auth0/client'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import {
  CreditCard,
  Home,
  Plus,
  TrendingUp,
  LogOut,
  Edit,
  Target,
  PiggyBank,
  Coffee,
  Car,
  Utensils,
  User,
  RotateCcw,
  Percent,
  Calendar,
  Settings,
  Bird,
  CheckCircle,
} from "lucide-react"
import { CardType, Screen, InvestmentStrategy, SavingMethod, BankCard, Goal, Transaction, SavingSettings } from "@/lib/types"
import { investmentStrategies } from "@/lib/constants"
import MarketingScreen from "@/components/screens/MarketingScreen"
import LoginScreen from "@/components/screens/LoginScreen"
import AddCardScreen from "@/components/screens/AddCardScreen"
import AddGoalScreen from "@/components/screens/AddGoalScreen"
import SavingsMethodsScreen from "@/components/screens/SavingsMethodsScreen"
import ContributionsScreen from "@/components/screens/ContributionsScreen"
import LoginButton from "@/app/components/auth/LoginButton"


export default function Page() {
  const { user: auth0User, error, isLoading } = useUser()
  const [currentScreen, setCurrentScreen] = useState<Screen>("marketing")
  const [marketingStep, setMarketingStep] = useState(0)
  const [cards, setCards] = useState<BankCard[]>([])
  const [goals, setGoals] = useState<Goal[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [savingSettings, setSavingSettings] = useState<SavingSettings>({
    roundups: true,
    percentage: true,
    percentageValue: 5,
    recurring: false,
    recurringAmount: 5,
    recurringFrequency: "weekly",
  })
  const [selectedStrategy, setSelectedStrategy] = useState<InvestmentStrategy>("Standard")

  // Form states
  const [loginForm, setLoginForm] = useState({ email: "", password: "" })
  const [cardForm, setCardForm] = useState({ name: "", type: "Debit" as CardType, cardNumber: "", cvv: "", expirationDate: "", limit: "" })
  const [goalForm, setGoalForm] = useState({ name: "", target: "", deadline: "" })
  const [editingCard, setEditingCard] = useState<BankCard | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [newLimit, setNewLimit] = useState("")

  // Use Auth0 user data
  const user = auth0User ? {
    email: auth0User.email || '',
    name: auth0User.name || auth0User.nickname || 'User'
  } : null

  const balance = 1847.5
  const currentGoal = goals[0] || {
    id: "1",
    name: "Vacation 2026",
    target: 2000,
    deadline: new Date("2026-07-01"),
    saved: 310,
    strategy: "Standard" as InvestmentStrategy,
  }

  // Sample transactions
  const sampleTransactions: Transaction[] = [
    { id: "1", merchant: "Starbucks", amount: 1.9, savings: 0.1, method: "roundups", icon: Coffee, time: "09:15" },
    { id: "2", merchant: "Lunch Spot", amount: 20, savings: 1.0, method: "percentage", icon: Utensils, time: "12:30" },
    { id: "3", merchant: "Weekly Top-up", amount: 5, savings: 5, method: "recurring", icon: PiggyBank, time: "10:00" },
    { id: "4", merchant: "Uber", amount: 12.8, savings: 0.2, method: "roundups", icon: Car, time: "18:45" },
  ]

  useEffect(() => {
    if (auth0User && transactions.length === 0) {
      setTransactions(sampleTransactions)
    }
    if (auth0User && goals.length === 0) {
      setGoals([currentGoal])
    }
    // Auto-navigate to dashboard if user is authenticated
    if (auth0User && currentScreen === "marketing") {
      setCurrentScreen("dashboard")
    }
  }, [auth0User, currentScreen])

  const totalSavingsToday = transactions.reduce((sum, t) => sum + t.savings, 0)
  const goalProgress = (currentGoal.saved / currentGoal.target) * 100
  const monthsLeft = Math.ceil((currentGoal.deadline.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24 * 30))
  const monthlyNeeded = (currentGoal.target - currentGoal.saved) / monthsLeft

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    if (loginForm.email && loginForm.password) {
      const name = loginForm.email.split("@")[0]
      setUser({ email: loginForm.email, name: name.charAt(0).toUpperCase() + name.slice(1) })
      setCurrentScreen("dashboard")
    }
  }

  const handleAddCard = (e: React.FormEvent) => {
    e.preventDefault()
    if (cardForm.name) {
      const newCard: BankCard = {
        id: Date.now().toString(),
        name: cardForm.name,
        type: cardForm.type,
        limit: cardForm.limit ? Number.parseInt(cardForm.limit) : undefined,
        cardNumber: cardForm.cardNumber,
        cvv: cardForm.cvv,
        expirationDate: cardForm.expirationDate,
      }
      setCards([...cards, newCard])
      setCardForm({ name: "", type: "Debit", limit: "", cardNumber: "", cvv: "", expirationDate: "" })
      setCurrentScreen("dashboard")
    }
  }

  const handleAddGoal = (e: React.FormEvent) => {
    e.preventDefault()
    if (goalForm.name && goalForm.target && goalForm.deadline) {
      const newGoal: Goal = {
        id: Date.now().toString(),
        name: goalForm.name,
        target: Number.parseFloat(goalForm.target),
        deadline: new Date(goalForm.deadline),
        saved: 0,
        strategy: selectedStrategy,
        isActive: true,
      }
      setGoals([newGoal, ...goals])
      setGoalForm({ name: "", target: "", deadline: "" })
      setCurrentScreen("dashboard")
    }
  }

  const handleEditLimit = (card: BankCard) => {
    setEditingCard(card)
    setNewLimit(card.limit?.toString() || "")
    setShowEditDialog(true)
  }

  const saveLimit = () => {
    if (editingCard) {
      setCards(
        cards.map((card) =>
          card.id === editingCard.id ? { ...card, limit: newLimit ? Number.parseInt(newLimit) : undefined } : card,
        ),
      )
      setShowEditDialog(false)
      setEditingCard(null)
      setNewLimit("")
    }
  }

  const logout = () => {
    // Clear local state
    setCards([])
    setGoals([])
    setTransactions([])
    setCurrentScreen("marketing")
    setMarketingStep(0)
    // Redirect to Auth0 logout
    window.location.href = '/api/auth/logout'
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#e58a35] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Marketing Flow - show if not authenticated
  if (currentScreen === "marketing" && !auth0User) {
    return (
      <MarketingScreen
        step={marketingStep}
        setStep={setMarketingStep}
        goToLogin={() => window.location.href = '/api/auth/login'}
      />
    )
  }

  // Login Screen - redirect to Auth0 if not authenticated
  if (currentScreen === "login" && !auth0User) {
    window.location.href = '/api/auth/login'
    return null
  }

  // Add Card Screen
  if (currentScreen === "add-card") {
    return (
      <AddCardScreen
        cardForm={cardForm}
        setCardForm={setCardForm}
        onSubmit={handleAddCard}
        cancel={() => setCurrentScreen("dashboard")}
      />
    )
  }

  // Add Goal Screen
  if (currentScreen === "add-goal") {
    return (
      <AddGoalScreen
        goalForm={goalForm}
        setGoalForm={setGoalForm}
        selectedStrategy={selectedStrategy}
        setSelectedStrategy={setSelectedStrategy}
        onSubmit={handleAddGoal}
        cancel={() => setCurrentScreen("dashboard")}
      />
    )
  }

  // Savings Methods Screen
  if (currentScreen === "savings-methods") {
    return (
      <SavingsMethodsScreen
        settings={savingSettings}
        setSettings={setSavingSettings}
        back={() => setCurrentScreen("dashboard")}
      />
    )
  }

  // Contributions Screen
  if (currentScreen === "contributions") {
    return (
      <ContributionsScreen
        transactions={transactions}
        settings={savingSettings}
        totalSavingsToday={totalSavingsToday}
        navigate={(s) => setCurrentScreen(s as Screen)}
      />
    )
  }

  // Investments Screen
  if (currentScreen === "investments") {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <div className="bg-gradient-to-r from-[#e58a35] to-amber-600 text-white p-6">
          <h1 className="text-2xl font-bold mb-2">Investment Strategies</h1>
          <p className="opacity-90">Choose your risk level</p>
        </div>

        <div className="p-4 space-y-6">
          {/* Current Strategy */}
          <Card className="border-l-4 border-l-[#e58a35]">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-[#e58a35]">Current Strategy</h3>
                  <p className="text-xl font-bold">{selectedStrategy}</p>
                  <p className="text-sm text-gray-600">{investmentStrategies[selectedStrategy].risk}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-green-600">
                    {investmentStrategies[selectedStrategy].return}
                  </p>
                  <p className="text-sm text-gray-600">Expected return</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Strategy Options */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">Available Strategies</h2>
            {Object.entries(investmentStrategies).map(([strategy, details]) => (
              <Card
                key={strategy}
                className={`cursor-pointer transition-all ${
                  selectedStrategy === strategy ? "ring-2 ring-[#e58a35] bg-[#e58a35]/5" : ""
                }`}
                onClick={() => setSelectedStrategy(strategy as InvestmentStrategy)}
              >
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{strategy}</h3>
                        <p className="text-sm text-gray-600">{details.description}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{details.risk}</Badge>
                        {selectedStrategy === strategy && <CheckCircle className="w-5 h-5 text-[#e58a35]" />}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className={`font-medium ${details.color}`}>{details.return} annual return</span>
                      <span className="text-sm text-gray-600">{details.projection}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Goal Impact */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Impact on Your Goal</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span>Current goal: {currentGoal.name}</span>
                  <span className="font-semibold">€{currentGoal.target}</span>
                </div>
                <div className="flex justify-between">
                  <span>Monthly savings needed:</span>
                  <span className="font-semibold">€{monthlyNeeded.toFixed(0)}</span>
                </div>
                <div className="flex justify-between">
                  <span>With {selectedStrategy} strategy:</span>
                  <span className="font-semibold text-[#e58a35]">
                    {investmentStrategies[selectedStrategy].projection}
                  </span>
                </div>
                <Progress value={goalProgress} className="h-3" />
                <div className="text-sm text-gray-600 text-center">
                  {goalProgress.toFixed(1)}% complete • {monthsLeft} months left
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
          <div className="flex justify-around">
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("dashboard")}
              className="flex flex-col items-center gap-1"
            >
              <Home className="w-5 h-5" />
              <span className="text-xs">Home</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("add-card")}
              className="flex flex-col items-center gap-1"
            >
              <CreditCard className="w-5 h-5" />
              <span className="text-xs">Cards</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("contributions")}
              className="flex flex-col items-center gap-1"
            >
              <PiggyBank className="w-5 h-5" />
              <span className="text-xs">Contributions</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center gap-1 text-[#e58a35]">
              <TrendingUp className="w-5 h-5" />
              <span className="text-xs">Investments</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("profile")}
              className="flex flex-col items-center gap-1"
            >
              <User className="w-5 h-5" />
              <span className="text-xs">Profile</span>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Profile Screen
  if (currentScreen === "profile") {
    return (
      <div className="min-h-screen bg-gray-50 pb-20">
        <div className="bg-gradient-to-r from-[#e58a35] to-amber-600 text-white p-6">
          <h1 className="text-2xl font-bold mb-2">Profile</h1>
          <p className="opacity-90">{user?.email}</p>
        </div>

        <div className="p-4 space-y-6">
          <Card>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-[#e58a35] to-amber-600 rounded-full flex items-center justify-center">
                  <Bird className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-lg">Welcome back, {user?.name}!</h3>
                  <p className="text-gray-600">{user?.email}</p>
                </div>
              </div>
              <Separator />
              <div className="space-y-3">
                <Button onClick={() => setCurrentScreen("add-goal")} variant="outline" className="w-full justify-start">
                  <Target className="w-4 h-4 mr-2" />
                  Add New Goal
                </Button>
                <Button
                  onClick={() => setCurrentScreen("savings-methods")}
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Saving Settings
                </Button>
              </div>
              <Separator />
              <Button onClick={logout} variant="outline" className="w-full">
                <LogOut className="w-4 h-4 mr-2" />
                Log Out
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Account Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Total Balance</span>
                <span className="font-semibold">€{balance.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Active Goals</span>
                <span className="font-semibold">{goals.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Cards Connected</span>
                <span className="font-semibold">{cards.length}</span>
              </div>
              <div className="flex justify-between">
                <span>Today's Savings</span>
                <span className="font-semibold text-[#e58a35]">€{totalSavingsToday.toFixed(2)}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Saving Methods Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <RotateCcw className="w-4 h-4 text-[#e58a35]" />
                  <span>Round-ups</span>
                </div>
                <Badge variant={savingSettings.roundups ? "default" : "secondary"}>
                  {savingSettings.roundups ? "Active" : "Inactive"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Percent className="w-4 h-4 text-[#e58a35]" />
                  <span>Percentage Saving</span>
                </div>
                <Badge variant={savingSettings.percentage ? "default" : "secondary"}>
                  {savingSettings.percentage ? `${savingSettings.percentageValue}%` : "Inactive"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-[#e58a35]" />
                  <span>Recurring Transfers</span>
                </div>
                <Badge variant={savingSettings.recurring ? "default" : "secondary"}>
                  {savingSettings.recurring ? `€${savingSettings.recurringAmount}` : "Inactive"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Navigation */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
          <div className="flex justify-around">
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("dashboard")}
              className="flex flex-col items-center gap-1"
            >
              <Home className="w-5 h-5" />
              <span className="text-xs">Home</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("add-card")}
              className="flex flex-col items-center gap-1"
            >
              <CreditCard className="w-5 h-5" />
              <span className="text-xs">Cards</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("contributions")}
              className="flex flex-col items-center gap-1"
            >
              <PiggyBank className="w-5 h-5" />
              <span className="text-xs">Contributions</span>
            </Button>
            <Button
              variant="ghost"
              onClick={() => setCurrentScreen("investments")}
              className="flex flex-col items-center gap-1"
            >
              <TrendingUp className="w-5 h-5" />
              <span className="text-xs">Investments</span>
            </Button>
            <Button variant="ghost" className="flex flex-col items-center gap-1 text-[#e58a35]">
              <User className="w-5 h-5" />
              <span className="text-xs">Profile</span>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Dashboard Screen
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      {/* Header */}
      <div className="bg-gradient-to-r from-[#e58a35] to-amber-600 text-white p-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm opacity-90">Welcome back,</p>
              <p className="text-lg font-medium">{user?.name}</p>
            </div>
            <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <Bird className="w-6 h-6" />
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-sm opacity-90">Total Balance</p>
            <p className="text-3xl font-bold">€{balance.toLocaleString()}</p>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Current Goal */}
        <Card className="border-l-4 border-l-[#e58a35]">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Target className="w-5 h-5 text-[#e58a35]" />
                  <h3 className="font-semibold">{currentGoal.name}</h3>
                </div>
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-600">Target</p>
                  <p className="font-semibold">€{currentGoal.target}</p>
                </div>
                <div>
                  <p className="text-gray-600">Time Left</p>
                  <p className="font-semibold">{monthsLeft} months</p>
                </div>
                <div>
                  <p className="text-gray-600">Saved</p>
                  <p className="font-semibold text-[#e58a35]">€{currentGoal.saved}</p>
                </div>
                <div>
                  <p className="text-gray-600">Projected</p>
                  <p className="font-semibold text-green-600">€2,130</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span className="text-green-600 font-medium">On Track ✅</span>
                </div>
                <Progress value={goalProgress} className="h-3" />
                <p className="text-xs text-gray-600 text-center">{goalProgress.toFixed(1)}% complete</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Today's Savings */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-[#e58a35]">Today's Savings</h3>
                <p className="text-2xl font-bold">€{totalSavingsToday.toFixed(2)}</p>
                <p className="text-sm text-gray-600">From {transactions.length} transactions</p>
              </div>
              <div className="w-16 h-16 bg-[#e58a35]/10 rounded-full flex items-center justify-center">
                <PiggyBank className="w-8 h-8 text-[#e58a35]" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Daily Savings Feed */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Daily Savings Feed</h2>
            <Button
              onClick={() => setCurrentScreen("contributions")}
              variant="ghost"
              size="sm"
              className="text-[#e58a35]"
            >
              View All
            </Button>
          </div>
          <div className="space-y-2">
            {transactions.slice(0, 4).map((transaction) => {
              const IconComponent = transaction.icon
              const methodIcons = {
                roundups: RotateCcw,
                percentage: Percent,
                recurring: Calendar,
              }
              const MethodIcon = methodIcons[transaction.method]
              return (
                <Card key={transaction.id}>
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <IconComponent className="w-4 h-4 text-gray-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{transaction.merchant}</p>
                          <div className="flex items-center gap-1">
                            <MethodIcon className="w-3 h-3 text-[#e58a35]" />
                            <p className="text-xs text-gray-600">
                              €{transaction.amount.toFixed(2)} → €{transaction.savings.toFixed(2)} saved
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-[#e58a35]">+€{transaction.savings.toFixed(2)}</p>
                        <p className="text-xs text-gray-600">{transaction.time}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
          <div className="bg-[#e58a35]/5 rounded-lg p-3 text-center">
            <p className="text-sm font-medium text-[#e58a35]">Total today: €{totalSavingsToday.toFixed(2)}</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-4">
          <Button
            onClick={() => setCurrentScreen("add-goal")}
            variant="outline"
            className="h-20 flex flex-col items-center gap-2"
          >
            <Target className="w-6 h-6 text-[#e58a35]" />
            <span className="text-sm">Add Goal</span>
          </Button>
          <Button
            onClick={() => setCurrentScreen("savings-methods")}
            variant="outline"
            className="h-20 flex flex-col items-center gap-2"
          >
            <Settings className="w-6 h-6 text-[#e58a35]" />
            <span className="text-sm">Settings</span>
          </Button>
        </div>

        {/* Cards Section */}
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Your Cards</h2>
            <Button onClick={() => setCurrentScreen("add-card")} size="sm" variant="outline">
              <Plus className="w-4 h-4 mr-2" />
              Add Card
            </Button>
          </div>

          {cards.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <CreditCard className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">No cards added yet</p>
                <Button onClick={() => setCurrentScreen("add-card")} className="bg-[#e58a35] hover:bg-[#d17a2e]">
                  Add Your First Card
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {cards.map((card) => (
                <Card key={card.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{card.name}</h3>
                          <Badge variant={card.type === "Credit" ? "default" : "secondary"}>{card.type}</Badge>
                        </div>
                        <p className="text-sm text-gray-600">•••• {card.cardNumber}</p>
                        <p className="text-sm text-gray-600">{card.limit ? `Limit: €${card.limit}` : "No limit"}</p>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => handleEditLimit(card)}>
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex justify-around">
          <Button variant="ghost" className="flex flex-col items-center gap-1 text-[#e58a35]">
            <Home className="w-5 h-5" />
            <span className="text-xs">Home</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => setCurrentScreen("add-card")}
            className="flex flex-col items-center gap-1"
          >
            <CreditCard className="w-5 h-5" />
            <span className="text-xs">Cards</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => setCurrentScreen("contributions")}
            className="flex flex-col items-center gap-1"
          >
            <PiggyBank className="w-5 h-5" />
            <span className="text-xs">Contributions</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => setCurrentScreen("investments")}
            className="flex flex-col items-center gap-1"
          >
            <TrendingUp className="w-5 h-5" />
            <span className="text-xs">Investments</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => setCurrentScreen("profile")}
            className="flex flex-col items-center gap-1"
          >
            <User className="w-5 h-5" />
            <span className="text-xs">Profile</span>
          </Button>
        </div>
      </div>

      {/* Edit Limit Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Spending Limit</DialogTitle>
            <DialogDescription>Update the spending limit for {editingCard?.name}</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="newLimit">New Limit (€)</Label>
              <Input
                id="newLimit"
                type="number"
                placeholder="Enter amount or leave empty for no limit"
                value={newLimit}
                onChange={(e) => setNewLimit(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={saveLimit} className="bg-[#e58a35] hover:bg-[#d17a2e]">
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}