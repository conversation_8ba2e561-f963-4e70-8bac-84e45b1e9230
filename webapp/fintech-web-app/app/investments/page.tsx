'use client';

import { useAuth } from '@/lib/auth';
import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface Investment {
  id: string;
  name: string;
  symbol: string;
  shares: number;
  currentPrice: number;
  totalValue: number;
  performance: number;
  performancePercent: number;
}

interface PortfolioData {
  totalValue: number;
  totalGainLoss: number;
  totalGainLossPercent: number;
  investments: Investment[];
}

export default function Investments() {
  const { user, isLoading, hasPermission, logout } = useAuth();
  const [portfolioData, setPortfolioData] = useState<PortfolioData | null>(null);
  const [dataLoading, setDataLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/');
      return;
    }

    if (user && !hasPermission('read:investments')) {
      router.push('/unauthorized');
      return;
    }

    if (user) {
      loadPortfolioData();
    }
  }, [user, isLoading, hasPermission, router]);

  const loadPortfolioData = useCallback(async () => {
    try {
      setDataLoading(true);
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));

      setPortfolioData({
        totalValue: 15420.5,
        totalGainLoss: 1250.3,
        totalGainLossPercent: 8.8,
        investments: [
          {
            id: '1',
            name: 'Apple Inc.',
            symbol: 'AAPL',
            shares: 25,
            currentPrice: 175.5,
            totalValue: 4387.5,
            performance: 287.5,
            performancePercent: 7.0,
          },
          {
            id: '2',
            name: 'Microsoft Corporation',
            symbol: 'MSFT',
            shares: 15,
            currentPrice: 380.2,
            totalValue: 5703.0,
            performance: 453.0,
            performancePercent: 8.6,
          },
          {
            id: '3',
            name: 'Tesla Inc.',
            symbol: 'TSLA',
            shares: 10,
            currentPrice: 245.8,
            totalValue: 2458.0,
            performance: -142.0,
            performancePercent: -5.5,
          },
          {
            id: '4',
            name: 'Amazon.com Inc.',
            symbol: 'AMZN',
            shares: 8,
            currentPrice: 154.25,
            totalValue: 1234.0,
            performance: 89.0,
            performancePercent: 7.8,
          },
          {
            id: '5',
            name: 'NVIDIA Corporation',
            symbol: 'NVDA',
            shares: 5,
            currentPrice: 327.6,
            totalValue: 1638.0,
            performance: 563.0,
            performancePercent: 52.4,
          },
        ],
      });
    } catch (error) {
      console.error('Failed to load portfolio data:', error);
    } finally {
      setDataLoading(false);
    }
  }, []);

  if (isLoading || dataLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading investments...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <h1 className="text-2xl font-bold text-gray-900">Investments</h1>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/dashboard')}
                className="text-blue-600 hover:text-blue-800"
              >
                Dashboard
              </button>
              <button
                onClick={() => router.push('/profile')}
                className="text-blue-600 hover:text-blue-800"
              >
                Profile
              </button>
              <button
                onClick={logout}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Portfolio Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Total Portfolio Value</h3>
            <p className="text-3xl font-bold text-blue-600">
              €{portfolioData?.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Total Gain/Loss</h3>
            <p
              className={`text-3xl font-bold ${
                (portfolioData?.totalGainLoss || 0) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {(portfolioData?.totalGainLoss || 0) >= 0 ? '+' : ''}€
              {portfolioData?.totalGainLoss.toLocaleString('en-US', { minimumFractionDigits: 2 })}
            </p>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Performance</h3>
            <p
              className={`text-3xl font-bold ${
                (portfolioData?.totalGainLossPercent || 0) >= 0 ? 'text-green-600' : 'text-red-600'
              }`}
            >
              {(portfolioData?.totalGainLossPercent || 0) >= 0 ? '+' : ''}
              {portfolioData?.totalGainLossPercent.toFixed(1)}%
            </p>
          </div>
        </div>

        {/* Holdings Table */}
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-900">Your Holdings</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Shares
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Value
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Gain/Loss
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Performance
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {portfolioData?.investments.map(investment => (
                  <tr key={investment.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{investment.name}</div>
                        <div className="text-sm text-gray-500">{investment.symbol}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {investment.shares}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      €{investment.currentPrice.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      €{investment.totalValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={investment.performance >= 0 ? 'text-green-600' : 'text-red-600'}
                      >
                        {investment.performance >= 0 ? '+' : ''}€{investment.performance.toFixed(2)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span
                        className={
                          investment.performancePercent >= 0 ? 'text-green-600' : 'text-red-600'
                        }
                      >
                        {investment.performancePercent >= 0 ? '+' : ''}
                        {investment.performancePercent.toFixed(1)}%
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>
    </div>
  );
}
