'use client';

import { useUser } from '@auth0/nextjs-auth0/client';
import { Button } from '@/components/ui/button';

export default function LoginButton() {
  const { user, isLoading } = useUser();

  if (isLoading) return <div>Loading...</div>;

  if (user) {
    return (
      <Button asChild>
        <a href="/api/auth/logout">Logout</a>
      </Button>
    );
  }

  return (
    <Button asChild>
      <a href="/api/auth/login">Login</a>
    </Button>
  );
}
