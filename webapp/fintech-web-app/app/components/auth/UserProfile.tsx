'use client';

import { useAuth } from '@/lib/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

export default function UserProfile() {
  const { user, error, isLoading } = useAuth();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>{error.message}</div>;

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Not Authenticated</CardTitle>
          <CardDescription>Please log in to view your profile.</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>User Profile</CardTitle>
        <CardDescription>Your account information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={''} alt={user.name || 'User'} />
            <AvatarFallback>{user.name?.charAt(0).toUpperCase() || 'U'}</AvatarFallback>
          </Avatar>
          <div>
            <h3 className="text-lg font-semibold">{user.name}</h3>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>

        <div className="space-y-2">
          <div>
            <span className="font-medium">Email Verified:</span>
            <Badge variant={user.email_verified ? 'default' : 'destructive'} className="ml-2">
              {user.email_verified ? 'Verified' : 'Not Verified'}
            </Badge>
          </div>

          <div>
            <span className="font-medium">User ID:</span>
            <code className="ml-2 text-sm bg-muted px-2 py-1 rounded">{user.sub}</code>
          </div>

          <div>
            <span className="font-medium">Last Updated:</span>
            <span className="ml-2 text-sm">
              N/A
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
