import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  
  if (!token) {
    return NextResponse.json({ error: 'No token found' }, { status: 401 });
  }
  
  return NextResponse.json({ access_token: token });
}

export async function POST(request: NextRequest) {
  // Handle token refresh
  const { refresh_token } = await request.json();
  
  if (!refresh_token) {
    return NextResponse.json({ error: 'No refresh token provided' }, { status: 400 });
  }
  
  try {
    // Call local Auth0 mock to refresh token
    const response = await fetch(`${process.env.LOCAL_AUTH0_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'refresh_token',
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        refresh_token,
      }),
    });
    
    if (!response.ok) {
      throw new Error('Token refresh failed');
    }
    
    const tokens = await response.json();
    
    // Update cookies
    const nextResponse = NextResponse.json({ access_token: tokens.access_token });
    nextResponse.cookies.set('auth-token', tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: tokens.expires_in || 3600,
    });
    
    return nextResponse;
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json({ error: 'Token refresh failed' }, { status: 500 });
  }
}
