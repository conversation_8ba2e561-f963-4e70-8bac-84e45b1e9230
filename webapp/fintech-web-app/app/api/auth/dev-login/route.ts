import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// Development-only login endpoint
// This bypasses Auth0 for local development
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 404 });
  }

  try {
    const { email, password } = await request.json();

    // Simple validation - accept any email/password for development
    if (!email || !password) {
      return NextResponse.json({ error: 'Email and password required' }, { status: 400 });
    }

    // Create a mock JWT token
    const mockUser = {
      sub: 'dev|' + Buffer.from(email).toString('base64'),
      email: email,
      email_verified: true,
      name: email.split('@')[0].charAt(0).toUpperCase() + email.split('@')[0].slice(1),
      given_name: email.split('@')[0].charAt(0).toUpperCase() + email.split('@')[0].slice(1),
      family_name: 'User',
      nickname: email.split('@')[0],
      picture: 'https://via.placeholder.com/150',
      aud: 'my-api',
      iss: 'http://localhost:3002',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      scope: 'openid profile email read:profile read:dashboard read:investments read:transactions read:cards read:goals write:profile write:cards write:goals'
    };

    // Sign the token (use a simple secret for development)
    const token = jwt.sign(mockUser, 'dev-secret-key');

    // Create response with secure cookie
    const response = NextResponse.json({ 
      success: true, 
      user: {
        email: mockUser.email,
        name: mockUser.name,
        picture: mockUser.picture
      }
    });

    // Set HTTP-only cookie for authentication
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: false, // Allow HTTP in development
      sameSite: 'lax',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Dev login error:', error);
    return NextResponse.json({ error: 'Login failed' }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
