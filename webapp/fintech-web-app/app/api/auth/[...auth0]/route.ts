// Simplified Auth0 route for testing
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  // Mock Auth0 authentication for testing
  const url = new URL(request.url);

  if (url.pathname.includes('/login')) {
    // Redirect to mock Auth0 login
    return NextResponse.redirect('https://mock-auth0.com/login');
  }

  if (url.pathname.includes('/logout')) {
    // Redirect to logout
    return NextResponse.redirect(new URL('/', request.url));
  }

  if (url.pathname.includes('/callback')) {
    // Mock successful callback
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.json({ message: 'Auth0 route' });
}
