import { NextRequest, NextResponse } from 'next/server';

// Local Auth0 Mock Integration
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const pathname = url.pathname;

  // Extract the auth action from the URL
  const authAction = pathname.split('/').pop();

  switch (authAction) {
    case 'login':
      return handleLogin(request);
    case 'logout':
      return handleLogout(request);
    case 'callback':
      return handleCallback(request);
    case 'me':
      return handleProfile(request);
    default:
      return NextResponse.json({ error: 'Unknown auth action' }, { status: 404 });
  }
}

async function handleLogin(request: NextRequest) {
  const url = new URL(request.url);
  const returnTo = url.searchParams.get('returnTo') || '/';

  // Redirect to local Auth0 mock login
  const loginUrl = new URL('/authorize', process.env.LOCAL_AUTH0_URL);
  loginUrl.searchParams.set('client_id', process.env.AUTH0_CLIENT_ID!);
  loginUrl.searchParams.set('response_type', 'code');
  loginUrl.searchParams.set('redirect_uri', `${process.env.AUTH0_BASE_URL}/api/auth/callback`);
  loginUrl.searchParams.set('scope', 'openid profile email read:profile read:dashboard read:investments');
  loginUrl.searchParams.set('audience', process.env.AUTH0_AUDIENCE!);
  loginUrl.searchParams.set('state', Buffer.from(JSON.stringify({ returnTo })).toString('base64'));

  return NextResponse.redirect(loginUrl.toString());
}

async function handleLogout(request: NextRequest) {
  const response = NextResponse.redirect(new URL('/', request.url));

  // Clear auth cookies
  response.cookies.delete('auth-token');
  response.cookies.delete('auth-user');

  return response;
}

async function handleCallback(request: NextRequest) {
  const url = new URL(request.url);
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');

  if (!code) {
    return NextResponse.redirect(new URL('/?error=no_code', request.url));
  }

  try {
    // Exchange code for token with local Auth0 mock
    const tokenResponse = await fetch(`${process.env.LOCAL_AUTH0_URL}/oauth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        grant_type: 'authorization_code',
        client_id: process.env.AUTH0_CLIENT_ID,
        client_secret: process.env.AUTH0_CLIENT_SECRET,
        code,
        redirect_uri: `${process.env.AUTH0_BASE_URL}/api/auth/callback`,
      }),
    });

    if (!tokenResponse.ok) {
      throw new Error('Token exchange failed');
    }

    const tokens = await tokenResponse.json();

    // Get user info
    const userResponse = await fetch(`${process.env.LOCAL_AUTH0_URL}/userinfo`, {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
      },
    });

    const user = await userResponse.json();

    // Parse return URL from state
    let returnTo = '/';
    if (state) {
      try {
        const stateData = JSON.parse(Buffer.from(state, 'base64').toString());
        returnTo = stateData.returnTo || '/';
      } catch (e) {
        console.warn('Failed to parse state:', e);
      }
    }

    // Set auth cookies and redirect
    const response = NextResponse.redirect(new URL(returnTo, request.url));
    response.cookies.set('auth-token', tokens.access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: tokens.expires_in || 3600,
    });
    response.cookies.set('auth-user', JSON.stringify(user), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: tokens.expires_in || 3600,
    });

    return response;
  } catch (error) {
    console.error('Auth callback error:', error);
    return NextResponse.redirect(new URL('/?error=auth_failed', request.url));
  }
}

async function handleProfile(request: NextRequest) {
  const token = request.cookies.get('auth-token')?.value;
  const userCookie = request.cookies.get('auth-user')?.value;

  if (!token || !userCookie) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = JSON.parse(userCookie);
    return NextResponse.json(user);
  } catch (error) {
    return NextResponse.json({ error: 'Invalid user data' }, { status: 401 });
  }
}
