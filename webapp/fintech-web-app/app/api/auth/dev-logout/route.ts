import { NextRequest, NextResponse } from 'next/server';

// Development-only logout endpoint
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV === 'production') {
    return NextResponse.json({ error: 'Not available in production' }, { status: 404 });
  }

  try {
    const response = NextResponse.json({ success: true, message: 'Logged out successfully' });

    // Clear the auth token cookie by setting it to empty and expiring it
    response.cookies.set('auth-token', '', {
      httpOnly: true,
      secure: false,
      sameSite: 'lax',
      maxAge: -1, // Expire immediately (negative value)
      expires: new Date(0), // Set to epoch time
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Dev logout error:', error);
    return NextResponse.json({ error: 'Logout failed' }, { status: 500 });
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
