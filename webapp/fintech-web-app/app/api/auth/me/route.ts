import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  try {
    // In development, check for dev auth token
    if (process.env.NODE_ENV !== 'production') {
      const authToken = request.cookies.get('auth-token')?.value;

      if (authToken) {
        try {
          const decoded = jwt.verify(authToken, 'dev-secret-key') as any;
          return NextResponse.json({
            authenticated: true,
            user: {
              sub: decoded.sub,
              email: decoded.email,
              name: decoded.name,
              picture: decoded.picture,
            },
          });
        } catch (jwtError) {
          // Invalid token, continue to unauthenticated response
        }
      }
    }

    // TODO: Add Auth0 session check here when needed
    // For now, we'll focus on development authentication

    return NextResponse.json({ authenticated: false }, { status: 401 });
  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json({ authenticated: false }, { status: 401 });
  }
}
