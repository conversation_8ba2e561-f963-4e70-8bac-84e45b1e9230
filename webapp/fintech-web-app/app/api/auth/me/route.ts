import { getSession } from '@auth0/nextjs-auth0';
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  try {
    // First try Auth0 session
    const session = await getSession();

    if (session && session.user) {
      console.log('Auth0 session found:', session.user.email);
      return NextResponse.json({
        authenticated: true,
        user: {
          sub: session.user.sub,
          email: session.user.email,
          name: session.user.name,
          picture: session.user.picture,
        },
      });
    }

    console.log('No Auth0 session, checking dev token...');

    // In development, also check for dev auth token
    if (process.env.NODE_ENV !== 'production') {
      const authToken = request.cookies.get('auth-token')?.value;
      console.log('Dev token found:', !!authToken);

      if (authToken) {
        try {
          const decoded = jwt.verify(authToken, 'dev-secret-key') as any;
          console.log('Dev token valid for:', decoded.email);
          return NextResponse.json({
            authenticated: true,
            user: {
              sub: decoded.sub,
              email: decoded.email,
              name: decoded.name,
              picture: decoded.picture,
            },
          });
        } catch (jwtError) {
          console.log('Dev token verification failed:', jwtError.message);
          // Invalid token, continue to unauthenticated response
        }
      }
    }

    console.log('No valid authentication found');
    return NextResponse.json({ authenticated: false }, { status: 401 });
  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json({ authenticated: false }, { status: 401 });
  }
}
