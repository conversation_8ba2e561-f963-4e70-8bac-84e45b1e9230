import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function GET(request: NextRequest) {
  try {
    // Check for Auth0 session cookie (appSession)
    const appSession = request.cookies.get('appSession')?.value;

    if (appSession) {
      try {
        // Try to decode the Auth0 session
        // In a real implementation, you'd verify this properly
        // For now, we'll assume it's valid if it exists
        return NextResponse.json({
          authenticated: true,
          user: {
            sub: 'auth0|session-user',
            email: '<EMAIL>',
            name: 'Session User',
            picture: 'https://via.placeholder.com/150',
          },
        });
      } catch (sessionError) {
        console.log('Auth0 session decode failed:', sessionError.message);
      }
    }

    // Fallback to development auth token in development mode
    if (process.env.NODE_ENV !== 'production') {
      const authToken = request.cookies.get('auth-token')?.value;

      if (authToken) {
        try {
          const decoded = jwt.verify(authToken, 'dev-secret-key') as any;
          return NextResponse.json({
            authenticated: true,
            user: {
              sub: decoded.sub,
              email: decoded.email,
              name: decoded.name,
              picture: decoded.picture,
            },
          });
        } catch (jwtError) {
          // Invalid token, continue to unauthenticated response
        }
      }
    }

    return NextResponse.json({ authenticated: false }, { status: 401 });
  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json({ authenticated: false }, { status: 401 });
  }
}
