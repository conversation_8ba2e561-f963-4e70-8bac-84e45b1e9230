import { getAccessToken, withApiAuthRequired } from '@auth0/nextjs-auth0';
import { NextRequest, NextResponse } from 'next/server';

export const GET = withApiAuthRequired(async function handler(req: NextRequest) {
  try {
    const { accessToken } = await getAccessToken();
    
    // Example: Call your backend API with the access token
    const response = await fetch(`${process.env.API_BASE_URL}/me`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch user data from backend' },
        { status: response.status }
      );
    }

    const userData = await response.json();
    
    return NextResponse.json({
      message: 'This is a protected API route',
      userData,
      accessToken: accessToken ? 'Token available' : 'No token',
    });
  } catch (error) {
    console.error('Protected API route error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
});
