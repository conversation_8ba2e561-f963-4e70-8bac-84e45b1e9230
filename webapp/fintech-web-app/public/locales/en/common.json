{"app": {"title": "FinTech App", "subtitle": "Smart Savings & Investments"}, "navigation": {"dashboard": "Dashboard", "cards": "Cards", "goals": "Goals", "investments": "Investments", "profile": "Profile"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "email": "Email", "password": "Password", "loginButton": "Sign In", "welcome": "Welcome back!"}, "dashboard": {"totalSavings": "Total Savings", "monthlyContribution": "Monthly Contribution", "activeGoals": "Active Goals", "recentTransactions": "Recent Transactions", "quickActions": "Quick Actions", "addCard": "Add Card", "addGoal": "Add Goal", "viewInvestments": "View Investments"}, "cards": {"title": "Payment Cards", "addNew": "Add New Card", "cardName": "Card Name", "cardNumber": "Card Number", "cvv": "CVV", "expirationDate": "Expiration Date", "cardType": "Card Type", "debit": "Debit", "credit": "Credit", "limit": "Credit Limit", "save": "Save Card", "cancel": "Cancel", "remove": "Remove Card", "removeConfirmation": "Are you sure you want to remove this card?", "lastFour": "•••• {{lastFour}}"}, "goals": {"title": "Savings Goals", "addNew": "Add New Goal", "goalName": "Goal Name", "targetAmount": "Target Amount", "deadline": "Deadline", "strategy": "Investment Strategy", "save": "Save Goal", "cancel": "Cancel", "progress": "Progress", "saved": "Saved", "remaining": "Remaining", "changeGoalConfirmation": "Are you sure you want to change this goal? It's currently running.", "strategies": {"fixedIncome": "Fixed Income", "standard": "Standard", "growth": "Growth"}}, "investments": {"title": "Investment Strategies", "fixedIncome": {"name": "Fixed Income", "risk": "Low Risk", "return": "3-5% annually", "description": "Conservative approach with stable returns", "projection": "Safe and steady growth"}, "standard": {"name": "Standard", "risk": "Medium Risk", "return": "6-8% annually", "description": "Balanced portfolio with moderate growth", "projection": "Balanced risk and reward"}, "growth": {"name": "Growth", "risk": "High Risk", "return": "9-12% annually", "description": "Aggressive strategy for maximum returns", "projection": "High potential returns"}}, "savings": {"methods": "Saving Methods", "roundups": "Round-ups", "percentage": "Percentage of purchases", "recurring": "Recurring transfers", "enable": "Enable", "disable": "Disable", "amount": "Amount", "frequency": "Frequency"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "confirm": "Confirm", "back": "Back", "next": "Next", "loading": "Loading...", "error": "Error", "success": "Success", "currency": "$"}, "validation": {"cardName": {"required": "Card name is required"}, "cardNumber": {"invalid": "Invalid card number"}, "cvv": {"invalid": "Invalid CVV"}, "expirationDate": {"invalid": "Invalid expiration date"}, "goalName": {"required": "Goal name is required"}, "target": {"invalid": "Target amount must be greater than 0"}, "deadline": {"future": "Deadline must be in the future"}, "email": {"invalid": "Invalid email address"}, "password": {"minLength": "Password must be at least 6 characters"}}}