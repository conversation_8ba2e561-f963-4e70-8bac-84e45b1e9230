'use client';

import { marketingScreens } from '@/lib/constants';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface Props {
  step: number;
  setStep: (step: number) => void;
  goToLogin: () => void;
}

export default function MarketingScreen({ step, setStep, goToLogin }: Props) {
  const currentMarketingScreen = marketingScreens[step];
  const IconComponent = currentMarketingScreen.icon;
  return (
    <div
      className={`min-h-screen bg-gradient-to-br ${currentMarketingScreen.gradient} flex flex-col`}
    >
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center text-white space-y-8 max-w-sm">
          <div className="w-24 h-24 mx-auto bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
            <IconComponent className="w-12 h-12" />
          </div>
          <div className="space-y-4">
            <h1 className="text-3xl font-bold leading-tight">{currentMarketingScreen.title}</h1>
            <p className="text-xl opacity-90 font-medium">{currentMarketingScreen.subtitle}</p>
            <p className="text-lg opacity-75">{currentMarketingScreen.description}</p>
          </div>
        </div>
      </div>
      <div className="p-6 space-y-6">
        <div className="flex justify-center space-x-2">
          {marketingScreens.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all ${index === step ? 'bg-white w-8' : 'bg-white/40'}`}
            />
          ))}
        </div>
        <div className="flex justify-between items-center">
          <Button
            variant="ghost"
            onClick={() => setStep(Math.max(0, step - 1))}
            disabled={step === 0}
            className="text-white hover:bg-white/20"
          >
            <ChevronLeft className="w-5 h-5 mr-1" />
            Back
          </Button>
          {step === marketingScreens.length - 1 ? (
            <Button
              onClick={goToLogin}
              size="lg"
              className="bg-white text-[#e58a35] hover:bg-gray-100 px-8 font-semibold"
            >
              Get Started
            </Button>
          ) : (
            <Button
              onClick={() => setStep(Math.min(marketingScreens.length - 1, step + 1))}
              className="bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm"
            >
              Next
              <ChevronRight className="w-5 h-5 ml-1" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
