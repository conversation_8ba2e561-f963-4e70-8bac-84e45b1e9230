'use client';

import { <PERSON> } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface Props {
  loginForm: { email: string; password: string };
  setLoginForm: (form: { email: string; password: string }) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export default function LoginScreen({ loginForm, setLoginForm, onSubmit }: Props) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#e58a35] to-amber-600 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-[#e58a35] to-amber-600 rounded-full flex items-center justify-center">
            <Bird className="w-8 h-8 text-white" />
          </div>
          <div>
            <CardTitle className="text-2xl text-[#e58a35]">nesteggli</CardTitle>
            <CardDescription className="mt-2">Sign in to grow your nest egg</CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={loginForm.email}
                onChange={e => setLoginForm({ ...loginForm, email: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                placeholder="Enter password"
                value={loginForm.password}
                onChange={e => setLoginForm({ ...loginForm, password: e.target.value })}
                required
              />
            </div>
            <Button type="submit" className="w-full bg-[#e58a35] hover:bg-[#d17a2e]">
              Log In
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
