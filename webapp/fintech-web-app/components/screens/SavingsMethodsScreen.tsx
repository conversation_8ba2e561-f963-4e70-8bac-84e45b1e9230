"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Calendar, Percent, RotateCcw } from "lucide-react"
import { SavingSettings } from "@/lib/types"

interface Props {
  settings: SavingSettings
  setSettings: (settings: SavingSettings) => void
  back: () => void
}

export default function SavingsMethodsScreen({ settings, setSettings, back }: Props) {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-gradient-to-r from-[#e58a35] to-amber-600 text-white p-6">
        <h1 className="text-2xl font-bold mb-2">Saving Methods</h1>
        <p className="opacity-90">Choose how you want to save</p>
      </div>
      <div className="p-4 space-y-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-[#e58a35]/10 rounded-full flex items-center justify-center">
                  <RotateCcw className="w-5 h-5 text-[#e58a35]" />
                </div>
                <div>
                  <h3 className="font-semibold">Round-ups</h3>
                  <p className="text-sm text-gray-600">Round up purchases to the nearest euro</p>
                </div>
              </div>
              <Switch checked={settings.roundups} onCheckedChange={(checked) => setSettings({ ...settings, roundups: checked })} />
            </div>
            <div className="text-sm text-gray-600">Example: €4.30 coffee → €0.70 saved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-[#e58a35]/10 rounded-full flex items-center justify-center">
                  <Percent className="w-5 h-5 text-[#e58a35]" />
                </div>
                <div>
                  <h3 className="font-semibold">Percentage of Spend</h3>
                  <p className="text-sm text-gray-600">Save a percentage of each transaction</p>
                </div>
              </div>
              <Switch checked={settings.percentage} onCheckedChange={(checked) => setSettings({ ...settings, percentage: checked })} />
            </div>
            {settings.percentage && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Percentage: {settings.percentageValue}%</span>
                </div>
                <Slider
                  value={[settings.percentageValue]}
                  onValueChange={(value) => setSettings({ ...settings, percentageValue: value[0] })}
                  max={20}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="text-sm text-gray-600">
                  Example: €20 lunch → €{((20 * settings.percentageValue) / 100).toFixed(2)} saved
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-[#e58a35]/10 rounded-full flex items-center justify-center">
                  <Calendar className="w-5 h-5 text-[#e58a35]" />
                </div>
                <div>
                  <h3 className="font-semibold">Recurring Transfers</h3>
                  <p className="text-sm text-gray-600">Automatic regular savings</p>
                </div>
              </div>
              <Switch checked={settings.recurring} onCheckedChange={(checked) => setSettings({ ...settings, recurring: checked })} />
            </div>
            {settings.recurring && (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="amount">Amount (€)</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={settings.recurringAmount}
                      onChange={(e) => setSettings({ ...settings, recurringAmount: Number.parseInt(e.target.value) || 0 })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="frequency">Frequency</Label>
                    <Select
                      value={settings.recurringFrequency}
                      onValueChange={(value) => setSettings({ ...settings, recurringFrequency: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        <Button onClick={back} className="w-full bg-[#e58a35] hover:bg-[#d17a2e]">
          Save Settings
        </Button>
      </div>
    </div>
  )
}
