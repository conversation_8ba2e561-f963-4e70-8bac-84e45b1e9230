"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { InvestmentStrategy } from "@/lib/types"

interface Props {
  goalForm: { name: string; target: string; deadline: string }
  setGoalForm: (form: { name: string; target: string; deadline: string }) => void
  selectedStrategy: InvestmentStrategy
  setSelectedStrategy: (s: InvestmentStrategy) => void
  onSubmit: (e: React.FormEvent) => void
  cancel: () => void
}

export default function AddGoalScreen({ goalForm, setGoalForm, selectedStrategy, setSelectedStrategy, onSubmit, cancel }: Props) {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-white border-b p-4">
        <h1 className="text-xl font-semibold">Add New Goal</h1>
      </div>
      <div className="p-4">
        <Card>
          <CardContent className="p-6">
            <form onSubmit={onSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="goalName">Goal Name</Label>
                <Input
                  id="goalName"
                  placeholder="e.g. Vacation, Emergency Fund"
                  value={goalForm.name}
                  onChange={(e) => setGoalForm({ ...goalForm, name: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="target">Target Amount (€)</Label>
                <Input
                  id="target"
                  type="number"
                  placeholder="2000"
                  value={goalForm.target}
                  onChange={(e) => setGoalForm({ ...goalForm, target: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deadline">Target Date</Label>
                <Input
                  id="deadline"
                  type="date"
                  value={goalForm.deadline}
                  onChange={(e) => setGoalForm({ ...goalForm, deadline: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Investment Strategy</Label>
                <Select value={selectedStrategy} onValueChange={(value: InvestmentStrategy) => setSelectedStrategy(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Fixed Income">Fixed Income (Low Risk)</SelectItem>
                    <SelectItem value="Standard">Standard (Balanced Risk)</SelectItem>
                    <SelectItem value="Growth">Growth (High Risk)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-2 pt-4">
                <Button type="button" variant="outline" onClick={cancel} className="flex-1">
                  Cancel
                </Button>
                <Button type="submit" className="flex-1 bg-[#e58a35] hover:bg-[#d17a2e]">
                  Create Goal
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
