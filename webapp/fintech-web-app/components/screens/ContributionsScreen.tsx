'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  PiggyBank,
  Coffee,
  Home,
  CreditCard,
  TrendingUp,
  User,
  RotateCcw,
  Percent,
  Calendar,
} from 'lucide-react';
import { SavingSettings, Transaction } from '@/lib/types';

interface Props {
  transactions: Transaction[];
  settings: SavingSettings;
  totalSavingsToday: number;
  navigate: (screen: string) => void;
}

export default function ContributionsScreen({
  transactions,
  settings,
  totalSavingsToday,
  navigate,
}: Props) {
  return (
    <div className="min-h-screen bg-gray-50 pb-20">
      <div className="bg-gradient-to-r from-[#e58a35] to-amber-600 text-white p-6">
        <h1 className="text-2xl font-bold mb-2">Contributions</h1>
        <p className="opacity-90">Track your daily savings</p>
      </div>
      <div className="p-4 space-y-6">
        <Card className="border-l-4 border-l-[#e58a35]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-[#e58a35]">Today&apos;s Savings</h3>
                <p className="text-2xl font-bold">€{totalSavingsToday.toFixed(2)}</p>
                <p className="text-sm text-gray-600">From {transactions.length} transactions</p>
              </div>
              <div className="w-16 h-16 bg-[#e58a35]/10 rounded-full flex items-center justify-center">
                <PiggyBank className="w-8 h-8 text-[#e58a35]" />
              </div>
            </div>
          </CardContent>
        </Card>
        <div className="space-y-4">
          <h2 className="text-lg font-semibold">Recent Activity</h2>
          {transactions.length === 0 ? (
            <Card>
              <CardContent className="p-6 text-center">
                <Coffee className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600">No transactions yet today</p>
                <p className="text-sm text-gray-500 mt-2">Start spending to see your savings!</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-3">
              {transactions.map(transaction => {
                const IconComponent = transaction.icon;
                const methodLabels = {
                  roundups: 'Round-up',
                  percentage: `${settings.percentageValue}% saved`,
                  recurring: 'Auto-save',
                };
                return (
                  <Card key={transaction.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                            <IconComponent className="w-5 h-5 text-gray-600" />
                          </div>
                          <div>
                            <p className="font-medium">{transaction.merchant}</p>
                            <div className="flex items-center gap-2">
                              <p className="text-sm text-gray-600">{transaction.time}</p>
                              <Badge variant="secondary" className="text-xs">
                                {methodLabels[transaction.method]}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">€{transaction.amount.toFixed(2)}</p>
                          <p className="text-sm text-[#e58a35] font-medium">
                            +€{transaction.savings.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Saving Methods</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <RotateCcw className="w-4 h-4 text-[#e58a35]" />
                <span>Round-ups</span>
              </div>
              <Badge variant={settings.roundups ? 'default' : 'secondary'}>
                {settings.roundups ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Percent className="w-4 h-4 text-[#e58a35]" />
                <span>Percentage ({settings.percentageValue}%)</span>
              </div>
              <Badge variant={settings.percentage ? 'default' : 'secondary'}>
                {settings.percentage ? `${settings.percentageValue}%` : 'Inactive'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-[#e58a35]" />
                <span>Recurring (€{settings.recurringAmount})</span>
              </div>
              <Badge variant={settings.recurring ? 'default' : 'secondary'}>
                {settings.recurring ? `€${settings.recurringAmount}` : 'Inactive'}
              </Badge>
            </div>
            <Button
              onClick={() => navigate('savings-methods')}
              variant="outline"
              className="w-full mt-3"
            >
              Manage Settings
            </Button>
          </CardContent>
        </Card>
      </div>
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t p-4">
        <div className="flex justify-around">
          <Button
            variant="ghost"
            onClick={() => navigate('dashboard')}
            className="flex flex-col items-center gap-1"
          >
            <Home className="w-5 h-5" />
            <span className="text-xs">Home</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => navigate('add-card')}
            className="flex flex-col items-center gap-1"
          >
            <CreditCard className="w-5 h-5" />
            <span className="text-xs">Cards</span>
          </Button>
          <Button variant="ghost" className="flex flex-col items-center gap-1 text-[#e58a35]">
            <PiggyBank className="w-5 h-5" />
            <span className="text-xs">Contributions</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => navigate('investments')}
            className="flex flex-col items-center gap-1"
          >
            <TrendingUp className="w-5 h-5" />
            <span className="text-xs">Investments</span>
          </Button>
          <Button
            variant="ghost"
            onClick={() => navigate('profile')}
            className="flex flex-col items-center gap-1"
          >
            <User className="w-5 h-5" />
            <span className="text-xs">Profile</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
