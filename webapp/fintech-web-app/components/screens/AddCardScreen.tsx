"use client"

import {<PERSON><PERSON>} from "@/components/ui/button"
import {Card, CardContent} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {CardType} from "@/lib/types"

interface Props {
    cardForm: { name: string; type: CardType; cardNumber: string; cvv: string; expirationDate: string; limit: string }
    setCardForm: (form: {
        name: string;
        type: CardType;
        cardNumber: string;
        cvv: string;
        expirationDate: string;
        limit: string
    }) => void
    onSubmit: (e: React.FormEvent) => void
    cancel: () => void
}

export default function AddCardScreen({cardForm, setCardForm, onSubmit, cancel}: Props) {
    return (
        <div className="min-h-screen bg-gray-50 pb-20">
            <div className="bg-white border-b p-4">
                <h1 className="text-xl font-semibold">Add New Card</h1>
            </div>
            <div className="p-4">
                <Card>
                    <CardContent className="p-6">
                        <form onSubmit={onSubmit} className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="cardName">Card Name</Label>
                                <Input
                                    id="cardName"
                                    placeholder="e.g. Personal, Business"
                                    value={cardForm.name}
                                    onChange={(e) => setCardForm({...cardForm, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="cardNumber">Card Number</Label>
                                <Input
                                    id="cardNumber"
                                    placeholder="0000 0000 0000 0000"
                                    value={cardForm.cardNumber}
                                    onChange={(e) => setCardForm({...cardForm, name: e.target.value})}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="cardType">Card Type</Label>
                                <Select value={cardForm.type}
                                        onValueChange={(value: CardType) => setCardForm({...cardForm, type: value})}>
                                    <SelectTrigger>
                                        <SelectValue/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="Debit">Debit</SelectItem>
                                        <SelectItem value="Credit">Credit</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="cvv">CVV</Label>
                                <Input
                                    id="cvv"
                                    placeholder="000"
                                    maxLength={3}
                                    value={cardForm.cvv}
                                    onChange={(e) => setCardForm({
                                        ...cardForm,
                                        cvv: e.target.value.replace(/\D/g, "")
                                    })}
                                    required
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="limit">Spending Limit (Optional)</Label>
                                <Input
                                    id="limit"
                                    type="number"
                                    placeholder="500"
                                    value={cardForm.limit}
                                    onChange={(e) => setCardForm({...cardForm, limit: e.target.value})}
                                />
                            </div>
                            <div className="flex gap-2 pt-4">
                                <Button type="button" variant="outline" onClick={cancel} className="flex-1">
                                    Cancel
                                </Button>
                                <Button type="submit" className="flex-1 bg-[#e58a35] hover:bg-[#d17a2e]">
                                    Add Card
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}
