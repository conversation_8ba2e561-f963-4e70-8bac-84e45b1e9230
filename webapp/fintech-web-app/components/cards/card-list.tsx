'use client';

import { useState } from 'react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { ConfirmationDialog } from '../ui/confirmation-dialog';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { BankCard } from '../../lib/types';
import { Trash2, CreditCard } from 'lucide-react';

interface CardListProps {
  cards: BankCard[];
  onRemoveCard: (cardId: string) => void;
}

export function CardList({ cards, onRemoveCard }: CardListProps) {
  const { t, currentLocale } = useTranslation();
  const [cardToRemove, setCardToRemove] = useState<string | null>(null);

  const handleRemoveCard = (cardId: string) => {
    setCardToRemove(cardId);
  };

  const confirmRemoveCard = () => {
    if (cardToRemove) {
      onRemoveCard(cardToRemove);
      setCardToRemove(null);
    }
  };

  if (cards.length === 0) {
    return (
      <div className="text-center py-8">
        <CreditCard className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p className="text-gray-500">{t('cards.title')}</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {cards.map(card => (
          <Card key={card.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{card.name}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveCard(card.id)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  aria-label={`${t('cards.remove')} ${card.name}`}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <Badge variant={card.type === 'Credit' ? 'default' : 'secondary'} className="w-fit">
                {t(`cards.${card.type.toLowerCase()}`)}
              </Badge>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-gray-500" />
                  <span className="font-mono text-sm">
                    {t('cards.lastFour', { lastFour: card.lastFour })}
                  </span>
                </div>

                <div className="text-sm text-gray-600">
                  <span>{t('cards.expirationDate')}: </span>
                  <span className="font-mono">{card.expirationDate}</span>
                </div>

                {card.type === 'Credit' && card.limit && (
                  <div className="text-sm text-gray-600">
                    <span>{t('cards.limit')}: </span>
                    <span className="font-semibold">
                      {formatCurrency(card.limit, currentLocale)}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <ConfirmationDialog
        open={!!cardToRemove}
        onOpenChange={open => !open && setCardToRemove(null)}
        title={t('cards.remove')}
        description={t('cards.removeConfirmation')}
        confirmText={t('common.delete')}
        cancelText={t('common.cancel')}
        onConfirm={confirmRemoveCard}
        variant="destructive"
      />
    </>
  );
}
