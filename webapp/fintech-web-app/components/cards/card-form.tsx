'use client';

import { useState } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { useTranslation } from '../../lib/i18n';
import { validateCardForm, formatCardNumber, formatExpirationDate } from '../../lib/validation';
import { CardFormData, CardType, ValidationError } from '../../lib/types';

interface CardFormProps {
  onSubmit: (data: CardFormData) => void;
  onCancel: () => void;
  initialData?: Partial<CardFormData>;
}

export function CardForm({ onSubmit, onCancel, initialData }: CardFormProps) {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<CardFormData>({
    name: initialData?.name || '',
    type: initialData?.type || 'Debit',
    cardNumber: initialData?.cardNumber || '',
    cvv: initialData?.cvv || '',
    expirationDate: initialData?.expirationDate || '',
    limit: initialData?.limit || '',
  });
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message;
  };

  const handleInputChange = (field: keyof CardFormData, value: string) => {
    let processedValue = value;

    if (field === 'cardNumber') {
      processedValue = formatCardNumber(value);
    } else if (field === 'expirationDate') {
      processedValue = formatExpirationDate(value);
    } else if (field === 'cvv') {
      processedValue = value.replace(/\D/g, '').substr(0, 4);
    }

    setFormData(prev => ({ ...prev, [field]: processedValue }));

    // Clear field error when user starts typing
    if (errors.some(error => error.field === field)) {
      setErrors(prev => prev.filter(error => error.field !== field));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const validationErrors = validateCardForm(formData);

    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      setIsSubmitting(false);
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting card form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t('cards.addNew')}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="cardName">{t('cards.cardName')}</Label>
            <Input
              id="cardName"
              value={formData.name}
              onChange={e => handleInputChange('name', e.target.value)}
              placeholder={t('cards.cardName')}
              aria-invalid={!!getFieldError('name')}
              aria-describedby={getFieldError('name') ? 'cardName-error' : undefined}
            />
            {getFieldError('name') && (
              <p id="cardName-error" className="text-sm text-red-600" role="alert">
                {t(getFieldError('name')!)}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="cardType">{t('cards.cardType')}</Label>
            <Select
              value={formData.type}
              onValueChange={(value: CardType) => handleInputChange('type', value)}
            >
              <SelectTrigger id="cardType">
                <SelectValue placeholder={t('cards.cardType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Debit">{t('cards.debit')}</SelectItem>
                <SelectItem value="Credit">{t('cards.credit')}</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cardNumber">{t('cards.cardNumber')}</Label>
            <Input
              id="cardNumber"
              value={formData.cardNumber}
              onChange={e => handleInputChange('cardNumber', e.target.value)}
              placeholder="1234 5678 9012 3456"
              maxLength={19}
              aria-invalid={!!getFieldError('cardNumber')}
              aria-describedby={getFieldError('cardNumber') ? 'cardNumber-error' : undefined}
            />
            {getFieldError('cardNumber') && (
              <p id="cardNumber-error" className="text-sm text-red-600" role="alert">
                {t(getFieldError('cardNumber')!)}
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="expirationDate">{t('cards.expirationDate')}</Label>
              <Input
                id="expirationDate"
                value={formData.expirationDate}
                onChange={e => handleInputChange('expirationDate', e.target.value)}
                placeholder="MM/YY"
                maxLength={5}
                aria-invalid={!!getFieldError('expirationDate')}
                aria-describedby={
                  getFieldError('expirationDate') ? 'expirationDate-error' : undefined
                }
              />
              {getFieldError('expirationDate') && (
                <p id="expirationDate-error" className="text-sm text-red-600" role="alert">
                  {t(getFieldError('expirationDate')!)}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="cvv">{t('cards.cvv')}</Label>
              <Input
                id="cvv"
                type="password"
                value={formData.cvv}
                onChange={e => handleInputChange('cvv', e.target.value)}
                placeholder="123"
                maxLength={4}
                aria-invalid={!!getFieldError('cvv')}
                aria-describedby={getFieldError('cvv') ? 'cvv-error' : undefined}
              />
              {getFieldError('cvv') && (
                <p id="cvv-error" className="text-sm text-red-600" role="alert">
                  {t(getFieldError('cvv')!)}
                </p>
              )}
            </div>
          </div>

          {formData.type === 'Credit' && (
            <div className="space-y-2">
              <Label htmlFor="limit">{t('cards.limit')}</Label>
              <Input
                id="limit"
                type="number"
                value={formData.limit}
                onChange={e => handleInputChange('limit', e.target.value)}
                placeholder="5000"
                min="0"
                step="100"
              />
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
              {t('cards.cancel')}
            </Button>
            <Button type="submit" disabled={isSubmitting} className="flex-1">
              {isSubmitting ? t('common.loading') : t('cards.save')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
