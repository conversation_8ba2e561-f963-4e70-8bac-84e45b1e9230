'use client'

import { useState } from 'react'
import { But<PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { useTranslation } from '../../lib/i18n'
import { validateGoalForm } from '../../lib/validation'
import { GoalFormData, InvestmentStrategy, ValidationError } from '../../lib/types'

interface GoalFormProps {
  onSubmit: (data: GoalFormData & { strategy: InvestmentStrategy }) => void
  onCancel: () => void
  initialData?: Partial<GoalFormData & { strategy: InvestmentStrategy }>
}

export function GoalForm({ onSubmit, onCancel, initialData }: GoalFormProps) {
  const { t } = useTranslation()
  const [formData, setFormData] = useState<GoalFormData & { strategy: InvestmentStrategy }>({
    name: initialData?.name || '',
    target: initialData?.target || '',
    deadline: initialData?.deadline || '',
    strategy: initialData?.strategy || 'Standard',
  })
  const [errors, setErrors] = useState<ValidationError[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)

  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message
  }

  const handleInputChange = (field: keyof (GoalFormData & { strategy: InvestmentStrategy }), value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear field error when user starts typing
    if (errors.some(error => error.field === field)) {
      setErrors(prev => prev.filter(error => error.field !== field))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    const validationErrors = validateGoalForm(formData)
    
    if (validationErrors.length > 0) {
      setErrors(validationErrors)
      setIsSubmitting(false)
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('Error submitting goal form:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format date for input (YYYY-MM-DD)
  const formatDateForInput = (dateString: string) => {
    if (!dateString) return ''
    const date = new Date(dateString)
    return date.toISOString().split('T')[0]
  }

  // Get minimum date (tomorrow)
  const getMinDate = () => {
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    return tomorrow.toISOString().split('T')[0]
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>{t('goals.addNew')}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="goalName">{t('goals.goalName')}</Label>
            <Input
              id="goalName"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder={t('goals.goalName')}
              aria-invalid={!!getFieldError('name')}
              aria-describedby={getFieldError('name') ? 'goalName-error' : undefined}
            />
            {getFieldError('name') && (
              <p id="goalName-error" className="text-sm text-red-600" role="alert">
                {t(getFieldError('name')!)}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="targetAmount">{t('goals.targetAmount')}</Label>
            <Input
              id="targetAmount"
              type="number"
              value={formData.target}
              onChange={(e) => handleInputChange('target', e.target.value)}
              placeholder="10000"
              min="1"
              step="0.01"
              aria-invalid={!!getFieldError('target')}
              aria-describedby={getFieldError('target') ? 'targetAmount-error' : undefined}
            />
            {getFieldError('target') && (
              <p id="targetAmount-error" className="text-sm text-red-600" role="alert">
                {t(getFieldError('target')!)}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="deadline">{t('goals.deadline')}</Label>
            <Input
              id="deadline"
              type="date"
              value={formatDateForInput(formData.deadline)}
              onChange={(e) => handleInputChange('deadline', e.target.value)}
              min={getMinDate()}
              aria-invalid={!!getFieldError('deadline')}
              aria-describedby={getFieldError('deadline') ? 'deadline-error' : undefined}
            />
            {getFieldError('deadline') && (
              <p id="deadline-error" className="text-sm text-red-600" role="alert">
                {t(getFieldError('deadline')!)}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="strategy">{t('goals.strategy')}</Label>
            <Select
              value={formData.strategy}
              onValueChange={(value: InvestmentStrategy) => handleInputChange('strategy', value)}
            >
              <SelectTrigger id="strategy">
                <SelectValue placeholder={t('goals.strategy')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Fixed Income">
                  {t('goals.strategies.fixedIncome')}
                </SelectItem>
                <SelectItem value="Standard">
                  {t('goals.strategies.standard')}
                </SelectItem>
                <SelectItem value="Growth">
                  {t('goals.strategies.growth')}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
            >
              {t('goals.cancel')}
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1"
            >
              {isSubmitting ? t('common.loading') : t('goals.save')}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
