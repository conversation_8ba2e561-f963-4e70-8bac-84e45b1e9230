'use client'

import { useState } from 'react'
import { But<PERSON> } from "../ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card"
import { Badge } from "../ui/badge"
import { Progress } from "../ui/progress"
import { ConfirmationDialog } from "../ui/confirmation-dialog"
import { useTranslation, formatCurrency } from '../../lib/i18n'
import { Goal } from '../../lib/types'
import { Target, Calendar, TrendingUp, Edit } from 'lucide-react'

interface GoalListProps {
  goals: Goal[]
  onEditGoal: (goal: Goal) => void
}

export function GoalList({ goals, onEditGoal }: GoalListProps) {
  const { t, currentLocale } = useTranslation()
  const [goalToEdit, setGoalToEdit] = useState<Goal | null>(null)

  const handleEditGoal = (goal: Goal) => {
    if (goal.isActive) {
      setGoalToEdit(goal)
    } else {
      onEditGoal(goal)
    }
  }

  const confirmEditGoal = () => {
    if (goalToEdit) {
      onEditGoal(goalToEdit)
      setGoalToEdit(null)
    }
  }

  const getProgressPercentage = (saved: number, target: number) => {
    return Math.min((saved / target) * 100, 100)
  }

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'Fixed Income':
        return 'bg-blue-100 text-blue-800'
      case 'Standard':
        return 'bg-green-100 text-green-800'
      case 'Growth':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDeadline = (deadline: Date) => {
    return new Intl.DateTimeFormat(currentLocale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(deadline))
  }

  if (goals.length === 0) {
    return (
      <div className="text-center py-8">
        <Target className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <p className="text-gray-500">{t('goals.title')}</p>
      </div>
    )
  }

  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {goals.map((goal) => {
          const progressPercentage = getProgressPercentage(goal.saved, goal.target)
          const remaining = goal.target - goal.saved

          return (
            <Card key={goal.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{goal.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    {goal.isActive && (
                      <Badge variant="default" className="text-xs">
                        Active
                      </Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditGoal(goal)}
                      aria-label={`${t('common.edit')} ${goal.name}`}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Badge 
                  className={`w-fit ${getStrategyColor(goal.strategy)}`}
                  variant="secondary"
                >
                  {t(`goals.strategies.${goal.strategy.toLowerCase().replace(' ', '')}`)}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{t('goals.progress')}</span>
                    <span>{progressPercentage.toFixed(1)}%</span>
                  </div>
                  <Progress value={progressPercentage} className="h-2" />
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="flex items-center gap-1 text-gray-600 mb-1">
                      <TrendingUp className="h-3 w-3" />
                      <span>{t('goals.saved')}</span>
                    </div>
                    <div className="font-semibold">
                      {formatCurrency(goal.saved, currentLocale)}
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center gap-1 text-gray-600 mb-1">
                      <Target className="h-3 w-3" />
                      <span>{t('goals.remaining')}</span>
                    </div>
                    <div className="font-semibold">
                      {formatCurrency(remaining, currentLocale)}
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex items-center gap-1 text-gray-600 text-sm">
                    <Calendar className="h-3 w-3" />
                    <span>{t('goals.deadline')}: </span>
                    <span className="font-medium">
                      {formatDeadline(goal.deadline)}
                    </span>
                  </div>
                </div>

                <div className="text-lg font-bold text-center pt-2">
                  {formatCurrency(goal.target, currentLocale)}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <ConfirmationDialog
        open={!!goalToEdit}
        onOpenChange={(open) => !open && setGoalToEdit(null)}
        title={t('goals.changeGoalConfirmation')}
        description={t('goals.changeGoalConfirmation')}
        confirmText={t('common.confirm')}
        cancelText={t('common.cancel')}
        onConfirm={confirmEditGoal}
        variant="default"
      />
    </>
  )
}
