'use client';

import { But<PERSON> } from './button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu';
import { useTranslation } from '../../lib/i18n';
import { Globe } from 'lucide-react';

const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
  { code: 'pl', name: '<PERSON><PERSON>', flag: '🇵🇱' },
];

export function LanguageSwitcher() {
  const { changeLanguage, currentLocale } = useTranslation();

  const currentLanguage = languages.find(lang => lang.code === currentLocale) || languages[0];

  return (
    <DropdownMenu>
      {/*<DropdownMenuTrigger asChild>*/}
      {/*  <Button variant="ghost" size="sm" className="gap-2">*/}
      {/*    <Globe className="h-4 w-4" />*/}
      {/*    <span className="hidden sm:inline">{currentLanguage.flag} {currentLanguage.name}</span>*/}
      {/*    <span className="sm:hidden">{currentLanguage.flag}</span>*/}
      {/*  </Button>*/}
      {/*</DropdownMenuTrigger>*/}
      {/*<DropdownMenuContent align="end">*/}
      {/*  {languages.map((language) => (*/}
      {/*    <DropdownMenuItem*/}
      {/*      key={language.code}*/}
      {/*      onClick={() => changeLanguage(language.code)}*/}
      {/*      className={currentLocale === language.code ? 'bg-accent' : ''}*/}
      {/*    >*/}
      {/*      <span className="mr-2">{language.flag}</span>*/}
      {/*      {language.name}*/}
      {/*    </DropdownMenuItem>*/}
      {/*  ))}*/}
      {/*</DropdownMenuContent>*/}
    </DropdownMenu>
  );
}
