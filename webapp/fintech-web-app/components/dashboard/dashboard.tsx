'use client';

import { <PERSON><PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { LanguageSwitcher } from '../ui/language-switcher';
import { useTranslation, formatCurrency } from '../../lib/i18n';
import { Goal, Transaction, SavingSettings } from '../../lib/types';
import { PiggyBank, TrendingUp, Target, CreditCard, Plus, Settings, LogOut } from 'lucide-react';

interface DashboardProps {
  totalSavings: number;
  monthlyContribution: number;
  activeGoals: Goal[];
  recentTransactions: Transaction[];
  savingSettings: SavingSettings;
  onAddCard: () => void;
  onAddGoal: () => void;
  onViewInvestments: () => void;
  onViewCards: () => void;
  onViewGoals: () => void;
  onViewProfile: () => void;
  onLogout: () => void;
}

export function Dashboard({
  totalSavings,
  monthlyContribution,
  activeGoals,
  recentTransactions,
  savingSettings,
  onAddCard,
  onAddGoal,
  onViewInvestments,
  onViewCards,
  onViewGoals,
  onViewProfile,
  onLogout,
}: DashboardProps) {
  const { t, currentLocale } = useTranslation();

  const formatTime = (time: string) => {
    return new Intl.DateTimeFormat(currentLocale, {
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(time));
  };

  const getSavingMethodBadge = (method: string) => {
    switch (method) {
      case 'roundups':
        return <Badge variant="secondary">{t('savings.roundups')}</Badge>;
      case 'percentage':
        return <Badge variant="outline">{t('savings.percentage')}</Badge>;
      case 'recurring':
        return <Badge variant="default">{t('savings.recurring')}</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{t('app.title')}</h1>
              <p className="text-sm text-gray-600">{t('app.subtitle')}</p>
            </div>

            <div className="flex items-center gap-4">
              <LanguageSwitcher />
              <Button variant="ghost" size="sm" onClick={onViewProfile} className="gap-2">
                <Settings className="h-4 w-4" />
                <span className="hidden sm:inline">{t('navigation.profile')}</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onLogout}
                className="gap-2 text-red-600 hover:text-red-700"
              >
                <LogOut className="h-4 w-4" />
                <span className="hidden sm:inline">{t('auth.logout')}</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboard.totalSavings')}</CardTitle>
              <PiggyBank className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(totalSavings, currentLocale)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {t('dashboard.monthlyContribution')}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(monthlyContribution, currentLocale)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('dashboard.activeGoals')}</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeGoals.length}</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('dashboard.quickActions')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={onAddCard} className="w-full justify-start gap-2" variant="outline">
                <CreditCard className="h-4 w-4" />
                {t('dashboard.addCard')}
              </Button>

              <Button onClick={onAddGoal} className="w-full justify-start gap-2" variant="outline">
                <Plus className="h-4 w-4" />
                {t('dashboard.addGoal')}
              </Button>

              <Button
                onClick={onViewInvestments}
                className="w-full justify-start gap-2"
                variant="outline"
              >
                <TrendingUp className="h-4 w-4" />
                {t('dashboard.viewInvestments')}
              </Button>

              <div className="grid grid-cols-2 gap-2 pt-4">
                <Button onClick={onViewCards} variant="secondary" size="sm">
                  {t('navigation.cards')}
                </Button>
                <Button onClick={onViewGoals} variant="secondary" size="sm">
                  {t('navigation.goals')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle>{t('dashboard.recentTransactions')}</CardTitle>
            </CardHeader>
            <CardContent>
              {recentTransactions.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No recent transactions</p>
              ) : (
                <div className="space-y-4">
                  {recentTransactions.slice(0, 5).map(transaction => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <transaction.icon className="h-8 w-8 text-gray-400" />
                        <div>
                          <p className="font-medium">{transaction.merchant}</p>
                          <p className="text-sm text-gray-600">{formatTime(transaction.time)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {formatCurrency(transaction.amount, currentLocale)}
                        </p>
                        <div className="flex items-center gap-2">
                          <p className="text-sm text-green-600">
                            +{formatCurrency(transaction.savings, currentLocale)}
                          </p>
                          {getSavingMethodBadge(transaction.method)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
