#!/bin/bash

# Real Auth0 E2E Test Runner
# This script sets up the complete environment for testing real Auth0 authentication flows

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_PORT=3000
BACKEND_PORT=8000
AUTH0_PORT=3002
POSTGRES_PORT=5432

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -ti:$port > /dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    echo -e "${YELLOW}⏳ Waiting for $service_name to be ready...${NC}"
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}   Attempt $attempt/$max_attempts - $service_name not ready yet...${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start within expected time${NC}"
    return 1
}

# Function to cleanup processes
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill background processes
    if [ ! -z "$FRONTEND_PID" ]; then
        echo -e "${YELLOW}🔄 Stopping frontend (PID: $FRONTEND_PID)...${NC}"
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$BACKEND_PID" ]; then
        echo -e "${YELLOW}🔄 Stopping backend (PID: $BACKEND_PID)...${NC}"
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    # Stop Docker services
    echo -e "${YELLOW}🔄 Stopping Docker services...${NC}"
    cd ../../services/api
    docker compose -f docker-compose.dev.yml down > /dev/null 2>&1 || true
    cd - > /dev/null
    
    # Kill any remaining processes on our ports
    for port in $FRONTEND_PORT $BACKEND_PORT $AUTH0_PORT; do
        if check_port $port; then
            echo -e "${YELLOW}🔄 Killing process on port $port...${NC}"
            lsof -ti:$port | xargs kill -9 2>/dev/null || true
        fi
    done
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Set up cleanup trap
trap cleanup EXIT

echo -e "${BLUE}🚀 Starting Real Auth0 E2E Test Environment${NC}"
echo -e "${BLUE}===========================================${NC}"

# Step 1: Clean up any existing processes
echo -e "\n${YELLOW}🧹 Initial cleanup...${NC}"
cleanup

# Step 2: Start PostgreSQL and Auth0 Mock with Docker
echo -e "\n${YELLOW}🐳 Starting Docker services (PostgreSQL + Auth0 Mock)...${NC}"
cd ../../services/api

# Ensure we have the latest docker-compose configuration
if [ ! -f "docker-compose.dev.yml" ]; then
    echo -e "${RED}❌ docker-compose.dev.yml not found in services/api${NC}"
    exit 1
fi

# Start Docker services
docker compose -f docker-compose.dev.yml up -d

# Wait for PostgreSQL to be ready
wait_for_service "http://localhost:$POSTGRES_PORT" "PostgreSQL" || {
    echo -e "${RED}❌ PostgreSQL failed to start${NC}"
    exit 1
}

# Wait for Auth0 Mock to be ready
wait_for_service "http://localhost:$AUTH0_PORT" "Auth0 Mock" || {
    echo -e "${RED}❌ Auth0 Mock failed to start${NC}"
    exit 1
}

echo -e "${GREEN}✅ Docker services are running${NC}"

# Step 3: Start Backend API
echo -e "\n${YELLOW}🔄 Starting backend API...${NC}"
cd src/fintech/api

# Check if we have the required dependencies
if [ ! -f "app.py" ]; then
    echo -e "${RED}❌ Backend app.py not found${NC}"
    exit 1
fi

# Start backend in background
echo -e "${YELLOW}🔄 Starting FastAPI backend...${NC}"
uv run uvicorn app:app --reload --host 0.0.0.0 --port $BACKEND_PORT > /tmp/backend.log 2>&1 &
BACKEND_PID=$!

# Wait for backend to be ready
wait_for_service "http://localhost:$BACKEND_PORT/health" "Backend API" || {
    echo -e "${RED}❌ Backend API failed to start${NC}"
    echo -e "${RED}Backend logs:${NC}"
    tail -20 /tmp/backend.log
    exit 1
}

echo -e "${GREEN}✅ Backend API is running on port $BACKEND_PORT${NC}"

# Step 4: Start Frontend
cd ../../../../webapp/fintech-web-app

# Check if we have the required dependencies
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Frontend package.json not found${NC}"
    exit 1
fi

# Kill any process using the frontend port
if check_port $FRONTEND_PORT; then
    echo -e "${YELLOW}🔄 Killing process on port $FRONTEND_PORT...${NC}"
    lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    sleep 2
fi

# Start frontend in background with explicit port
echo -e "${YELLOW}🔄 Starting frontend server...${NC}"
PORT=$FRONTEND_PORT npm run dev > /tmp/frontend.log 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to be ready
wait_for_service "http://localhost:$FRONTEND_PORT" "Frontend" || {
    echo -e "${RED}❌ Frontend failed to start${NC}"
    echo -e "${RED}Frontend logs:${NC}"
    tail -20 /tmp/frontend.log
    exit 1
}

echo -e "${GREEN}✅ Frontend is running on port $FRONTEND_PORT${NC}"

# Step 5: Verify all services are working
echo -e "\n${BLUE}🔍 Verifying all services...${NC}"

# Test PostgreSQL connection
echo -e "${YELLOW}🔄 Testing PostgreSQL connection...${NC}"
if curl -f -s "http://localhost:$BACKEND_PORT/health" | grep -q "database.*connected"; then
    echo -e "${GREEN}✅ PostgreSQL connection verified${NC}"
else
    echo -e "${RED}❌ PostgreSQL connection failed${NC}"
    exit 1
fi

# Test Auth0 Mock
echo -e "${YELLOW}🔄 Testing Auth0 Mock...${NC}"
if curl -f -s "http://localhost:$AUTH0_PORT/.well-known/openid_configuration" > /dev/null; then
    echo -e "${GREEN}✅ Auth0 Mock is responding${NC}"
else
    echo -e "${RED}❌ Auth0 Mock is not responding${NC}"
    exit 1
fi

# Test Backend API
echo -e "${YELLOW}🔄 Testing Backend API...${NC}"
if curl -f -s "http://localhost:$BACKEND_PORT/" | grep -q "FinTech API"; then
    echo -e "${GREEN}✅ Backend API is responding${NC}"
else
    echo -e "${RED}❌ Backend API is not responding${NC}"
    exit 1
fi

# Test Frontend
echo -e "${YELLOW}🔄 Testing Frontend...${NC}"
if curl -f -s "http://localhost:$FRONTEND_PORT" > /dev/null; then
    echo -e "${GREEN}✅ Frontend is responding${NC}"
else
    echo -e "${RED}❌ Frontend is not responding${NC}"
    exit 1
fi

# Step 6: Run the Auth0 E2E tests
echo -e "\n${GREEN}🎭 All services are ready! Running Auth0 E2E tests...${NC}"
echo -e "${BLUE}============================================${NC}"

# Run the specific Auth0 real flow tests
npm run test:e2e:auth0

# Check test results
if [ $? -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All Auth0 E2E tests passed!${NC}"
    echo -e "${GREEN}✅ Real Auth0 authentication flow is working correctly${NC}"
    echo -e "${GREEN}✅ User registration and onboarding flow is working${NC}"
    echo -e "${GREEN}✅ Backend API authentication is working${NC}"
    echo -e "${GREEN}✅ Database integration is working${NC}"
else
    echo -e "\n${RED}❌ Some Auth0 E2E tests failed${NC}"
    echo -e "${RED}Check the test output above for details${NC}"
    exit 1
fi

echo -e "\n${BLUE}🏁 Auth0 E2E Test Run Complete!${NC}"
echo -e "${BLUE}================================${NC}"
echo -e "${GREEN}✅ Environment: PostgreSQL + Auth0 Mock + Backend API + Frontend${NC}"
echo -e "${GREEN}✅ Authentication: Real Auth0 flow with JWT tokens${NC}"
echo -e "${GREEN}✅ Database: User onboarding and data persistence${NC}"
echo -e "${GREEN}✅ API: Protected endpoints with Auth0 authentication${NC}"

# Keep services running for manual testing if desired
echo -e "\n${YELLOW}💡 Services are still running for manual testing:${NC}"
echo -e "${YELLOW}   Frontend: http://localhost:$FRONTEND_PORT${NC}"
echo -e "${YELLOW}   Backend API: http://localhost:$BACKEND_PORT${NC}"
echo -e "${YELLOW}   Auth0 Mock: http://localhost:$AUTH0_PORT${NC}"
echo -e "${YELLOW}   API Docs: http://localhost:$BACKEND_PORT/docs${NC}"
echo -e "\n${YELLOW}Press Ctrl+C to stop all services${NC}"

# Wait for user to stop services
wait
