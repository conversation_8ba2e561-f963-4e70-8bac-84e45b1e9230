#!/bin/bash

# Start Local Auth0 Environment
# Usage: ./start-local-auth.sh [dev|test]
# Default: dev

ENVIRONMENT=${1:-dev}

echo "🚀 Starting Local Auth0 Environment for $ENVIRONMENT..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker compose is available
if ! command -v docker compose >/dev/null 2>&1; then
    echo "❌ docker compose is not available. Please install docker compose."
    exit 1
fi

# Set compose file based on environment
if [ "$ENVIRONMENT" = "test" ]; then
    COMPOSE_FILE="docker-compose.test.yml"
    SERVICE_NAME="localauth0-test"
    echo "🧪 Using test environment configuration"
else
    COMPOSE_FILE="docker-compose.yml"
    SERVICE_NAME="localauth0"
    echo "🔧 Using development environment configuration"
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose -f $COMPOSE_FILE down

# Start the local Auth0 mock
echo "🔧 Starting Local Auth0 Mock..."
docker compose -f $COMPOSE_FILE up -d $SERVICE_NAME

# Wait for Auth0 mock to be ready
echo "⏳ Waiting for Local Auth0 to be ready..."
timeout=60
counter=0

while [ $counter -lt $timeout ]; do
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "✅ Local Auth0 is ready!"
        break
    fi

    if [ $counter -eq 30 ]; then
        echo "⏳ Still waiting for Local Auth0..."
    fi

    sleep 1
    counter=$((counter + 1))
done

if [ $counter -eq $timeout ]; then
    echo "❌ Local Auth0 failed to start within $timeout seconds"
    echo "📋 Checking container logs..."
    docker compose -f $COMPOSE_FILE logs $SERVICE_NAME
    exit 1
fi

# Display Auth0 configuration
echo ""
echo "🎉 Local Auth0 Environment is ready!"
echo ""
echo "📋 Configuration:"
echo "   Auth0 URL: http://localhost:3001"
echo "   Client ID: my-client-id"
echo "   Client Secret: my-client-secret"
echo "   Audience: my-api"
echo ""
echo "👤 Test User:"
echo "   Email: <EMAIL>"
echo "   Password: Test1234"
echo ""
echo "🔑 Permissions: read:profile, read:dashboard, read:investments"
echo ""
echo "🌐 You can now start your Next.js app with:"
echo "   npm run dev"
echo ""
echo "🧪 Run E2E tests with:"
echo "   npm run test:e2e"
echo ""
echo "📊 View container status:"
echo "   docker-compose ps"
echo ""
echo "📝 View logs:"
echo "   docker-compose logs -f localauth0"
echo ""
