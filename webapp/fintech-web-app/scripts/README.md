# Development Scripts

This directory contains scripts to manage the development environment and run E2E tests.

## Scripts Overview

### 🚀 `start-dev-environment.sh`

Starts the complete development environment with all necessary services.

**What it does:**

1. Starts PostgreSQL + Auth0 Mock (Docker)
2. Starts Backend API (FastAPI) [optional]
3. Starts Frontend (Next.js) [optional]

**Usage:**

```bash
# Start all services
./scripts/start-dev-environment.sh

# Start only Docker services (PostgreSQL + Auth0)
./scripts/start-dev-environment.sh --services-only

# Start Docker services + API (no frontend)
./scripts/start-dev-environment.sh --api-only

# Start all and wait (Ctrl+C to stop)
./scripts/start-dev-environment.sh --wait
```

**Environment Variables:**

```bash
START_API=false      # Skip starting the API
START_FRONTEND=false # Skip starting the frontend
WAIT=true           # Wait and watch (Ctrl+C to stop)
```

### 🧪 `run-e2e-tests.sh`

Runs the complete E2E test suite with full environment setup.

**What it does:**

1. Starts PostgreSQL + Auth0 Mock (Docker)
2. Starts Backend API (FastAPI)
3. Seeds the development database
4. Starts Frontend (Next.js)
5. Runs Playwright E2E tests
6. Cleans up all services

**Usage:**

```bash
# Run full E2E test suite
./scripts/run-e2e-tests.sh

# Show help
./scripts/run-e2e-tests.sh --help
```

## Quick Commands

### Using npm scripts:

```bash
# Development
npm run dev:start      # Start all services
npm run dev:services   # Start only Docker services
npm run dev:api        # Start Docker + API

# Testing
npm run test:e2e:full  # Full E2E test suite
npm run test:e2e:quick # Quick E2E tests (assumes services running)

# Backend management
npm run backend:start  # Start backend services
npm run backend:stop   # Stop backend services
```

### Using just commands:

```bash
# Development
just dev-start         # Start all services
just services-start    # Start only Docker services
just api-start         # Start Docker + API

# Testing
just test-e2e-full     # Full E2E test suite
just test-e2e          # Quick E2E tests

# Backend management
just backend-start     # Start backend services
just backend-stop      # Stop backend services
```

## Service Ports

| Service     | Port | URL                        |
| ----------- |------|----------------------------|
| Frontend    | 3000 | http://localhost:3000      |
| Backend API | 8000 | http://localhost:8000      |
| API Docs    | 8000 | http://localhost:8000/docs |
| Auth0 Mock  | 3002 | http://localhost:3002      |
| PostgreSQL  | 5434 | localhost:5434             |

## Test User Credentials

For testing and development:

- **Email:** `<EMAIL>`
- **Password:** `Test1234`

## Environment Configuration

### Auth0 Mock Configuration

- **Domain:** `localhost:3001`
- **Client ID:** `my-client-id`
- **Client Secret:** `my-client-secret`
- **Audience:** `my-api`
- **Issuer:** `http://localhost:3002`

### Database Configuration

- **Host:** `localhost`
- **Port:** `5434`
- **Database:** `fintech_dev`
- **User:** `dev_user`
- **Password:** `dev_password`

## Troubleshooting

### Common Issues

1. **Port conflicts:**

   ```bash
   # Check what's using a port
   lsof -ti:3000

   # Kill process using port
   kill $(lsof -ti:3000)
   ```

2. **Docker not running:**

   ```bash
   # Check Docker status
   docker info

   # Start Docker Desktop or Docker daemon
   ```

3. **Services not starting:**

   ```bash
   # Check service logs
   cd ../../services/api
   just logs

   # Or check specific service
   just logs postgres-dev
   just logs localauth0-dev
   ```

4. **Database connection issues:**

   ```bash
   # Reset database
   cd ../../services/api
   just dev-stop
   just dev-start
   ```

5. **Frontend build issues:**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

### Cleanup Commands

```bash
# Stop all services
npm run backend:stop

# Clean Docker resources
npm run clean

# Full cleanup (from services/api)
cd ../../services/api
just clean
```

### Log Files

When running in background mode, logs are saved to:

- **API logs:** `/tmp/api.log`
- **Frontend logs:** `/tmp/frontend.log`
- **Process IDs:** `/tmp/api.pid`, `/tmp/frontend.pid`

### Manual Service Management

If you need to start services manually:

```bash
# 1. Start Docker services
cd ../../services/api
docker compose -f docker-compose.dev.yml up -d

# 2. Start API
cd ../../services/api
uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000

# 3. Start Frontend
cd webapp/fintech-web-app
npm run dev
```

## Development Workflow

### Typical Development Session

1. **Start services:**

   ```bash
   npm run dev:start
   ```

2. **Develop and test:**
   - Frontend: http://localhost:3000
   - API: http://localhost:8000/docs
   - Make changes to code

3. **Run tests:**

   ```bash
   npm run test:e2e:quick  # Quick tests
   # or
   npm run test:e2e:full   # Full test suite
   ```

4. **Stop services:**
   ```bash
   npm run backend:stop
   ```

### E2E Testing Workflow

1. **Run full test suite:**

   ```bash
   npm run test:e2e:full
   ```

   This automatically starts all services, runs tests, and cleans up.

2. **Debug tests:**
   ```bash
   npm run dev:start       # Start services
   npm run test:e2e:debug  # Debug mode
   npm run backend:stop    # Stop when done
   ```

## Script Dependencies

### Required Tools

- **Node.js** (v18+)
- **npm** or **yarn**
- **Docker** and **Docker Compose**
- **uv** (Python package manager)
- **curl** (for health checks)
- **just** (command runner)

### Installation Check

```bash
# Check if all tools are available
node --version
npm --version
docker --version
docker compose version
uv --version
curl --version
just --version
```
