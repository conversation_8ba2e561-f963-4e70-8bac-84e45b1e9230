#!/bin/bash

# Start Full Development Environment
echo "🚀 Starting Full Development Environment..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker compose is available
if ! command -v docker compose >/dev/null 2>&1; then
    echo "❌ docker compose is not available. Please install docker compose."
    exit 1
fi

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose down

# Start all development services
echo "🔧 Starting development services..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."

# Wait for Auth0 mock
timeout=60
counter=0
echo "  🔐 Checking Auth0 mock..."
while [ $counter -lt $timeout ]; do
    if curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "  ✅ Auth0 mock is ready!"
        break
    fi
    sleep 1
    counter=$((counter + 1))
done

if [ $counter -eq $timeout ]; then
    echo "  ❌ Auth0 mock failed to start"
    docker compose logs localauth0
    exit 1
fi

# Wait for PostgreSQL
counter=0
echo "  🐘 Checking PostgreSQL..."
while [ $counter -lt $timeout ]; do
    if docker compose exec postgres-dev pg_isready -U dev_user -d fintech_dev > /dev/null 2>&1; then
        echo "  ✅ PostgreSQL is ready!"
        break
    fi
    sleep 1
    counter=$((counter + 1))
done

if [ $counter -eq $timeout ]; then
    echo "  ❌ PostgreSQL failed to start"
    docker compose logs postgres-dev
    exit 1
fi

# Display environment information
echo ""
echo "🎉 Development Environment is ready!"
echo ""
echo "📋 Services:"
echo "   🔐 Auth0 Mock: http://localhost:3001"
echo "   🐘 PostgreSQL: localhost:5434"
echo "   📊 Database: fintech_dev"
echo "   👤 DB User: dev_user"
echo ""
echo "👤 Test User:"
echo "   Email: <EMAIL>"
echo "   Password: Test1234"
echo ""
echo "🔑 Permissions: read:profile, read:dashboard, read:investments"
echo ""
echo "🌐 Start your Next.js app:"
echo "   npm run dev"
echo ""
echo "🧪 Run E2E tests:"
echo "   npm run test:e2e"
echo ""
echo "📊 View services status:"
echo "   docker compose ps"
echo ""
echo "📝 View logs:"
echo "   docker compose logs -f"
echo ""
