#!/bin/bash

# E2E Test Runner Script
# This script starts all necessary services and runs E2E tests

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKEND_DIR="../../services/api"
FRONTEND_PORT=3000
BACKEND_PORT=8000
AUTH0_PORT=3001
POSTGRES_PORT=5434
TIMEOUT=120

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill background processes
    if [[ -n "${FRONTEND_PID:-}" ]]; then
        echo "Stopping frontend server (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    if [[ -n "${BACKEND_PID:-}" ]]; then
        echo "Stopping backend server (PID: $BACKEND_PID)..."
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    # Stop Docker services
    echo "Stopping Docker services..."
    cd "$BACKEND_DIR" && docker compose -f docker-compose.dev.yml down || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

# Set trap to cleanup on exit
trap cleanup EXIT

# Function to wait for service
wait_for_service() {
    local service_name="$1"
    local url="$2"
    local timeout="$3"
    
    echo -e "${BLUE}⏳ Waiting for $service_name to be ready...${NC}"
    
    local counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ $service_name is ready!${NC}"
            return 0
        fi
        
        if [ $((counter % 10)) -eq 0 ] && [ $counter -gt 0 ]; then
            echo -e "${YELLOW}⏳ Still waiting for $service_name... (${counter}s)${NC}"
        fi
        
        sleep 1
        counter=$((counter + 1))
    done
    
    echo -e "${RED}❌ $service_name failed to start within ${timeout}s${NC}"
    return 1
}

# Function to check if port is in use
check_port() {
    local port="$1"
    local service="$2"
    
    if lsof -ti:$port > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  Port $port is already in use (may be $service)${NC}"
        return 0
    fi
    return 1
}

# Main execution
main() {
    echo -e "${BLUE}🚀 Starting E2E Test Environment${NC}"
    echo "=================================="
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
        exit 1
    fi
    
    # Check if required commands exist
    for cmd in node npm docker curl; do
        if ! command -v $cmd >/dev/null 2>&1; then
            echo -e "${RED}❌ Required command '$cmd' not found${NC}"
            exit 1
        fi
    done
    
    # Step 1: Start Backend Services (PostgreSQL + Auth0)
    echo -e "\n${BLUE}🔧 Step 1: Starting backend services...${NC}"
    cd "$BACKEND_DIR"
    
    # Stop any existing services
    docker compose -f docker-compose.dev.yml down || true
    
    # Start backend services
    docker compose -f docker-compose.dev.yml up -d
    
    # Wait for PostgreSQL
    wait_for_service "PostgreSQL" "http://localhost:$POSTGRES_PORT" 30 || {
        echo -e "${RED}❌ PostgreSQL failed to start${NC}"
        docker compose -f docker-compose.dev.yml logs postgres-dev
        exit 1
    }
    
    # Wait for Auth0 Mock
    wait_for_service "Auth0 Mock" "http://localhost:$AUTH0_PORT/health" 60 || {
        echo -e "${RED}❌ Auth0 Mock failed to start${NC}"
        docker compose -f docker-compose.dev.yml logs localauth0-dev
        exit 1
    }
    
    # Step 2: Start Backend API (optional)
    echo -e "\n${BLUE}🔧 Step 2: Starting backend API...${NC}"
    
    # Install backend dependencies
    uv sync
    
    # Set environment variables for development
    export DB_HOST=localhost
    export DB_PORT=$POSTGRES_PORT
    export DB_NAME=fintech_dev
    export DB_USER=dev_user
    export DB_PASSWORD=dev_password
    export AUTH0_DOMAIN=localhost:$AUTH0_PORT
    export AUTH0_API_AUDIENCE=my-api
    export AUTH0_ALGORITHMS=RS256
    export AUTH0_ISSUER=http://localhost:$AUTH0_PORT
    
    # Seed the database
    echo -e "${YELLOW}🌱 Seeding development database...${NC}"
    uv run python src/fintech/api/seed_database.py --reset
    
    # Start API server in background
    echo -e "${YELLOW}🔄 Starting API server...${NC}"
    uv run uvicorn fintech.api.app:app --host 0.0.0.0 --port $BACKEND_PORT > /tmp/api.log 2>&1 &
    BACKEND_PID=$!
    
    # Wait for API
    wait_for_service "Backend API" "http://localhost:$BACKEND_PORT/health/" 30 || {
        echo -e "${RED}❌ Backend API failed to start${NC}"
        echo "API logs:"
        cat /tmp/api.log
        exit 1
    }
    
    # Step 3: Start Frontend
    echo -e "\n${BLUE}🌐 Step 3: Starting frontend...${NC}"
    cd - # Go back to webapp directory
    
    # Install frontend dependencies
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}📦 Installing frontend dependencies...${NC}"
        npm install
    fi
    
    # Start frontend in background
    echo -e "${YELLOW}🔄 Starting frontend server...${NC}"
    npm run dev > /tmp/frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    # Wait for frontend
    wait_for_service "Frontend" "http://localhost:$FRONTEND_PORT" 30 || {
        echo -e "${RED}❌ Frontend failed to start${NC}"
        echo "Frontend logs:"
        cat /tmp/frontend.log
        exit 1
    }
    
    # Step 4: Run E2E Tests
    echo -e "\n${BLUE}🧪 Step 4: Running E2E tests...${NC}"
    
    # Install Playwright browsers if needed
    if [ ! -d "~/.cache/ms-playwright" ]; then
        echo -e "${YELLOW}🎭 Installing Playwright browsers...${NC}"
        npx playwright install
    fi
    
    # Set environment variables for E2E tests
    export AUTH0_SECRET='a-very-long-secret-key-for-testing-that-is-at-least-32-characters-long'
    export AUTH0_BASE_URL="http://localhost:$FRONTEND_PORT"
    export AUTH0_ISSUER_BASE_URL="http://localhost:$AUTH0_PORT"
    export AUTH0_CLIENT_ID='my-client-id'
    export AUTH0_CLIENT_SECRET='my-client-secret'
    export AUTH0_AUDIENCE='my-api'
    export NEXT_PUBLIC_AUTH0_DOMAIN="localhost:$AUTH0_PORT"
    export NEXT_PUBLIC_AUTH0_CLIENT_ID='my-client-id'
    export NEXT_PUBLIC_AUTH0_AUDIENCE='my-api'
    
    # Run the tests
    echo -e "${GREEN}🎭 Running Playwright E2E tests...${NC}"
    npm run test:e2e
    
    # Check test results
    if [ $? -eq 0 ]; then
        echo -e "\n${GREEN}🎉 All E2E tests passed!${NC}"
    else
        echo -e "\n${RED}❌ Some E2E tests failed${NC}"
        exit 1
    fi
}

# Help function
show_help() {
    echo "E2E Test Runner"
    echo "==============="
    echo ""
    echo "This script starts all necessary services and runs E2E tests:"
    echo "  1. PostgreSQL + Auth0 Mock (Docker)"
    echo "  2. Backend API (FastAPI)"
    echo "  3. Frontend (Next.js)"
    echo "  4. E2E Tests (Playwright)"
    echo ""
    echo "Usage:"
    echo "  $0                 Run full E2E test suite"
    echo "  $0 --help         Show this help message"
    echo ""
    echo "Services will be available at:"
    echo "  Frontend:    http://localhost:$FRONTEND_PORT"
    echo "  Backend API: http://localhost:$BACKEND_PORT"
    echo "  Auth0 Mock:  http://localhost:$AUTH0_PORT"
    echo "  PostgreSQL:  localhost:$POSTGRES_PORT"
    echo ""
    echo "Test user credentials:"
    echo "  Email:    <EMAIL>"
    echo "  Password: Test1234"
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
