#!/usr/bin/env node

// Test the development authentication flow
async function testDevAuth() {
  console.log('🧪 Testing development authentication flow...\n');

  // Step 1: Check unauthenticated state
  console.log('1️⃣ Testing unauthenticated state...');
  try {
    const response = await fetch('http://localhost:3000/api/auth/me');
    const data = await response.json();
    console.log('   Response:', JSON.stringify(data, null, 2));
    
    if (!data.authenticated) {
      console.log('   ✅ Correctly shows unauthenticated\n');
    } else {
      console.log('   ❌ Should be unauthenticated\n');
      return;
    }
  } catch (error) {
    console.log('   ❌ Error:', error.message);
    return;
  }

  // Step 2: Test development login
  console.log('2️⃣ Testing development login...');
  try {
    const loginResponse = await fetch('http://localhost:3000/api/auth/dev-login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'demo123'
      })
    });

    const loginData = await loginResponse.json();
    console.log('   Login response:', JSON.stringify(loginData, null, 2));

    if (loginResponse.ok && loginData.success) {
      console.log('   ✅ Login successful');
      
      // Extract the cookie from the response
      const setCookieHeader = loginResponse.headers.get('set-cookie');
      console.log('   🍪 Set-Cookie header:', setCookieHeader);
      
      if (setCookieHeader) {
        // Extract the auth-token value
        const tokenMatch = setCookieHeader.match(/auth-token=([^;]+)/);
        if (tokenMatch) {
          const token = tokenMatch[1];
          console.log('   🔑 Auth token extracted\n');
          
          // Step 3: Test authenticated state
          console.log('3️⃣ Testing authenticated state...');
          const authResponse = await fetch('http://localhost:3000/api/auth/me', {
            headers: {
              'Cookie': `auth-token=${token}`
            }
          });
          
          const authData = await authResponse.json();
          console.log('   Auth check response:', JSON.stringify(authData, null, 2));
          
          if (authData.authenticated && authData.user) {
            console.log('   ✅ Successfully authenticated as:', authData.user.email);
            console.log('   👤 User name:', authData.user.name);
            console.log('   🖼️  Profile picture:', authData.user.picture);
            
            // Step 4: Test logout
            console.log('\n4️⃣ Testing logout...');
            const logoutResponse = await fetch('http://localhost:3000/api/auth/dev-logout', {
              method: 'POST',
              headers: {
                'Cookie': `auth-token=${token}`
              }
            });
            
            const logoutData = await logoutResponse.json();
            console.log('   Logout response:', JSON.stringify(logoutData, null, 2));
            
            if (logoutResponse.ok && logoutData.success) {
              console.log('   ✅ Logout successful');
              
              // Step 5: Verify logged out state (without sending the old token)
              console.log('\n5️⃣ Verifying logged out state...');
              const finalResponse = await fetch('http://localhost:3000/api/auth/me');
              
              const finalData = await finalResponse.json();
              console.log('   Final auth check:', JSON.stringify(finalData, null, 2));
              
              if (!finalData.authenticated) {
                console.log('   ✅ Successfully logged out\n');
                console.log('🎉 All tests passed! Development authentication is working correctly!');
              } else {
                console.log('   ❌ Still authenticated after logout');
              }
            } else {
              console.log('   ❌ Logout failed');
            }
          } else {
            console.log('   ❌ Authentication check failed');
          }
        } else {
          console.log('   ❌ Could not extract auth token from cookie');
        }
      } else {
        console.log('   ❌ No Set-Cookie header found');
      }
    } else {
      console.log('   ❌ Login failed:', loginData.error || 'Unknown error');
    }
  } catch (error) {
    console.log('   ❌ Login error:', error.message);
  }
}

// Run the test
testDevAuth().catch(console.error);
