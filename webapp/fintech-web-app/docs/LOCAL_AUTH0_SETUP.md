# Local Auth0 Setup Guide

This guide explains how to set up and use the local Auth0 mock environment for development and testing.

## Overview

The local Auth0 environment uses the Docker image `public.ecr.aws/primaassicurazioni/localauth0:0.8.2` to simulate Auth0 functionality locally. This allows for:

- **Local Development**: Test authentication flows without external dependencies
- **E2E Testing**: Automated testing with predictable authentication state
- **Offline Development**: Work without internet connectivity
- **Consistent Testing**: Same authentication behavior across all environments

## Quick Start

### 1. Start Local Auth0 Environment

```bash
# Start the local Auth0 mock
npm run auth:start

# Or manually with Docker Compose
docker-compose up -d localauth0
```

### 2. Start the Application

```bash
# Start Next.js development server
npm run dev

# Or start both Auth0 and app together
npm run dev:full
```

### 3. Test Authentication

1. Navigate to `http://localhost:3000`
2. Go through the marketing screens and click "Get Started"
3. You'll be redirected to the local Auth0 login at `http://localhost:3001`
4. Use the test credentials:
   - **Email**: `<EMAIL>`
   - **Password**: `Test1234`
5. After login, you'll be redirected back to the app with authentication

## Configuration

### Environment Variables

The following environment variables are configured in `.env.local`:

```bash
# Auth0 Configuration for Local Development
AUTH0_SECRET='a-very-long-secret-key-for-local-development-that-is-at-least-32-characters-long'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='http://localhost:3001'
AUTH0_CLIENT_ID='my-client-id'
AUTH0_CLIENT_SECRET='my-client-secret'
AUTH0_AUDIENCE='my-api'

# Local Auth0 Mock Configuration
LOCAL_AUTH0_URL='http://localhost:3001'
LOCAL_AUTH0_DOMAIN='localhost:3001'

# Test User Credentials
TEST_USER_EMAIL='<EMAIL>'
TEST_USER_PASSWORD='Test1234'
```

### Docker Configuration

The local Auth0 mock is configured in `docker-compose.yml`:

```yaml
services:
  localauth0:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.2
    ports:
      - "3001:3000"
    environment:
      - AUTH0_AUDIENCE=my-api
      - AUTH0_CLIENT_ID=my-client-id
      - AUTH0_CLIENT_SECRET=my-client-secret
      - AUTH0_DOMAIN=localhost:3001
      - AUTH0_ISSUER=http://localhost:3001
      - AUTH0_BASE_URL=http://localhost:3001
```

### Test User Configuration

The mock Auth0 server is configured with a test user:

- **Email**: `<EMAIL>`
- **Password**: `Test1234`
- **User ID**: `auth0|123456789`
- **Name**: `Test User`
- **Roles**: `["user"]`
- **Permissions**: `["read:profile", "read:dashboard", "read:investments"]`

## Available Scripts

### Auth0 Management

```bash
# Start local Auth0 environment
npm run auth:start

# Stop local Auth0 environment
npm run auth:stop

# View Auth0 container logs
npm run auth:logs

# Check Auth0 container status
npm run auth:status
```

### Development

```bash
# Start Auth0 and app together
npm run dev:full

# Run E2E tests with Auth0
npm run test:full
```

## Application Features

### Protected Routes

The following routes require authentication and specific permissions:

- **`/dashboard`**: Requires `read:dashboard` permission
- **`/profile`**: Requires `read:profile` permission  
- **`/investments`**: Requires `read:investments` permission

### Authentication Flow

1. **Marketing Screen**: Unauthenticated users see marketing content
2. **Login Redirect**: "Get Started" button redirects to local Auth0
3. **Authentication**: User enters credentials on Auth0 mock
4. **Callback**: Auth0 redirects back with authorization code
5. **Token Exchange**: App exchanges code for access token
6. **Protected Content**: User can access dashboard, profile, and investments

### User Interface

After authentication, users can:

- **Dashboard**: View balance, savings, and recent transactions
- **Profile**: See user information, permissions, and access token
- **Investments**: View portfolio and stock holdings
- **Token Refresh**: Manually refresh access tokens
- **Logout**: Clear authentication and return to marketing screen

## E2E Testing

### Test Scenarios

The E2E test suite covers:

1. **Marketing Flow**: Navigation through marketing screens
2. **Authentication**: Complete login flow with local Auth0
3. **Protected Routes**: Access to dashboard, profile, and investments
4. **Route Protection**: Blocking unauthenticated access
5. **Logout Flow**: Clearing authentication state
6. **Token Refresh**: Refreshing access tokens

### Running Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run tests with UI
npm run test:e2e:ui

# Run tests in debug mode
npm run test:e2e:debug

# Run full test suite (starts Auth0, runs tests, stops Auth0)
npm run test:full
```

### Test Helper

The tests include a helper function for authentication:

```typescript
async function authenticateUser(page: any) {
  // Navigate through marketing screens
  // Click "Get Started"
  // Fill in credentials on Auth0 mock
  // Wait for redirect back to app
}
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Ensure ports 3000 and 3001 are available
2. **Docker Issues**: Make sure Docker is running
3. **Auth0 Not Ready**: Wait for the health check to pass
4. **Token Issues**: Check environment variables and Auth0 configuration

### Debugging

```bash
# Check container status
docker-compose ps

# View Auth0 logs
docker-compose logs localauth0

# Check if Auth0 is responding
curl http://localhost:3001/health

# View app logs
npm run dev
```

### Reset Environment

```bash
# Stop all containers
npm run auth:stop

# Remove containers and volumes
docker-compose down -v

# Restart fresh
npm run auth:start
```

## Production vs Local

### Switching Environments

To switch between local and production Auth0:

1. **Local Development**: Use `.env.local` with local Auth0 settings
2. **Production**: Use production environment variables

### Environment Detection

The app automatically detects the environment based on `AUTH0_ISSUER_BASE_URL`:

- Local: `http://localhost:3001`
- Production: `https://your-domain.auth0.com`

## Security Notes

- The local Auth0 mock is for development only
- Never use local Auth0 configuration in production
- Test credentials are hardcoded for convenience
- Access tokens are stored in HTTP-only cookies
- HTTPS is disabled for local development

## Support

For issues with the local Auth0 setup:

1. Check the troubleshooting section
2. Review container logs
3. Verify environment variables
4. Ensure Docker is running properly
