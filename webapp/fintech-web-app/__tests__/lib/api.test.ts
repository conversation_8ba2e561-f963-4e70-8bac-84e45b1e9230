import { ApiClient } from '@/lib/api';

// Mock getAccessToken
jest.mock('@auth0/nextjs-auth0', () => ({
  getAccessToken: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

describe('ApiClient', () => {
  let apiClient: ApiClient;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    apiClient = new ApiClient('http://localhost:8000');
    jest.clearAllMocks();
  });

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const mockResponse = { id: 1, name: 'Test' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.get('/test');

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8000/test', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      expect(result).toEqual({
        data: mockResponse,
        status: 200,
      });
    });

    it('should handle GET request errors', async () => {
      const errorResponse = { detail: 'Not found' };
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: async () => errorResponse,
      } as Response);

      const result = await apiClient.get('/test');

      expect(result).toEqual({
        error: 'Not found',
        status: 404,
      });
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await apiClient.get('/test');

      expect(result).toEqual({
        error: 'Network error',
        status: 500,
      });
    });
  });

  describe('POST requests', () => {
    it('should make successful POST request', async () => {
      const requestBody = { name: 'Test' };
      const mockResponse = { id: 1, ...requestBody };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.post('/test', requestBody);

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8000/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      expect(result).toEqual({
        data: mockResponse,
        status: 201,
      });
    });
  });

  describe('PUT requests', () => {
    it('should make successful PUT request', async () => {
      const requestBody = { id: 1, name: 'Updated Test' };
      const mockResponse = requestBody;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.put('/test/1', requestBody);

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8000/test/1', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
      expect(result).toEqual({
        data: mockResponse,
        status: 200,
      });
    });
  });

  describe('DELETE requests', () => {
    it('should make successful DELETE request with 204 response', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 204,
      } as Response);

      const result = await apiClient.delete('/test/1');

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:8000/test/1', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      expect(result).toEqual({
        status: 204,
      });
    });

    it('should make successful DELETE request with JSON response', async () => {
      const mockResponse = { message: 'Deleted successfully' };
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: async () => mockResponse,
      } as Response);

      const result = await apiClient.delete('/test/1');

      expect(result).toEqual({
        data: mockResponse,
        status: 200,
      });
    });
  });
});
