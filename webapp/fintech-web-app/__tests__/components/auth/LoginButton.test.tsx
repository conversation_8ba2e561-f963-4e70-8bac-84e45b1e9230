import { render, screen } from '@testing-library/react';
import { useUser } from '@auth0/nextjs-auth0';
import LoginButton from '@/app/components/auth/LoginButton';

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

describe('LoginButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading state when user is loading', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      error: undefined,
      isLoading: true,
      checkSession: jest.fn(),
    });

    render(<LoginButton />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows login button when user is not authenticated', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      error: undefined,
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<LoginButton />);
    const loginButton = screen.getByRole('link', { name: /login/i });
    expect(loginButton).toBeInTheDocument();
    expect(loginButton).toHaveAttribute('href', '/api/auth/login');
  });

  it('shows logout button when user is authenticated', () => {
    mockUseUser.mockReturnValue({
      user: {
        sub: 'auth0|123',
        name: 'Test User',
        email: '<EMAIL>',
      },
      error: undefined,
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<LoginButton />);
    const logoutButton = screen.getByRole('link', { name: /logout/i });
    expect(logoutButton).toBeInTheDocument();
    expect(logoutButton).toHaveAttribute('href', '/api/auth/logout');
  });
});
