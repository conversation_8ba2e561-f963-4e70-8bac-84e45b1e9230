import { render, screen } from '@testing-library/react';
import { useUser } from '@auth0/nextjs-auth0';
import UserProfile from '@/app/components/auth/UserProfile';

const mockUseUser = useUser as jest.MockedFunction<typeof useUser>;

describe('UserProfile', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading state when user is loading', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      error: undefined,
      isLoading: true,
      checkSession: jest.fn(),
    });

    render(<UserProfile />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    const errorMessage = 'Authentication failed';
    mockUseUser.mockReturnValue({
      user: undefined,
      error: new Error(errorMessage),
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<UserProfile />);
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('shows not authenticated message when user is not logged in', () => {
    mockUseUser.mockReturnValue({
      user: undefined,
      error: undefined,
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<UserProfile />);
    expect(screen.getByText('Not Authenticated')).toBeInTheDocument();
    expect(screen.getByText('Please log in to view your profile.')).toBeInTheDocument();
  });

  it('shows user profile when user is authenticated', () => {
    const mockUser = {
      sub: 'auth0|123',
      name: 'John Doe',
      email: '<EMAIL>',
      picture: 'https://example.com/avatar.jpg',
      email_verified: true,
      updated_at: '2023-01-01T00:00:00.000Z',
    };

    mockUseUser.mockReturnValue({
      user: mockUser,
      error: undefined,
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<UserProfile />);

    expect(screen.getByText('User Profile')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Verified')).toBeInTheDocument();
    expect(screen.getByText('auth0|123')).toBeInTheDocument();
  });

  it('shows not verified badge when email is not verified', () => {
    const mockUser = {
      sub: 'auth0|123',
      name: 'John Doe',
      email: '<EMAIL>',
      email_verified: false,
    };

    mockUseUser.mockReturnValue({
      user: mockUser,
      error: undefined,
      isLoading: false,
      checkSession: jest.fn(),
    });

    render(<UserProfile />);

    expect(screen.getByText('Not Verified')).toBeInTheDocument();
  });
});
