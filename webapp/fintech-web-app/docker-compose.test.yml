version: '3.8'

services:
  # Local Auth0 Mock for E2E testing
  localauth0-test:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.2
    ports:
      - "3002:3000"  # Use different port for testing
    environment:
      - AUTH0_AUDIENCE=my-api
      - AUTH0_CLIENT_ID=my-client-id
      - AUTH0_CLIENT_SECRET=my-client-secret
      - AUTH0_DOMAIN=localhost:3002
      - AUTH0_ISSUER=http://localhost:3002
      - AUTH0_BASE_URL=http://localhost:3002
      # Test user with all required permissions
      - AUTH0_USERS=[{"email":"<EMAIL>","password":"Test1234","user_id":"auth0|123456789","name":"Test User","roles":["user"],"permissions":["read:profile","read:dashboard","read:investments"]}]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 20s
    networks:
      - fintech-test-network

  # PostgreSQL for E2E testing (following existing pattern)
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Same port as existing test setup
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d fintech_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - fintech-test-network
    tmpfs:
      - /tmp
      - /var/run/postgresql

networks:
  fintech-test-network:
    driver: bridge

volumes:
  postgres_test_data:
    driver: local
