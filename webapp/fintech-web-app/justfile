import '../../just/assets.just'

# Frontend Development Commands
# =============================

# Start backend services (Auth0 + PostgreSQL) for frontend development
[group: 'Development']
backend-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting backend services for frontend development..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi

    # Start backend services using the API service justfile
    cd ../../services/api && just services-start

# Stop backend services
[group: 'Development']
backend-stop:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🛑 Stopping backend services..."
    cd ../../services/api && just dev-stop

# Start full development environment (backend + frontend)
[group: 'Development']
dev-full:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting full development environment..."

    # Start backend services
    just backend-start

    # Wait for services to be ready
    echo "⏳ Waiting for backend services..."
    timeout=60
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ Backend services are ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    if [ $counter -eq $timeout ]; then
        echo "❌ Backend services failed to start"
        exit 1
    fi

    echo "🌐 Starting Next.js development server..."
    echo "   🔐 Auth0 Mock: http://localhost:3001"
    echo "   🐘 PostgreSQL: localhost:5434"
    echo "   🌐 Frontend: http://localhost:3000"
    echo ""

    # Start Next.js dev server
    npm run dev

# View development services status
[group: 'Development']
dev-status:
    #!/usr/bin/env bash
    echo "📊 Development services status:"
    docker compose ps

# View development logs
[group: 'Development']
dev-logs service="":
    #!/usr/bin/env bash
    if [ -n "{{service}}" ]; then
        echo "📝 Viewing logs for {{service}}..."
        docker compose logs -f {{service}}
    else
        echo "📝 Viewing all development logs..."
        docker compose logs -f
    fi

# Run E2E tests with backend services
[group: 'Testing']
test-e2e:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Running E2E tests with backend services..."

    # Check if backend services are running
    if ! curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "🔧 Backend services not running, starting them..."
        just backend-start

        # Wait for services to be ready
        echo "⏳ Waiting for backend services..."
        timeout=60
        counter=0
        while [ $counter -lt $timeout ]; do
            if curl -f http://localhost:3001/health > /dev/null 2>&1; then
                echo "✅ Backend services are ready!"
                break
            fi
            sleep 1
            counter=$((counter + 1))
        done

        if [ $counter -eq $timeout ]; then
            echo "❌ Backend services failed to start"
            exit 1
        fi
    else
        echo "✅ Using existing backend services"
    fi

    # Run E2E tests
    echo "🎭 Running Playwright tests..."
    npm run test:e2e

# Run full E2E test suite with complete environment setup
[group: 'Testing']
test-e2e-full:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Running full E2E test suite with complete environment..."
    ./scripts/run-e2e-tests.sh

# Start complete development environment (all services)
[group: 'Development']
dev-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting complete development environment..."
    ./scripts/start-dev-environment.sh

# Start only backend services (Docker)
[group: 'Development']
services-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🔧 Starting backend services only..."
    ./scripts/start-dev-environment.sh --services-only

# Start backend services + API (no frontend)
[group: 'Development']
api-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🔗 Starting backend services + API..."
    ./scripts/start-dev-environment.sh --api-only

# Run full test suite (unit + E2E)
[group: 'Testing']
test-all:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Running full test suite..."

    # Run unit tests
    echo "🔬 Running unit tests..."
    npm test

    # Run E2E tests
    echo "🎭 Running E2E tests..."
    just test-e2e

    echo "✅ All tests completed!"

# Clean up all Docker resources
[group: 'Maintenance']
clean-docker:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧹 Cleaning up Docker resources..."

    # Stop all compose services
    docker compose down -v || true
    docker compose -f docker-compose.test.yml down -v || true

    # Remove unused images
    docker image prune -f

    echo "✅ Docker cleanup completed!"

# Deploy the Dashboard to <env>.
[group: 'Deployment']
deploy: (upload-web-app 'dev' 'nesteggli-web-app/app')
