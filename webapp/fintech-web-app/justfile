import '../../just/assets.just'

# Local Development Commands
# ==========================

# Start full development environment (Auth0 + PostgreSQL)
[group: 'Development']
dev-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting full development environment..."

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi

    # Start development services
    ./scripts/start-dev-env.sh

# Start only Auth0 mock for development
[group: 'Development']
auth-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🔐 Starting Auth0 mock for development..."
    ./scripts/start-local-auth.sh dev

# Start Auth0 mock for testing
[group: 'Testing']
auth-test:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Starting Auth0 mock for testing..."
    ./scripts/start-local-auth.sh test

# Stop development environment
[group: 'Development']
dev-stop:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🛑 Stopping development environment..."
    docker compose down

# Stop test environment
[group: 'Testing']
test-stop:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🛑 Stopping test environment..."
    docker compose -f docker-compose.test.yml down

# View development services status
[group: 'Development']
dev-status:
    #!/usr/bin/env bash
    echo "📊 Development services status:"
    docker compose ps

# View development logs
[group: 'Development']
dev-logs service="":
    #!/usr/bin/env bash
    if [ -n "{{service}}" ]; then
        echo "📝 Viewing logs for {{service}}..."
        docker compose logs -f {{service}}
    else
        echo "📝 Viewing all development logs..."
        docker compose logs -f
    fi

# Run E2E tests with local Auth0 (reuse development environment)
[group: 'Testing']
test-e2e:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Running E2E tests with local Auth0..."

    # Check if development Auth0 is running
    if ! curl -f http://localhost:3001/health > /dev/null 2>&1; then
        echo "🔧 Development Auth0 not running, starting it..."
        just auth-start

        # Wait for Auth0 to be ready
        echo "⏳ Waiting for Auth0 mock..."
        timeout=60
        counter=0
        while [ $counter -lt $timeout ]; do
            if curl -f http://localhost:3001/health > /dev/null 2>&1; then
                echo "✅ Auth0 mock is ready!"
                break
            fi
            sleep 1
            counter=$((counter + 1))
        done

        if [ $counter -eq $timeout ]; then
            echo "❌ Auth0 mock failed to start"
            exit 1
        fi
    else
        echo "✅ Using existing Auth0 mock on port 3001"
    fi

    # Run E2E tests
    echo "🎭 Running Playwright tests..."
    npm run test:e2e

# Run full test suite (unit + E2E)
[group: 'Testing']
test-all:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Running full test suite..."

    # Run unit tests
    echo "🔬 Running unit tests..."
    npm test

    # Run E2E tests
    echo "🎭 Running E2E tests..."
    just test-e2e

    echo "✅ All tests completed!"

# Clean up all Docker resources
[group: 'Maintenance']
clean-docker:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧹 Cleaning up Docker resources..."

    # Stop all compose services
    docker compose down -v || true
    docker compose -f docker-compose.test.yml down -v || true

    # Remove unused images
    docker image prune -f

    echo "✅ Docker cleanup completed!"

# Deploy the Dashboard to <env>.
[group: 'Deployment']
deploy: (upload-web-app 'dev' 'nesteggli-web-app/app')
