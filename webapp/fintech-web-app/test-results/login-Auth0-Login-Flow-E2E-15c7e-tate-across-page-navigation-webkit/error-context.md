# Page snapshot

```yaml
- alert
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- navigation:
  - button "previous" [disabled]:
    - img "previous"
  - text: 1/1
  - button "next" [disabled]:
    - img "next"
- img
- img
- text: Next.js 15.3.4 Webpack
- img
- dialog "Build Error":
  - text: Build Error
  - button "Copy Stack Trace":
    - img
  - link "Go to related documentation":
    - /url: https://nextjs.org/docs/messages/module-not-found
    - img
  - link "Learn more about enabling Node.js inspector for server code with Chrome DevTools":
    - /url: https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code
    - img
  - paragraph: "Module not found: Package path ./client is not exported from package /Users/<USER>/projects/fintech-monorepo/webapp/fintech-web-app/node_modules/@auth0/nextjs-auth0 (see exports field in /Users/<USER>/projects/fintech-monorepo/webapp/fintech-web-app/node_modules/@auth0/nextjs-auth0/package.json)"
  - img
  - text: ./app/providers/UserProvider.tsx (3:1)
  - button "Open in editor":
    - img
  - text: "Module not found: Package path ./client is not exported from package /Users/<USER>/projects/fintech-monorepo/webapp/fintech-web-app/node_modules/@auth0/nextjs-auth0 (see exports field in /Users/<USER>/projects/fintech-monorepo/webapp/fintech-web-app/node_modules/@auth0/nextjs-auth0/package.json) 1 | 'use client'; 2 | > 3 | import { UserProvider as Auth0UserProvider } from '@auth0/nextjs-auth0/client'; | ^ 4 | 5 | interface UserProviderProps { 6 | children: React.ReactNode;"
  - link "https://nextjs.org/docs/messages/module-not-found":
    - /url: https://nextjs.org/docs/messages/module-not-found
- contentinfo:
  - paragraph: This error occurred during the build process and can only be dismissed by fixing the error.
```