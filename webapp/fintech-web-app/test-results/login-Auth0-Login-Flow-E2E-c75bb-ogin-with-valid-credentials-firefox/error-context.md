# Page snapshot

```yaml
- 'heading "Application error: a client-side exception has occurred while loading localhost (see the browser console for more information)." [level=2]'
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 1 Issue
- button "Collapse issues badge":
  - img
- navigation:
  - button "previous" [disabled]:
    - img "previous"
  - text: 1/1
  - button "next" [disabled]:
    - img "next"
- img
- img
- text: Next.js 15.3.4 Webpack
- img
- dialog "Runtime Error":
  - text: Runtime Error
  - button "Copy Stack Trace":
    - img
  - button "No related documentation found" [disabled]:
    - img
  - link "Learn more about enabling Node.js inspector for server code with Chrome DevTools":
    - /url: https://nextjs.org/docs/app/building-your-application/configuring/debugging#server-side-code
    - img
  - paragraph: "Error: Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: undefined. You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports. Check the render method of `UserProvider`."
  - paragraph:
    - img
    - text: app/providers/UserProvider.tsx (10:10) @ UserProvider
    - button "Open in editor":
      - img
  - text: "8 | 9 | export default function UserProvider({ children }: UserProviderProps) { > 10 | return <Auth0UserProvider>{children}</Auth0UserProvider>; | ^ 11 | } 12 |"
  - paragraph: Call Stack 55
  - button "Show 27 ignore-listed frame(s)":
    - text: Show 27 ignore-listed frame(s)
    - img
  - text: ./node_modules/next/dist/compiled/scheduler/cjs/scheduler.development.js .next/static/chunks/main-app.js (2457:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) fn .next/static/chunks/webpack.js (357:21) ./node_modules/next/dist/compiled/scheduler/index.js .next/static/chunks/main-app.js (2468:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) fn .next/static/chunks/webpack.js (357:21) ./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js .next/static/chunks/main-app.js (2314:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) fn .next/static/chunks/webpack.js (357:21) ./node_modules/next/dist/compiled/react-dom/client.js .next/static/chunks/main-app.js (2336:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) fn .next/static/chunks/webpack.js (357:21) ./node_modules/next/dist/client/app-index.js .next/static/chunks/main-app.js (160:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) fn .next/static/chunks/webpack.js (357:21) ./node_modules/next/dist/client/app-next-dev.js .next/static/chunks/main-app.js (182:1) options.factory .next/static/chunks/webpack.js (700:31) __webpack_require__ .next/static/chunks/webpack.js (37:33) __webpack_exec__ .next/static/chunks/main-app.js (2824:67) <unknown> .next/static/chunks/main-app.js (2825:154) <unknown> .next/static/chunks/main-app.js (9:61) UserProvider
  - button:
    - img
  - text: app/providers/UserProvider.tsx (10:10) RootLayout
  - button:
    - img
  - text: app/layout.tsx (18:9)
- contentinfo:
  - region "Error feedback":
    - paragraph:
      - link "Was this helpful?":
        - /url: https://nextjs.org/telemetry#error-feedback
    - button "Mark as helpful"
    - button "Mark as not helpful"
```