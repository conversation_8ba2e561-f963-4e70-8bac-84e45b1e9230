import { ValidationError } from './types'

// <PERSON><PERSON> algorithm for credit card validation
export function validateCardNumber(cardNumber: string): boolean {
  const cleanNumber = cardNumber.replace(/\s/g, '')
  
  if (!/^\d{13,19}$/.test(cleanNumber)) {
    return false
  }

  let sum = 0
  let isEven = false

  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber[i])

    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }

    sum += digit
    isEven = !isEven
  }

  return sum % 10 === 0
}

export function validateCVV(cvv: string, cardNumber: string): boolean {
  const cleanCVV = cvv.replace(/\s/g, '')
  const cleanCardNumber = cardNumber.replace(/\s/g, '')
  
  // American Express cards have 4-digit CVV, others have 3-digit
  const isAmex = cleanCardNumber.startsWith('34') || cleanCardNumber.startsWith('37')
  const expectedLength = isAmex ? 4 : 3
  
  return /^\d+$/.test(cleanCVV) && cleanCVV.length === expectedLength
}

export function validateExpirationDate(expirationDate: string): boolean {
  const cleanDate = expirationDate.replace(/\s/g, '')
  
  if (!/^\d{2}\/\d{2}$/.test(cleanDate)) {
    return false
  }

  const [month, year] = cleanDate.split('/').map(num => parseInt(num))
  
  if (month < 1 || month > 12) {
    return false
  }

  const currentDate = new Date()
  const currentYear = currentDate.getFullYear() % 100
  const currentMonth = currentDate.getMonth() + 1

  if (year < currentYear || (year === currentYear && month < currentMonth)) {
    return false
  }

  return true
}

export function formatCardNumber(value: string): string {
  const cleanValue = value.replace(/\s/g, '')
  const groups = cleanValue.match(/.{1,4}/g) || []
  return groups.join(' ').substr(0, 19) // Max 16 digits + 3 spaces
}

export function formatExpirationDate(value: string): string {
  const cleanValue = value.replace(/\D/g, '')
  if (cleanValue.length >= 2) {
    return cleanValue.substr(0, 2) + '/' + cleanValue.substr(2, 2)
  }
  return cleanValue
}

export function validateCardForm(data: {
  name: string
  cardNumber: string
  cvv: string
  expirationDate: string
}): ValidationError[] {
  const errors: ValidationError[] = []

  if (!data.name.trim()) {
    errors.push({ field: 'name', message: 'validation.cardName.required' })
  }

  if (!validateCardNumber(data.cardNumber)) {
    errors.push({ field: 'cardNumber', message: 'validation.cardNumber.invalid' })
  }

  if (!validateCVV(data.cvv, data.cardNumber)) {
    errors.push({ field: 'cvv', message: 'validation.cvv.invalid' })
  }

  if (!validateExpirationDate(data.expirationDate)) {
    errors.push({ field: 'expirationDate', message: 'validation.expirationDate.invalid' })
  }

  return errors
}

export function validateGoalForm(data: {
  name: string
  target: string
  deadline: string
}): ValidationError[] {
  const errors: ValidationError[] = []

  if (!data.name.trim()) {
    errors.push({ field: 'name', message: 'validation.goalName.required' })
  }

  const targetAmount = parseFloat(data.target)
  if (isNaN(targetAmount) || targetAmount <= 0) {
    errors.push({ field: 'target', message: 'validation.target.invalid' })
  }

  const deadlineDate = new Date(data.deadline)
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  
  if (deadlineDate <= today) {
    errors.push({ field: 'deadline', message: 'validation.deadline.future' })
  }

  return errors
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function validateLoginForm(data: {
  email: string
  password: string
}): ValidationError[] {
  const errors: ValidationError[] = []

  if (!validateEmail(data.email)) {
    errors.push({ field: 'email', message: 'validation.email.invalid' })
  }

  if (data.password.length < 6) {
    errors.push({ field: 'password', message: 'validation.password.minLength' })
  }

  return errors
}
