import { useState, useEffect } from 'react';

// Simple i18n implementation for App Router
type Locale = 'en' | 'de' | 'pl';

const translations = {
  en: () => import('../public/locales/en/common.json').then(m => m.default),
  de: () => import('../public/locales/de/common.json').then(m => m.default),
  pl: () => import('../public/locales/pl/common.json').then(m => m.default),
};

let currentTranslations: any = {};
let currentLocale: Locale = 'en';

export function useTranslation() {
  const [locale, setLocale] = useState<Locale>(currentLocale);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadTranslations(locale);
  }, [locale]);

  const loadTranslations = async (newLocale: Locale) => {
    if (currentLocale === newLocale && Object.keys(currentTranslations).length > 0) {
      return;
    }

    setIsLoading(true);
    try {
      currentTranslations = await translations[newLocale]();
      currentLocale = newLocale;
      if (typeof window !== 'undefined') {
        localStorage.setItem('locale', newLocale);
      }
    } catch (error) {
      console.error('Failed to load translations:', error);
      // Fallback to English
      if (newLocale !== 'en') {
        currentTranslations = await translations.en();
        currentLocale = 'en';
      }
    }
    setIsLoading(false);
  };

  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.');
    let value = currentTranslations;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return key if translation not found
      }
    }

    if (typeof value === 'string' && params) {
      return value.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey] || match;
      });
    }

    return typeof value === 'string' ? value : key;
  };

  const changeLanguage = async (newLocale: Locale) => {
    setLocale(newLocale);
    await loadTranslations(newLocale);
  };

  // Initialize with saved locale on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedLocale = localStorage.getItem('locale') as Locale;
      if (savedLocale && savedLocale !== locale) {
        setLocale(savedLocale);
      }
    }
  }, [locale]);

  return {
    t,
    changeLanguage,
    currentLocale: locale,
    isLoading,
  };
}

export function formatCurrency(amount: number, locale: string = 'en'): string {
  const currencyMap = {
    en: 'USD',
    de: 'USD', // Using USD for consistency
    pl: 'USD', // Using USD for consistency
  };

  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyMap[locale as keyof typeof currencyMap] || 'USD',
  }).format(amount);
}
