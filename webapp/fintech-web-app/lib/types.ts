export type CardType = 'Debit' | 'Credit';

export type Screen =
  | 'marketing'
  | 'login'
  | 'dashboard'
  | 'add-card'
  | 'contributions'
  | 'investments'
  | 'profile'
  | 'add-goal'
  | 'savings-methods';

export type InvestmentStrategy = 'Fixed Income' | 'Standard' | 'Growth';
export type SavingMethod = 'roundups' | 'percentage' | 'recurring';

export interface BankCard {
  id: string;
  name: string;
  type: CardType;
  cardNumber: string;
  cvv: string;
  expirationDate: string;
  limit?: number;
  lastFour?: string;
}

export interface Goal {
  id: string;
  name: string;
  target: number;
  deadline: Date;
  saved: number;
  strategy: InvestmentStrategy;
  isActive: boolean;
}

export interface Transaction {
  id: string;
  merchant: string;
  amount: number;
  savings: number;
  method: SavingMethod;
  icon: React.ComponentType<{ className?: string }>;
  time: string;
}

export interface SavingSettings {
  roundups: boolean;
  percentage: boolean;
  percentageValue: number;
  recurring: boolean;
  recurringAmount: number;
  recurringFrequency: string;
}

export interface User {
  email: string;
  name: string;
}

export interface CardFormData {
  name: string;
  type: CardType;
  cardNumber: string;
  cvv: string;
  expirationDate: string;
  limit: string;
}

export interface GoalFormData {
  name: string;
  target: string;
  deadline: string;
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface InvestmentStrategyDetails {
  risk: string;
  return: string;
  description: string;
  projection: string;
  color: string;
}
