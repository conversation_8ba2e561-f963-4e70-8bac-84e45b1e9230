import { Bird, Coffee, TrendingUp, Settings, Target, Egg } from "lucide-react"

export const marketingScreens = [
  {
    title: "Save effortlessly as you spend",
    subtitle: "Your money works harder for you",
    description: "Transform every purchase into progress toward your dreams",
    icon: Bird,
    gradient: "from-orange-400 to-amber-500",
  },
  {
    title: "Every coffee pays into your future",
    subtitle: "Small changes, big impact",
    description: "Round up purchases and watch your savings grow automatically",
    icon: Coffee,
    gradient: "from-amber-400 to-orange-500",
  },
  {
    title: "Invest your spare change into curated ETFs",
    subtitle: "Professional investing made simple",
    description: "Your savings are invested in diversified portfolios by experts",
    icon: TrendingUp,
    gradient: "from-orange-400 to-red-500",
  },
  {
    title: "Choose how you save",
    subtitle: "Round-ups, % of spending, or recurring transfers",
    description: "Multiple ways to build wealth that fit your lifestyle",
    icon: Settings,
    gradient: "from-red-400 to-pink-500",
  },
  {
    title: "Set your goal — we'll help you reach it",
    subtitle: "Dream big, save smart",
    description: "Whether it's a vacation or a house, we'll create a plan",
    icon: Target,
    gradient: "from-pink-400 to-purple-500",
  },
  {
    title: "Watch your savings grow",
    subtitle: "Smart investment strategies",
    description: "Based on proven strategies that compound your wealth over time",
    icon: Egg,
    gradient: "from-purple-400 to-indigo-500",
  },
]

export const investmentStrategies = {
  "Fixed Income": {
    risk: "Low Risk",
    return: "3-4%",
    description: "Conservative approach with steady, predictable returns",
    projection: "€1,450 by July 2026",
    color: "text-green-600",
  },
  Standard: {
    risk: "Balanced Risk",
    return: "5-6%",
    description: "Moderate growth with balanced risk and reward",
    projection: "€2,130 in 11 months",
    color: "text-orange-600",
  },
  Growth: {
    risk: "High Risk",
    return: "7-10%",
    description: "Aggressive growth for long-term wealth building",
    projection: "May exceed €2,000 goal",
    color: "text-red-600",
  },
} as const
