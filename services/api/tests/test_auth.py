"""Tests for Auth0 authentication."""

import json
import pytest
from unittest.mock import Mock, patch
from fastapi.testclient import TestClient
from jose import jwt

from fintech.api.app import create_app
from fintech.api.auth import Auth0JWT<PERSON>earer, User


@pytest.fixture
def app():
    """Create test app."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_jwks():
    """Mock JWKS response."""
    return {
        "keys": [
            {
                "kty": "RSA",
                "kid": "test-key-id",
                "use": "sig",
                "n": "test-n-value",
                "e": "AQAB"
            }
        ]
    }


@pytest.fixture
def valid_token_payload():
    """Valid JWT token payload."""
    return {
        "sub": "auth0|123456789",
        "email": "<EMAIL>",
        "name": "Test User",
        "nickname": "testuser",
        "picture": "https://example.com/avatar.jpg",
        "email_verified": True,
        "aud": "test-audience",
        "iss": "https://test.auth0.com/",
        "exp": 9999999999,  # Far future
        "iat": 1234567890
    }


class TestAuth0JWTBearer:
    """Test Auth0 JWT Bearer authentication."""

    def test_user_model_creation(self):
        """Test User model creation."""
        user_data = {
            "sub": "auth0|123",
            "email": "<EMAIL>",
            "name": "Test User",
            "nickname": "testuser",
            "picture": "https://example.com/avatar.jpg",
            "email_verified": True
        }
        
        user = User(**user_data)
        
        assert user.sub == "auth0|123"
        assert user.email == "<EMAIL>"
        assert user.name == "Test User"
        assert user.nickname == "testuser"
        assert user.picture == "https://example.com/avatar.jpg"
        assert user.email_verified is True

    def test_user_model_optional_fields(self):
        """Test User model with only required fields."""
        user = User(sub="auth0|123")
        
        assert user.sub == "auth0|123"
        assert user.email is None
        assert user.name is None
        assert user.nickname is None
        assert user.picture is None
        assert user.email_verified is None

    @patch('fintech.api.auth.urlopen')
    def test_get_jwks(self, mock_urlopen, mock_jwks):
        """Test JWKS retrieval."""
        mock_response = Mock()
        mock_response.read.return_value = json.dumps(mock_jwks).encode()
        mock_urlopen.return_value.__enter__.return_value = mock_response
        
        auth_bearer = Auth0JWTBearer()
        jwks = auth_bearer.get_jwks()
        
        assert jwks == mock_jwks
        mock_urlopen.assert_called_once()

    @patch('fintech.api.auth.urlopen')
    def test_get_jwks_caching(self, mock_urlopen, mock_jwks):
        """Test JWKS caching."""
        mock_response = Mock()
        mock_response.read.return_value = json.dumps(mock_jwks).encode()
        mock_urlopen.return_value.__enter__.return_value = mock_response
        
        auth_bearer = Auth0JWTBearer()
        
        # First call
        jwks1 = auth_bearer.get_jwks()
        # Second call should use cache
        jwks2 = auth_bearer.get_jwks()
        
        assert jwks1 == jwks2 == mock_jwks
        # Should only call urlopen once due to caching
        mock_urlopen.assert_called_once()


class TestAuthRoutes:
    """Test authentication routes."""

    def test_public_endpoint(self, client):
        """Test public endpoint doesn't require authentication."""
        response = client.get("/auth/public")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "This is a public endpoint"
        assert data["authentication_required"] is False

    def test_protected_endpoint_without_token(self, client):
        """Test protected endpoint requires authentication."""
        response = client.get("/auth/protected")
        
        assert response.status_code == 401

    def test_me_endpoint_without_token(self, client):
        """Test /me endpoint requires authentication."""
        response = client.get("/auth/me")
        
        assert response.status_code == 401

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_protected_endpoint_with_valid_token(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test protected endpoint with valid token."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/auth/protected", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "This is a protected endpoint"
        assert data["user"]["sub"] == "auth0|123456789"
        assert data["user"]["email"] == "<EMAIL>"
        assert data["access_granted"] is True

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_me_endpoint_with_valid_token(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test /me endpoint with valid token."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["sub"] == "auth0|123456789"
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Test User"
        assert data["email_verified"] is True

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_optional_auth_endpoint_with_token(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test optional auth endpoint with token."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/auth/optional-auth", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Hello authenticated user!"
        assert data["authenticated"] is True
        assert data["user"]["sub"] == "auth0|123456789"

    def test_optional_auth_endpoint_without_token(self, client):
        """Test optional auth endpoint without token."""
        response = client.get("/auth/optional-auth")
        
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Hello anonymous user!"
        assert data["authenticated"] is False

    @patch('fintech.api.auth.jwt.get_unverified_header')
    def test_invalid_token_header(self, mock_get_header, client):
        """Test invalid token header."""
        mock_get_header.side_effect = Exception("Invalid header")
        
        headers = {"Authorization": "Bearer invalid-token"}
        response = client.get("/auth/protected", headers=headers)
        
        assert response.status_code == 401
        assert "Invalid token header" in response.json()["detail"]

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.get_unverified_header')
    def test_key_not_found(self, mock_get_header, mock_get_jwks, client, mock_jwks):
        """Test when RSA key is not found."""
        mock_get_header.return_value = {"kid": "unknown-key-id"}
        mock_get_jwks.return_value = mock_jwks
        
        headers = {"Authorization": "Bearer token-with-unknown-kid"}
        response = client.get("/auth/protected", headers=headers)
        
        assert response.status_code == 401
        assert "Unable to find appropriate key" in response.json()["detail"]

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    @patch('fintech.api.auth.jwt.get_unverified_header')
    def test_expired_token(self, mock_get_header, mock_jwt_decode, mock_get_jwks, client, mock_jwks):
        """Test expired token."""
        mock_get_header.return_value = {"kid": "test-key-id"}
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.side_effect = jwt.ExpiredSignatureError("Token expired")
        
        headers = {"Authorization": "Bearer expired-token"}
        response = client.get("/auth/protected", headers=headers)
        
        assert response.status_code == 401
        assert "Token has expired" in response.json()["detail"]

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    @patch('fintech.api.auth.jwt.get_unverified_header')
    def test_invalid_claims(self, mock_get_header, mock_jwt_decode, mock_get_jwks, client, mock_jwks):
        """Test invalid JWT claims."""
        mock_get_header.return_value = {"kid": "test-key-id"}
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.side_effect = jwt.JWTClaimsError("Invalid claims")
        
        headers = {"Authorization": "Bearer invalid-claims-token"}
        response = client.get("/auth/protected", headers=headers)
        
        assert response.status_code == 401
        assert "Invalid claims" in response.json()["detail"]
