"""Tests for user routes with Auth0 integration."""

import pytest
from unittest.mock import patch
from fastapi.testclient import TestClient

from fintech.api.app import create_app


@pytest.fixture
def app():
    """Create test app."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_jwks():
    """Mock JWKS response."""
    return {
        "keys": [
            {
                "kty": "RSA",
                "kid": "test-key-id",
                "use": "sig",
                "n": "test-n-value",
                "e": "AQAB"
            }
        ]
    }


@pytest.fixture
def valid_token_payload():
    """Valid JWT token payload."""
    return {
        "sub": "auth0|123456789",
        "email": "<EMAIL>",
        "name": "Test User",
        "nickname": "testuser",
        "picture": "https://example.com/avatar.jpg",
        "email_verified": True,
        "aud": "test-audience",
        "iss": "https://test.auth0.com/",
        "exp": 9999999999,  # Far future
        "iat": 1234567890
    }


class TestUserRoutes:
    """Test user-related routes."""

    def test_me_endpoint_without_token(self, client):
        """Test /me endpoint requires authentication."""
        response = client.get("/api/users/me")
        
        assert response.status_code == 401

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_me_endpoint_with_valid_token(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test /me endpoint with valid token."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/api/users/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["sub"] == "auth0|123456789"
        assert data["email"] == "<EMAIL>"
        assert data["name"] == "Test User"
        assert data["nickname"] == "testuser"
        assert data["picture"] == "https://example.com/avatar.jpg"
        assert data["email_verified"] is True
        assert data["provider"] == "auth0"

    def test_summary_endpoint_without_token(self, client):
        """Test /me/summary endpoint requires authentication."""
        response = client.get("/api/users/me/summary")
        
        assert response.status_code == 401

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_summary_endpoint_with_valid_token(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test /me/summary endpoint with valid token."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        response = client.get("/api/users/me/summary", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "total_balance" in data
        assert "active_goals" in data
        assert "cards_connected" in data
        assert "total_savings" in data
        assert data["total_balance"] == 1847.50
        assert data["active_goals"] == 2
        assert data["cards_connected"] == 1
        assert data["total_savings"] == 310.25

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_me_endpoint_with_minimal_token_payload(self, mock_jwt_decode, mock_get_jwks, client, mock_jwks):
        """Test /me endpoint with minimal token payload."""
        minimal_payload = {
            "sub": "auth0|minimal",
            "aud": "test-audience",
            "iss": "https://test.auth0.com/",
            "exp": 9999999999,
            "iat": 1234567890
        }
        
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = minimal_payload
        
        headers = {"Authorization": "Bearer minimal-token"}
        response = client.get("/api/users/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["sub"] == "auth0|minimal"
        assert data["email"] is None
        assert data["name"] is None
        assert data["nickname"] is None
        assert data["picture"] is None
        assert data["email_verified"] is False
        assert data["provider"] == "auth0"

    def test_legacy_profile_endpoint_still_works(self, client):
        """Test that the legacy profile endpoint still works."""
        response = client.get("/api/users/profile")
        
        # This should work as it uses the existing service layer
        # The exact response depends on the mock setup in the existing code
        assert response.status_code in [200, 500]  # 500 if database not available

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_user_data_consistency(self, mock_jwt_decode, mock_get_jwks, client, valid_token_payload, mock_jwks):
        """Test that user data is consistent across different endpoints."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.return_value = valid_token_payload
        
        headers = {"Authorization": "Bearer valid-token"}
        
        # Get user info from /me endpoint
        me_response = client.get("/api/users/me", headers=headers)
        me_data = me_response.json()
        
        # Get user summary
        summary_response = client.get("/api/users/me/summary", headers=headers)
        summary_data = summary_response.json()
        
        assert me_response.status_code == 200
        assert summary_response.status_code == 200
        
        # Both endpoints should be accessible with the same token
        assert me_data["sub"] == "auth0|123456789"
        assert "total_balance" in summary_data

    @patch('fintech.api.auth.Auth0JWTBearer.get_jwks')
    @patch('fintech.api.auth.jwt.decode')
    def test_error_handling_in_auth_flow(self, mock_jwt_decode, mock_get_jwks, client, mock_jwks):
        """Test error handling in authentication flow."""
        mock_get_jwks.return_value = mock_jwks
        mock_jwt_decode.side_effect = Exception("JWT processing error")
        
        headers = {"Authorization": "Bearer problematic-token"}
        response = client.get("/api/users/me", headers=headers)
        
        assert response.status_code == 401
