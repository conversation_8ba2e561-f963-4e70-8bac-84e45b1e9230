{"info": {"name": "FinTech API Collection", "description": "Complete API collection for FinTech application endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "test_user_id", "value": "550e8400-e29b-41d4-a716-446655440000", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Root Endpoint", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}}, "response": []}, {"name": "User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/user/profile?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "user", "profile"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Account Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/account/summary?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "account", "summary"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Savings Goal", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/savings/goal?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "savings", "goal"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Today's Savings Summary", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/savings/today/summary?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "savings", "today", "summary"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Today's Savings Feed", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/savings/today/feed?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "savings", "today", "feed"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Savings Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/savings/methods?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "savings", "methods"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}, {"name": "Investment Strategy", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/investing/strategy?user_id={{test_user_id}}", "host": ["{{base_url}}"], "path": ["api", "investing", "strategy"], "query": [{"key": "user_id", "value": "{{test_user_id}}"}]}}, "response": []}]}