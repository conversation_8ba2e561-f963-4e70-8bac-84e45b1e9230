ARG PYTHON_BASE=3.12

FROM --platform=linux/amd64 python:$PYTHON_BASE-slim AS builder
ARG FINTECH_SERVICE=api

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install UV (the build tool)
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Set working directory
WORKDIR /tmp/fintech

# Copy dependency files
COPY libs /tmp/fintech/libs
COPY services/$FINTECH_SERVICE /tmp/fintech/services/$FINTECH_SERVICE

# Work in the service directory
WORKDIR /tmp/fintech/services/$FINTECH_SERVICE

# Create virtual environment and install dependencies
RUN uv venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN uv sync --frozen

# Production image
FROM --platform=linux/amd64 python:$PYTHON_BASE-slim AS service
ARG FINTECH_SERVICE=api
ENV FINTECH_SERVICE=$FINTECH_SERVICE

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

# Copy virtual environment from builder
COPY --from=builder /opt/venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy service code
COPY --from=builder /tmp/fintech/services/$FINTECH_SERVICE/src /app

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN groupadd -r fintech && useradd -r -g fintech fintech
RUN chown -R fintech:fintech /app
USER fintech

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health/ || exit 1

# Expose port
EXPOSE 8000

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Run the application
CMD ["uvicorn", "fintech.api.app:app", "--host", "0.0.0.0", "--port", "8000"]
