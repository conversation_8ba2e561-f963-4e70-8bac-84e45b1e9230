# Database Configuration
DATABASE_URL=postgresql://test_user:test_password@localhost:5433/fintech_test
DB_HOST=localhost
DB_PORT=5433
DB_NAME=fintech_test
DB_USER=test_user
DB_PASSWORD=test_password

# Auth0 Configuration
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_API_AUDIENCE=https://your-api.example.com
AUTH0_ALGORITHMS=RS256

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3001
