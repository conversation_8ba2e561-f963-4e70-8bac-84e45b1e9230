#!/bin/bash
# Quick development server start (assumes database is already running)

echo "🚀 Starting FinTech API in development mode..."

# Set database environment variables for the test database
export DB_HOST=localhost
export DB_PORT=5433
export DB_NAME=fintech_test
export DB_USER=test_user
export DB_PASSWORD=test_password

echo "🔄 Starting uvicorn with auto-reload..."
echo "📍 Database: PostgreSQL (localhost:5433)"
echo "🔗 API: http://localhost:8000"
echo "📚 Docs: http://localhost:8000/docs"
echo "💡 Auto-reload enabled - changes will restart server automatically"
echo ""

# Start uvicorn with reload
uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000
