[project]
name = "fintech-api-service"
version = "0.0.0"
requires-python = "==3.12.*"

dependencies = [
    # Internal Dependencies
    "fintech-models",
    # FastAPI and related
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    # Database
    "asyncpg>=0.29.0",
    "alembic>=1.13.1",
    # Authentication & Security
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    # Validation & Serialization
    "pydantic[email]>=2.5.0",
    "pydantic-settings>=2.1.0",
    # HTTP Client
    "httpx>=0.25.2",
    # Utilities
    "python-dotenv>=1.0.0",
    # Services
    "fintech-services",
    "fintech-testutils",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "httpx>=0.25.2",
    "aiosqlite>=0.19.0",
]
typing = [
    "types-passlib",
    "types-python-jose",
]

#TEST
[tool.uv.sources]
fintech-models = { path = "../../libs/models", editable = true }
fintech-services = { path = "../../libs/services", editable = true }
fintech-testutils = { path = "../../libs/testutils", editable = true }

[tool.pytest.ini_options]
pythonpath = ["src"]

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
dev = [
    "mypy>=1.16.0",
    "pytest>=8.4.0",
]
