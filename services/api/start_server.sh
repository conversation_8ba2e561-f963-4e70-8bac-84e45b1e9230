#!/bin/bash
# Start the FinTech API server with database

echo "🚀 Starting FinTech API server with PostgreSQL database..."

# Check if PostgreSQL database is running
if ! nc -z localhost 5433 2>/dev/null; then
    echo "🔄 Starting PostgreSQL database..."
    cd ../../libs/models
    docker compose -f docker-compose.test.yml up -d
    cd ../../services/api

    # Wait for database to be ready
    echo "⏳ Waiting for database to be ready..."
    for i in {1..30}; do
        if nc -z localhost 5433 2>/dev/null; then
            echo "✅ Database is ready!"
            break
        fi
        sleep 1
    done

    if ! nc -z localhost 5433 2>/dev/null; then
        echo "❌ Database failed to start"
        exit 1
    fi
fi

# Set database environment variables for the test database
export DB_HOST=localhost
export DB_PORT=5433
export DB_NAME=fintech_test
export DB_USER=test_user
export DB_PASSWORD=test_password

echo "🌐 Starting API server..."
echo "📍 Database: PostgreSQL (localhost:5433)"
echo "🔗 API: http://localhost:8000"
echo "📚 Docs: http://localhost:8000/docs"
echo ""

# Seed the database first
echo "🌱 Seeding database with sample data..."
uv run python src/fintech/api/seed_database.py

# Start the server with uvicorn and reload
echo "🔄 Starting server with auto-reload..."
uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000
