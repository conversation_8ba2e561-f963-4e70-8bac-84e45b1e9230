# Development Environment Configuration for API Service

# Database Configuration
DB_HOST=localhost
DB_PORT=5434
DB_NAME=fintech_dev
DB_USER=dev_user
DB_PASSWORD=dev_password
DATABASE_URL=postgresql://dev_user:dev_password@localhost:5434/fintech_dev

# Auth0 Configuration for Local Development
AUTH0_DOMAIN=localhost:3002
AUTH0_API_AUDIENCE=my-api
AUTH0_ALGORITHMS=RS256
AUTH0_ISSUER=http://localhost:3002

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Environment
ENVIRONMENT=development
DEBUG=true
