"""Test the DDD service layer integration."""

import sys
import traceback
from pathlib import Path
from unittest.mock import MagicMock, <PERSON><PERSON>


def test_services_library_integration():
    """Test that services library integrates correctly with API."""
    # Mock fintech.models to avoid dependency issues
    mock_models = MagicMock()
    mock_models.repositories = MagicMock()
    sys.modules["fintech.models"] = mock_models
    sys.modules["fintech.models.repositories"] = mock_models.repositories

    try:
        # Import services - this should work
        from fintech.services import (
            AccountService,
            CardService,
            TransactionService,
            UserService,
        )

        # Test that classes exist and can be instantiated
        user_service = UserService()
        card_service = CardService()
        transaction_service = TransactionService()
        account_service = AccountService()

        assert user_service is not None
        assert card_service is not None
        assert transaction_service is not None
        assert account_service is not None

        print("✅ Services library integration successful")

    except ImportError as e:
        assert False, f"Failed to import services: {e}"


def test_services_domain_logic():
    """Test that services domain logic works correctly."""
    # Mock fintech.models
    mock_models = MagicMock()
    sys.modules["fintech.models"] = mock_models
    sys.modules["fintech.models.repositories"] = mock_models.repositories

    from fintech.services import (
        AccountService,
        CardService,
        TransactionService,
        UserService,
    )

    # Test UserService domain logic
    user_service = UserService()
    mock_user = Mock()
    mock_user.first_name = "John"
    welcome_msg = user_service._create_welcome_message(mock_user)
    assert welcome_msg == "Welcome back, John!"

    # Test CardService domain logic
    card_service = CardService()
    assert card_service._determine_spending_status(95.0) == "critical"
    assert card_service._determine_spending_status(30.0) == "low"

    # Test TransactionService domain logic
    transaction_service = TransactionService()
    mock_transactions = [Mock(), Mock()]
    mock_transactions[0].category = "Food"
    mock_transactions[0].amount = 25.0
    mock_transactions[1].category = "Food"
    mock_transactions[1].amount = 35.0

    result = transaction_service._analyze_categories(mock_transactions)
    assert "Food" in result
    assert result["Food"]["count"] == 2
    assert result["Food"]["amount"] == 60.0

    # Test AccountService domain logic
    account_service = AccountService()
    overview = account_service._calculate_financial_overview(mock_transactions)
    assert overview["total_spent"] == 60.0
    assert overview["transaction_count"] == 2

    print("✅ Services domain logic working correctly")


def test_api_routes_structure():
    """Test that API routes structure is correct."""
    # Get the API directory relative to this test file
    test_file = Path(__file__)
    api_dir = test_file.parent.parent

    # Check API structure
    assert (api_dir / "src" / "fintech" / "api").exists(), "API source should exist"

    # Check that routes exist
    routes_dir = api_dir / "src" / "fintech" / "api" / "routes"
    assert routes_dir.exists(), "Routes directory should exist"

    route_files = ["users.py", "cards.py", "transactions.py", "account.py"]
    for route_file in route_files:
        route_path = routes_dir / route_file
        if route_path.exists():
            content = route_path.read_text()
            # Should import from services library
            assert "fintech.services" in content, (
                f"{route_file} should import from fintech.services"
            )

    print("✅ API routes structure correct")


def test_services_follow_ddd_principles():
    """Test that services follow DDD principles."""
    # Get the services directory
    test_file = Path(__file__)
    project_root = test_file.parent.parent.parent.parent
    services_dir = project_root / "libs" / "services" / "src" / "fintech" / "services"

    service_files = [
        "user_service.py",
        "card_service.py",
        "transaction_service.py",
        "account_service.py",
    ]

    for service_file in service_files:
        service_path = services_dir / service_file
        if service_path.exists():
            content = service_path.read_text()

            # Should have class definition
            service_class = (
                service_file.replace("_service.py", "").title().replace("_", "")
                + "Service"
            )
            assert f"class {service_class}" in content, (
                f"Should have {service_class} class"
            )

            # Should have domain logic methods (private methods starting with _)
            assert "def _" in content, (
                f"{service_file} should have domain logic methods"
            )

            # Should import from repositories
            assert "from fintech.models.repositories import" in content, (
                f"{service_file} should import repositories"
            )

    print("✅ Services follow DDD principles")


if __name__ == "__main__":
    """Run tests when executed directly."""
    import traceback

    tests = [
        test_services_library_integration,
        test_services_domain_logic,
        test_api_routes_structure,
        test_services_follow_ddd_principles,
    ]

    print("🧪 Running API Services Integration Tests")
    print("=" * 60)

    passed = 0
    failed = 0

    for test in tests:
        try:
            test()
            print(f"✅ {test.__name__}")
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: {e}")
            traceback.print_exc()
            failed += 1

    print("\n" + "=" * 60)
    print(f"📊 Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All API integration tests passed!")
        print("\n📋 Summary:")
        print("✅ Services library integration working")
        print("✅ Services domain logic validated")
        print("✅ API routes structure correct")
        print("✅ Services follow DDD principles")
        print("\n🚀 API is ready for development!")
    else:
        print(f"⚠️  {failed} tests failed - check output above")
