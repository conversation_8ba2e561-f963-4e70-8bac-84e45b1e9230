#!/usr/bin/env python3
"""Simple test that works in PyCharm regardless of environment configuration."""

import sys
import traceback
from pathlib import Path


def test_python_environment():
    """Test that Python environment is working."""
    assert sys.version_info >= (3, 12), "Python 3.12+ required"
    print(f"✅ Python version: {sys.version}")


def test_project_structure():
    """Test that project structure is accessible."""
    # Get the project root (should be 3 levels up from this test file)
    test_file = Path(__file__)
    api_dir = test_file.parent.parent  # services/api
    project_root = api_dir.parent.parent  # fintech-monorepo

    # Check key directories exist
    assert api_dir.exists(), f"API directory should exist: {api_dir}"
    assert (project_root / "libs" / "services").exists(), (
        "Services library should exist"
    )
    assert (project_root / "libs" / "models").exists(), "Models library should exist"

    print(f"✅ Project root: {project_root}")
    print(f"✅ API directory: {api_dir}")


def test_services_library_structure():
    """Test that services library has correct structure."""
    test_file = Path(__file__)
    project_root = test_file.parent.parent.parent.parent
    services_dir = project_root / "libs" / "services"

    # Check services library structure
    assert services_dir.exists(), "Services library directory should exist"
    assert (services_dir / "pyproject.toml").exists(), (
        "Services pyproject.toml should exist"
    )
    assert (services_dir / "README.md").exists(), "Services README should exist"

    # Check source structure
    src_dir = services_dir / "src" / "fintech" / "services"
    assert src_dir.exists(), "Services source directory should exist"
    assert (src_dir / "__init__.py").exists(), "Services __init__.py should exist"

    # Check service files
    service_files = [
        "user_service.py",
        "card_service.py",
        "transaction_service.py",
        "account_service.py",
    ]

    for service_file in service_files:
        service_path = src_dir / service_file
        assert service_path.exists(), f"{service_file} should exist"

        # Check that file contains expected class
        content = service_path.read_text()
        service_class = (
            service_file.replace("_service.py", "").title().replace("_", "") + "Service"
        )
        assert f"class {service_class}" in content, f"Should have {service_class} class"

    print("✅ Services library structure verified")


def test_services_follow_ddd():
    """Test that services follow DDD principles."""
    test_file = Path(__file__)
    project_root = test_file.parent.parent.parent.parent
    services_dir = project_root / "libs" / "services" / "src" / "fintech" / "services"

    service_files = [
        "user_service.py",
        "card_service.py",
        "transaction_service.py",
        "account_service.py",
    ]

    for service_file in service_files:
        service_path = services_dir / service_file
        content = service_path.read_text()

        # Should have domain logic methods (private methods starting with _)
        assert "def _" in content, f"{service_file} should have domain logic methods"

        # Should import from repositories
        assert "from fintech.models.repositories import" in content, (
            f"{service_file} should import repositories"
        )

        # Should not use response models directly (clean separation)
        assert "Response.model_validate" not in content, (
            f"{service_file} should not use response models directly"
        )

    print("✅ Services follow DDD principles")


def test_api_structure():
    """Test that API structure is correct."""
    test_file = Path(__file__)
    api_dir = test_file.parent.parent

    # Check API structure
    assert (api_dir / "pyproject.toml").exists(), "API pyproject.toml should exist"
    assert (api_dir / "src" / "fintech" / "api").exists(), "API source should exist"

    # Check that routes exist
    routes_dir = api_dir / "src" / "fintech" / "api" / "routes"
    assert routes_dir.exists(), "Routes directory should exist"

    route_files = ["users.py", "cards.py", "transactions.py", "account.py"]
    for route_file in route_files:
        route_path = routes_dir / route_file
        if route_path.exists():
            content = route_path.read_text()
            # Should import from services library
            assert "fintech.services" in content, (
                f"{route_file} should import from fintech.services"
            )

    print("✅ API structure verified")


def test_documentation_exists():
    """Test that documentation exists."""
    test_file = Path(__file__)
    project_root = test_file.parent.parent.parent.parent

    # Check services library documentation
    services_dir = project_root / "libs" / "services"
    readme_path = services_dir / "README.md"

    assert readme_path.exists(), "Services README should exist"

    readme_content = readme_path.read_text()
    assert "DDD" in readme_content or "Domain-Driven Design" in readme_content, (
        "README should mention DDD"
    )
    assert "application layer" in readme_content.lower(), (
        "README should mention application layer"
    )

    print("✅ Documentation verified")


if __name__ == "__main__":
    """Run tests when executed directly."""

    tests = [
        test_python_environment,
        test_project_structure,
        test_services_library_structure,
        test_services_follow_ddd,
        test_api_structure,
        test_documentation_exists,
    ]

    print("🧪 Running PyCharm-Compatible Tests")
    print("=" * 50)

    passed = 0
    failed = 0

    for test in tests:
        try:
            test()
            print(f"✅ {test.__name__}")
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: {e}")
            traceback.print_exc()
            failed += 1

    print("\n" + "=" * 50)
    print(f"📊 Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All tests passed!")
        print("\n📋 Summary:")
        print("✅ Python environment working")
        print("✅ Project structure correct")
        print("✅ Services library structure verified")
        print("✅ Services follow DDD principles")
        print("✅ API structure correct")
        print("✅ Documentation exists")
        print("\n🚀 Services library is ready for development!")
    else:
        print(f"⚠️  {failed} tests failed - check output above")
