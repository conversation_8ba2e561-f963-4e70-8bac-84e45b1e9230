"""Test database integration for the API."""

import os
import sys
from pathlib import Path
from uuid import UUID

from fastapi.testclient import TestClient

# Set up environment for testing
os.environ["DB_HOST"] = "localhost"
os.environ["DB_PORT"] = "5433"
os.environ["DB_NAME"] = "fintech_test"
os.environ["DB_USER"] = "test_user"
os.environ["DB_PASSWORD"] = "test_password"

# Add the src directory to Python path
src_dir = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_dir))

from fintech.api.run_with_database import create_app

app = create_app()


class TestDatabaseIntegration:
    """Test database integration with the API."""

    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
        self.test_user_id = "550e8400-e29b-41d4-a716-************"

    def test_health_endpoint(self):
        """Test health endpoint works."""
        response = self.client.get("/health/")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "fintech-api"

    def test_root_endpoint(self):
        """Test root endpoint works."""
        response = self.client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "FinTech API"
        assert "endpoints" in data

    def test_user_profile_endpoint_with_database(self):
        """Test user profile endpoint connects to database."""
        # Test with default user ID
        response = self.client.get("/api/users/profile")

        # Should either return user data (200) or user not found (404)
        # Both are valid responses indicating database connection works
        assert response.status_code in [200, 404]

        if response.status_code == 200:
            data = response.json()
            assert "id" in data
            assert "email" in data
            assert "welcome_message" in data
            # Validate UUID format
            UUID(data["id"])  # This will raise ValueError if invalid
        else:
            # 404 means database connection works but user not found
            data = response.json()
            assert "detail" in data
            assert "not found" in data["detail"].lower()

    def test_user_profile_endpoint_with_specific_user(self):
        """Test user profile endpoint with specific user ID."""
        response = self.client.get(f"/api/users/profile?user_id={self.test_user_id}")

        # Should either return user data (200) or user not found (404)
        assert response.status_code in [200, 404]

        if response.status_code == 200:
            data = response.json()
            assert data["id"] == self.test_user_id
            assert "email" in data
            assert "welcome_message" in data
        else:
            # 404 is expected if test user doesn't exist
            data = response.json()
            assert "detail" in data

    def test_user_profile_endpoint_invalid_uuid(self):
        """Test user profile endpoint with invalid UUID."""
        response = self.client.get("/api/users/profile?user_id=invalid-uuid")

        # Should return 400 for invalid UUID format
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "invalid" in data["detail"].lower()

    def test_database_connection_error_handling(self):
        """Test that database connection errors are handled properly."""
        # This test verifies that the endpoint can handle database issues gracefully
        # If database is down, should return 500, not crash
        response = self.client.get("/api/users/profile")

        # Valid responses: 200 (success), 404 (user not found), 500 (db error)
        assert response.status_code in [200, 404, 500]

        # Response should always be valid JSON
        data = response.json()
        assert isinstance(data, dict)
