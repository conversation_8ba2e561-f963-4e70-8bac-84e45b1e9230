# Test Environment Configuration for API Service

# Database Configuration
DB_HOST=localhost
DB_PORT=5433
DB_NAME=fintech_test
DB_USER=test_user
DB_PASSWORD=test_password
DATABASE_URL=postgresql://test_user:test_password@localhost:5433/fintech_test

# Auth0 Configuration for Testing
AUTH0_DOMAIN=localhost:3001
AUTH0_API_AUDIENCE=my-api
AUTH0_ALGORITHMS=RS256
AUTH0_ISSUER=http://localhost:3001

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Environment
ENVIRONMENT=test
DEBUG=true
