# FinTech API Endpoints

This document describes the new API endpoints for the FinTech application, including setup instructions and testing procedures.

## 🚀 Quick Start

### 1. Setup Database and Test Data

```bash
# Navigate to API service directory
cd services/api

# Install dependencies
uv sync

# Setup database with test data
python scripts/seed_database.py

# Run API server (Standard FastAPI)
uv run uvicorn app:app --reload --host 0.0.0.0 --port 8000
```

### 2. Test Endpoints

```bash
# Run automated tests
python test_fastapi.py

# Or test manually
curl http://localhost:8000/api/user/profile?user_id=550e8400-e29b-41d4-a716-************
```

### 3. Import Postman Collection

1. Open Postman
2. Import `services/api/postman/FinTech_API_Collection.json`
3. Set environment variable `base_url` to `http://localhost:8000`
4. Run the collection

## 📋 API Endpoints

### 1. GET /api/user/profile

**Purpose**: Fetch user's basic profile information

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "email": "<EMAIL>",
  "first_name": "Adsa",
  "last_name": "Doe",
  "welcome_message": "Welcome back, Adsa!"
}
```

### 2. GET /api/account/summary

**Purpose**: Display user's financial summary

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "total_balance": 1847.50,
  "currency": "EUR",
  "active_goals": 1,
  "cards_connected": 1,
  "today_savings": 6.30
}
```

### 3. GET /api/savings/goal

**Purpose**: Get details of user's main savings goal

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "title": "Vacation 2026",
  "target_amount": 2000.0,
  "saved_amount": 310.0,
  "projected_amount": 2130.0,
  "progress_percent": 15.5,
  "time_left_months": 13,
  "monthly_savings_needed": 130.0,
  "strategy": "standard",
  "status": "on_track",
  "currency": "EUR"
}
```

### 4. GET /api/savings/today/summary

**Purpose**: Show today's savings performance

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "date": "2025-06-15",
  "amount": 6.30,
  "transactions_count": 4,
  "currency": "EUR"
}
```

### 5. GET /api/savings/today/feed

**Purpose**: Feed of today's savings from transactions

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "transactions": [
    {
      "title": "Starbucks",
      "amount": 4.30,
      "saved": 0.915,
      "currency": "EUR",
      "timestamp": "2025-06-15T09:15:00Z"
    },
    {
      "title": "Lunch Spot",
      "amount": 20.00,
      "saved": 1.00,
      "currency": "EUR",
      "timestamp": "2025-06-15T12:30:00Z"
    }
  ]
}
```

### 6. GET /api/savings/methods

**Purpose**: Get current saving methods configuration

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "round_ups": {
    "active": true,
    "description": "Round up purchases to the nearest euro",
    "example": "€4.30 coffee → €0.70 saved"
  },
  "percentage_saving": {
    "active": true,
    "percentage": 5.0,
    "example": "€20 lunch → €1.00 saved"
  },
  "recurring_transfers": {
    "active": false,
    "interval": null,
    "amount": null
  }
}
```

### 7. GET /api/investing/strategy

**Purpose**: Show current and available investment strategies

**Parameters**:
- `user_id` (query): User UUID (defaults to test user)

**Example Response**:
```json
{
  "current": {
    "name": "Standard",
    "risk": "Balanced Risk",
    "expected_return_range": "5-6%",
    "projected_goal_value": 2130,
    "months_left": 11
  },
  "available": [
    {
      "name": "Fixed Income",
      "description": "Conservative approach with steady, predictable returns",
      "risk": "Low Risk",
      "expected_return_range": "3-4%",
      "projected_value": 1450,
      "target_date": "2026-07"
    }
  ]
}
```

## 🗄️ Database Schema

The API uses the following main models:

- **User**: User accounts with profile information
- **Goal**: Savings goals with targets and strategies
- **Transaction**: Financial transactions from cards
- **Contribution**: Savings contributions (roundups, percentage, recurring)
- **Card**: Payment cards linked to users
- **UserSettings**: User preferences for saving methods

## 🧪 Test Data

The seed script creates:

- Test user: `<EMAIL>` (ID: `550e8400-e29b-41d4-a716-************`)
- Savings goal: "Vacation 2026" (€2,000 target)
- Historical transactions (last 30 days)
- Today's transactions (Starbucks, Lunch, Uber)
- Contributions from roundups and percentage saving
- User settings with roundups and percentage saving enabled

## 🔧 Development

### Service Layer Architecture

The API follows a clean architecture pattern:

```
routes/ -> services/ -> repositories/ -> models/
```

- **Routes**: FastAPI endpoint handlers
- **Services**: Business logic and data transformation
- **Repositories**: Data access layer (from models library)
- **Models**: SQLAlchemy models and database operations

### Adding New Endpoints

1. Create service method in appropriate service class
2. Add route handler in routes directory
3. Include router in main app
4. Add tests to Postman collection
5. Update documentation

## 📊 API Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**: Ensure PostgreSQL is running and models library is properly configured
2. **Import Errors**: Make sure you're in the correct directory and dependencies are installed
3. **Test User Not Found**: Run the seed script to create test data
4. **Port Already in Use**: Change the port in uvicorn command or kill existing process

### Debug Commands

```bash
# Check if API is running
curl http://localhost:8000/health

# Check database connection
python -c "from fintech.models import engine; print(engine.url)"

# Recreate test data
python scripts/seed_database.py
```
