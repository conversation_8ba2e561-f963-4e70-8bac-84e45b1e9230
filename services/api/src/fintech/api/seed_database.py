#!/usr/bin/env python3
"""Database seeding script for the FinTech API."""

import os
from decimal import Decimal
from uuid import UUID

# Set database URL for local API database
os.environ.setdefault(
    "DATABASE_URL", "postgresql://test_user:test_password@localhost:5433/fintech_test"
)

# Import existing models from the models library
from fintech.models import Base, Card, Transaction, User, engine, hash_password
from fintech.models.database import SessionLocal


def seed_database():
    """Seed the database with sample data."""
    print("🌱 Seeding database with sample data...")

    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created")

    # Create sample data
    db = SessionLocal()
    try:
        # Check if data already exists
        existing_user = db.query(User).first()
        if existing_user:
            print("✅ Database already has data, skipping seed")
            return

        # Create sample user
        user = User(
            id=UUID("550e8400-e29b-41d4-a716-************"),
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="Doe",
            country="Germany",
            currency="EUR",
            password_hash=hash_password("password123"),
        )
        db.add(user)
        db.flush()
        print(f"✅ Created user: {user.email}")

        # Create sample cards
        card1 = Card(
            user_id=user.id,
            card_name="Main Debit Card",
            card_type="debit",
            last_four="1234",
            spending_limit=Decimal("1000.00"),
        )
        card2 = Card(
            user_id=user.id,
            card_name="Credit Card",
            card_type="credit",
            last_four="5678",
            spending_limit=Decimal("5000.00"),
        )
        db.add(card1)
        db.add(card2)
        db.flush()
        print(f"✅ Created {2} cards")

        # Create sample transactions
        transactions = [
            Transaction(
                user_id=user.id,
                card_id=card1.id,
                amount=Decimal("25.50"),
                merchant_name="Coffee Shop",
                category="Food & Drink",
            ),
            Transaction(
                user_id=user.id,
                card_id=card1.id,
                amount=Decimal("120.00"),
                merchant_name="Grocery Store",
                category="Groceries",
            ),
            Transaction(
                user_id=user.id,
                card_id=card2.id,
                amount=Decimal("89.99"),
                merchant_name="Online Store",
                category="Shopping",
            ),
            Transaction(
                user_id=user.id,
                card_id=card1.id,
                amount=Decimal("15.75"),
                merchant_name="Fast Food",
                category="Food & Drink",
            ),
            Transaction(
                user_id=user.id,
                card_id=card2.id,
                amount=Decimal("299.99"),
                merchant_name="Electronics Store",
                category="Electronics",
            ),
            Transaction(
                user_id=user.id,
                card_id=card1.id,
                amount=Decimal("45.20"),
                merchant_name="Gas Station",
                category="Transportation",
            ),
            Transaction(
                user_id=user.id,
                card_id=card2.id,
                amount=Decimal("199.99"),
                merchant_name="Clothing Store",
                category="Shopping",
            ),
            Transaction(
                user_id=user.id,
                card_id=card1.id,
                amount=Decimal("8.50"),
                merchant_name="Bakery",
                category="Food & Drink",
            ),
        ]

        for transaction in transactions:
            db.add(transaction)

        db.commit()
        print(f"✅ Created {len(transactions)} transactions")
        print("🎉 Database seeding completed successfully!")

    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def reset_database():
    """Reset the database by dropping and recreating all tables."""
    print("🔄 Resetting database...")
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    print("✅ Database reset completed")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        reset_database()

    seed_database()
