#!/usr/bin/env python3
"""Run the FinTech API server with database integration."""

import os

# Set database URL for local API database
os.environ.setdefault(
    "DATABASE_URL", "postgresql://test_user:test_password@localhost:5433/fintech_test"
)

# Import FastAPI and middleware
# Import all routers
import sys
from pathlib import Path

from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

# Import models and database
from fintech.models import Card, Transaction, User, get_db

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from routes import (
    account_router,
    cards_router,
    goals_router,
    health_router,
    transactions_router,
    users_router,
)


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="FinTech API",
        description="FinTech API with PostgreSQL database integration",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include all routers
    app.include_router(health_router)
    app.include_router(users_router)
    app.include_router(cards_router)
    app.include_router(transactions_router)
    app.include_router(goals_router)
    app.include_router(account_router)

    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "message": "FinTech API",
            "version": "1.0.0",
            "docs": "/docs",
            "status": "running",
            "database": "PostgreSQL",
            "endpoints": [
                "/health/",
                "/api/users/profile",
                "/api/cards/",
                "/api/transactions/",
                "/api/goals/",
                "/api/account/summary",
                "/seed-status",
            ],
        }

    @app.get("/seed-status")
    async def seed_status(db: Session = Depends(get_db)):
        """Check if database has been seeded."""
        user_count = db.query(User).count()
        card_count = db.query(Card).count()
        transaction_count = db.query(Transaction).count()

        return {
            "seeded": user_count > 0,
            "user_count": user_count,
            "card_count": card_count,
            "transaction_count": transaction_count,
            "message": (
                "Database seeded successfully"
                if user_count > 0
                else "Database not seeded"
            ),
        }

    return app


if __name__ == "__main__":
    import sys
    from pathlib import Path

    import uvicorn

    # Add the current directory to Python path for imports
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))

    from seed_database import seed_database

    # Seed database with sample data
    try:
        seed_database()
    except Exception as e:
        print(f"⚠️  Warning: Database seeding failed: {e}")
        print("🔄 Continuing with server startup...")

    # Create the FastAPI app
    app = create_app()
    seed_database()
    print("🚀 Starting FinTech API server...")
    print("📍 Database: PostgreSQL")
    print("🌐 Docs: http://localhost:8000/docs")
    print("🔗 API: http://localhost:8000")
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
