"""Auth0 JWT authentication for FastAPI."""

import json
from urllib.request import urlopen

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from jose import JWTError, jwt
from pydantic import BaseModel
from pydantic_settings import BaseSettings


class Auth0Settings(BaseSettings):
    """Auth0 configuration settings."""

    auth0_domain: str = "test.auth0.com"
    auth0_api_audience: str = "test-audience"
    auth0_algorithms: str = "RS256"

    class Config:
        env_file = ".env"
        env_prefix = "AUTH0_"


class User(BaseModel):
    """User model from JWT token."""

    sub: str  # Auth0 user ID
    email: str | None = None
    name: str | None = None
    nickname: str | None = None
    picture: str | None = None
    email_verified: bool | None = None


class Auth0JWTBearer(HTTPBearer):
    """Auth0 JWT Bearer token authentication."""

    def __init__(self, auto_error: bool = True):
        super().__init__(auto_error=auto_error)
        self.settings = Auth0Settings()
        self._jwks_cache: dict | None = None

    def get_jwks(self) -> dict:
        """Get JWKS from Auth0."""
        if self._jwks_cache is None:
            jwks_url = f"https://{self.settings.auth0_domain}/.well-known/jwks.json"
            with urlopen(jwks_url) as response:
                self._jwks_cache = json.loads(response.read())
        return self._jwks_cache

    def get_rsa_key(self, token: str) -> dict:
        """Get RSA key for token verification."""
        try:
            unverified_header = jwt.get_unverified_header(token)
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid token header"
            )

        jwks = self.get_jwks()
        rsa_key = {}

        for key in jwks["keys"]:
            if key["kid"] == unverified_header["kid"]:
                rsa_key = {
                    "kty": key["kty"],
                    "kid": key["kid"],
                    "use": key["use"],
                    "n": key["n"],
                    "e": key["e"],
                }
                break

        if not rsa_key:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Unable to find appropriate key",
            )

        return rsa_key

    async def verify_token(self, credentials: HTTPAuthorizationCredentials) -> User:
        """Verify JWT token and return user."""
        if not credentials:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header is expected",
            )

        token = credentials.credentials
        rsa_key = self.get_rsa_key(token)

        try:
            payload = jwt.decode(
                token,
                rsa_key,
                algorithms=[self.settings.auth0_algorithms],
                audience=self.settings.auth0_api_audience,
                issuer=f"https://{self.settings.auth0_domain}/",
            )
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="Token has expired"
            )
        except jwt.JWTClaimsError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid claims. Please check the audience and issuer",
            )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Unable to parse authentication token",
            )

        # Extract user information from payload
        user_data = {
            "sub": payload.get("sub"),
            "email": payload.get("email"),
            "name": payload.get("name"),
            "nickname": payload.get("nickname"),
            "picture": payload.get("picture"),
            "email_verified": payload.get("email_verified", False),
        }

        return User(**user_data)


# Create global instances
auth0_bearer = Auth0JWTBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(HTTPBearer())) -> User:
    """Dependency to get current authenticated user."""
    return await auth0_bearer.verify_token(credentials)


async def get_optional_user(
    credentials: HTTPAuthorizationCredentials | None = Depends(
        HTTPBearer(auto_error=False)
    ),
) -> User | None:
    """Dependency to get current user if authenticated, None otherwise."""
    if not credentials:
        return None

    try:
        return await auth0_bearer.verify_token(credentials)
    except HTTPException:
        return None
