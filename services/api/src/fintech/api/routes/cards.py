"""Card API endpoints following DDD architecture.

This module contains the presentation layer (controllers) for card-related operations.
Routes are kept thin and delegate all business logic to the service layer.
"""

from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from fintech.models import CardResponse, get_db
from fintech.services import card_service

router = APIRouter(prefix="/api/cards", tags=["cards"])


@router.get("/", response_model=list[CardResponse])
async def get_user_cards(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch cards for",
    ),
    db: Session = Depends(get_db),
) -> list[CardResponse]:
    """Get all cards for a specific user.

    This endpoint delegates all business logic to the card service layer.
    """
    try:
        user_uuid = UUID(user_id)
        return card_service.get_user_cards(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/{card_id}", response_model=CardResponse)
async def get_card_details(
    card_id: str,
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID for authorization",
    ),
    db: Session = Depends(get_db),
) -> CardResponse:
    """Get specific card details with ownership validation.

    This endpoint delegates all business logic to the card service layer.
    """
    try:
        card_uuid = UUID(card_id)
        user_uuid = UUID(user_id)

        return card_service.get_card_details(db, card_uuid, user_uuid)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid ID format: {e}") from e
    except Exception as e:
        # Service layer exceptions are converted to HTTP exceptions
        if "not found" in str(e).lower():
            raise HTTPException(status_code=404, detail=str(e)) from e
        if "does not belong" in str(e).lower():
            raise HTTPException(status_code=403, detail=str(e)) from e
        raise HTTPException(status_code=500, detail="Internal server error") from e
