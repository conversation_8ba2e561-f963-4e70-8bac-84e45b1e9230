"""Authentication-related API routes."""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel

from ..auth import get_current_user, get_optional_user, User


router = APIRouter(prefix="/auth", tags=["authentication"])


class UserResponse(BaseModel):
    """User response model."""
    
    sub: str
    email: str | None = None
    name: str | None = None
    nickname: str | None = None
    picture: str | None = None
    email_verified: bool = False


class ProtectedResponse(BaseModel):
    """Protected endpoint response."""
    
    message: str
    user: UserResponse
    timestamp: str


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)) -> UserResponse:
    """Get current authenticated user information."""
    return UserResponse(
        sub=current_user.sub,
        email=current_user.email,
        name=current_user.name,
        nickname=current_user.nickname,
        picture=current_user.picture,
        email_verified=current_user.email_verified or False
    )


@router.get("/protected")
async def protected_endpoint(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """Example protected endpoint that requires authentication."""
    from datetime import datetime
    
    return {
        "message": "This is a protected endpoint",
        "user": {
            "sub": current_user.sub,
            "email": current_user.email,
            "name": current_user.name,
        },
        "timestamp": datetime.utcnow().isoformat(),
        "access_granted": True
    }


@router.get("/public")
async def public_endpoint() -> Dict[str, Any]:
    """Example public endpoint that doesn't require authentication."""
    from datetime import datetime
    
    return {
        "message": "This is a public endpoint",
        "timestamp": datetime.utcnow().isoformat(),
        "authentication_required": False
    }


@router.get("/optional-auth")
async def optional_auth_endpoint(current_user: User | None = Depends(get_optional_user)) -> Dict[str, Any]:
    """Example endpoint with optional authentication."""
    from datetime import datetime
    
    if current_user:
        return {
            "message": "Hello authenticated user!",
            "user": {
                "sub": current_user.sub,
                "email": current_user.email,
                "name": current_user.name,
            },
            "timestamp": datetime.utcnow().isoformat(),
            "authenticated": True
        }
    else:
        return {
            "message": "Hello anonymous user!",
            "timestamp": datetime.utcnow().isoformat(),
            "authenticated": False
        }
