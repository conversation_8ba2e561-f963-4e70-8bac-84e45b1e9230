"""Transaction API endpoints following DDD architecture.

This module contains the presentation layer (controllers) for transaction-related operations.
Routes are kept thin and delegate all business logic to the service layer.
"""

# Import service with absolute path
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from fintech.models import TransactionResponse, get_db
from fintech.services import transaction_service

router = APIRouter(prefix="/api/transactions", tags=["transactions"])


@router.get("/", response_model=list[TransactionResponse])
async def get_user_transactions(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch transactions for",
    ),
    skip: int = Query(default=0, ge=0, description="Number of transactions to skip"),
    limit: int = Query(
        default=50, ge=1, le=100, description="Number of transactions to return"
    ),
    db: Session = Depends(get_db),
) -> list[TransactionResponse]:
    """Get user's transactions with pagination.

    This endpoint delegates all business logic to the transaction service layer.
    """
    try:
        user_uuid = UUID(user_id)
        return transaction_service.get_user_transactions(db, user_uuid, skip, limit)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction_details(
    transaction_id: str,
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID for authorization",
    ),
    db: Session = Depends(get_db),
) -> TransactionResponse:
    """Get specific transaction details with ownership validation.

    This endpoint delegates all business logic to the transaction service layer.
    """
    try:
        transaction_uuid = UUID(transaction_id)
        user_uuid = UUID(user_id)

        return transaction_service.get_transaction_details(
            db, transaction_uuid, user_uuid
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid ID format: {e}") from e
    except Exception as e:
        # Service layer exceptions are converted to HTTP exceptions
        if "not found" in str(e).lower():
            raise HTTPException(status_code=404, detail=str(e)) from e
        if "does not belong" in str(e).lower():
            raise HTTPException(status_code=403, detail=str(e)) from e
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/summary/stats")
async def get_transaction_stats(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch transaction stats for",
    ),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """Get user's transaction statistics.

    This endpoint delegates all business logic to the transaction service layer.
    """
    try:
        user_uuid = UUID(user_id)
        return transaction_service.get_transaction_statistics(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e
