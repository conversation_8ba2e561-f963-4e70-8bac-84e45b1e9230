"""Account summary API endpoints following DDD architecture.

This module contains the presentation layer (controllers) for account-related operations.
Routes are kept thin and delegate all business logic to the service layer.
"""

# Import service with absolute path
from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from fintech.models import get_db
from fintech.services import account_service

router = APIRouter(prefix="/api/account", tags=["account"])


@router.get("/summary")
async def get_account_summary(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch account summary for",
    ),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """Get comprehensive account summary with all user data.

    This endpoint delegates all business logic to the account service layer.
    """
    try:
        user_uuid = UUID(user_id)
        return account_service.get_account_summary(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        # Service layer exceptions are converted to HTTP exceptions
        if "not found" in str(e).lower():
            raise HTTPException(status_code=404, detail=str(e)) from e
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/dashboard")
async def get_dashboard_data(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch dashboard data for",
    ),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """Get essential dashboard data for quick overview.

    This endpoint delegates all business logic to the account service layer.
    """
    try:
        user_uuid = UUID(user_id)
        return account_service.get_dashboard_data(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        # Service layer exceptions are converted to HTTP exceptions
        if "not found" in str(e).lower():
            raise HTTPException(status_code=404, detail=str(e)) from e
        raise HTTPException(status_code=500, detail="Internal server error") from e
