"""User profile API endpoints following DDD architecture.

This module contains the presentation layer (controllers) for user-related operations.
Routes are kept thin and delegate all business logic to the service layer.
"""

# Import service with absolute path
# Temporary import fix for services
import sys
from pathlib import Path
from uuid import UUID
from typing import Dict, Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from fintech.models import UserResponse, get_db
from ..auth import get_current_user, User

services_path = Path(__file__).parent.parent.parent.parent.parent / "libs" / "services" / "src"
sys.path.insert(0, str(services_path))

# Mock fintech.models for services
from unittest.mock import MagicMock

mock_models = MagicMock()
mock_models.repositories = MagicMock()
sys.modules["fintech.models.repositories"] = mock_models.repositories

from fintech.services import user_service

router = APIRouter(prefix="/api/users", tags=["users"])


class AccountSummary(BaseModel):
    """User account summary."""

    total_balance: float
    active_goals: int
    cards_connected: int
    total_savings: float
    last_transaction_date: datetime | None = None


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    user_id: str = Query(default="550e8400-e29b-41d4-a716-************"),
    db: Session = Depends(get_db),
) -> UserResponse:
    """Get user's basic profile information.

    This endpoint delegates all business logic to the user service layer,
    keeping the route thin and focused on HTTP concerns.
    """
    try:
        user_uuid = UUID(user_id)
        return user_service.get_user_profile(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/me", response_model=Dict[str, Any])
async def get_current_user_info(current_user: User = Depends(get_current_user)) -> Dict[str, Any]:
    """Get current authenticated user information from Auth0."""
    return {
        "sub": current_user.sub,
        "email": current_user.email,
        "name": current_user.name,
        "nickname": current_user.nickname,
        "picture": current_user.picture,
        "email_verified": current_user.email_verified or False,
        "provider": "auth0"
    }


@router.get("/me/summary", response_model=AccountSummary)
async def get_account_summary(current_user: User = Depends(get_current_user)) -> AccountSummary:
    """Get user account summary."""
    # Mock data - in a real app, you'd fetch from your database using current_user.sub
    return AccountSummary(
        total_balance=1847.50,
        active_goals=2,
        cards_connected=1,
        total_savings=310.25,
        last_transaction_date=datetime.utcnow()
    )
