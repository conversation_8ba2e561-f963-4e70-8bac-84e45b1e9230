"""User profile API endpoints following DDD architecture.

This module contains the presentation layer (controllers) for user-related operations.
Routes are kept thin and delegate all business logic to the service layer.
"""

# Import service with absolute path
# Temporary import fix for services
import sys
from pathlib import Path
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from fintech.models import UserResponse, get_db

services_path = Path(__file__).parent.parent.parent.parent.parent / "libs" / "services" / "src"
sys.path.insert(0, str(services_path))

# Mock fintech.models for services
from unittest.mock import MagicMock

mock_models = MagicMock()
mock_models.repositories = MagicMock()
sys.modules["fintech.models.repositories"] = mock_models.repositories

from fintech.services import user_service

router = APIRouter(prefix="/api/users", tags=["users"])


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(
    user_id: str = Query(default="550e8400-e29b-41d4-a716-************"),
    db: Session = Depends(get_db),
) -> UserResponse:
    """Get user's basic profile information.

    This endpoint delegates all business logic to the user service layer,
    keeping the route thin and focused on HTTP concerns.
    """
    try:
        user_uuid = UUID(user_id)
        return user_service.get_user_profile(db, user_uuid)

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e
