"""Goal API endpoints with database integration."""

from typing import Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from fintech.models import Goal, GoalResponse, Investment, get_db

router = APIRouter(prefix="/api/goals", tags=["goals"])


@router.get("/", response_model=list[GoalResponse])
async def get_user_goals(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch goals for",
    ),
    db: Session = Depends(get_db),
) -> list[GoalResponse]:
    """Get all goals for a specific user."""
    try:
        user_uuid = UUID(user_id)
        goals = db.query(Goal).filter(Goal.user_id == user_uuid).all()

        # Convert to response models
        return [GoalResponse.model_validate(goal) for goal in goals]

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/{goal_id}", response_model=GoalResponse)
async def get_goal_details(
    goal_id: str,
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID for authorization",
    ),
    db: Session = Depends(get_db),
) -> GoalResponse:
    """Get specific goal details by goal ID."""
    try:
        goal_uuid = UUID(goal_id)
        user_uuid = UUID(user_id)

        goal = db.query(Goal).filter(Goal.id == goal_uuid).first()

        if not goal:
            raise HTTPException(
                status_code=404, detail=f"Goal with ID {goal_id} not found"
            )

        # Verify goal belongs to the user
        if goal.user_id != user_uuid:
            raise HTTPException(
                status_code=403, detail="Access denied: Goal does not belong to user"
            )

        return GoalResponse.model_validate(goal)

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid ID format: {e}") from e
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/{goal_id}/progress")
async def get_goal_progress(
    goal_id: str,
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID for authorization",
    ),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """Get goal progress including investments and savings."""
    try:
        goal_uuid = UUID(goal_id)
        user_uuid = UUID(user_id)

        goal = db.query(Goal).filter(Goal.id == goal_uuid).first()

        if not goal:
            raise HTTPException(
                status_code=404, detail=f"Goal with ID {goal_id} not found"
            )

        # Verify goal belongs to the user
        if goal.user_id != user_uuid:
            raise HTTPException(
                status_code=403, detail="Access denied: Goal does not belong to user"
            )

        # Get investments for this goal
        investments = db.query(Investment).filter(Investment.goal_id == goal_uuid).all()

        # Calculate progress
        total_invested = sum(float(inv.amount_invested or 0) for inv in investments)
        target_amount = float(goal.target_amount or 0)
        progress_percentage = (
            (total_invested / target_amount * 100) if target_amount > 0 else 0
        )

        # Calculate days remaining
        days_remaining = None
        if goal.target_date:
            from datetime import date

            today = date.today()
            if goal.target_date > today:
                days_remaining = (goal.target_date - today).days
            else:
                days_remaining = 0

        return {
            "goal_id": str(goal.id),
            "goal_name": goal.name,
            "target_amount": target_amount,
            "current_amount": round(total_invested, 2),
            "progress_percentage": round(progress_percentage, 2),
            "remaining_amount": round(max(0, target_amount - total_invested), 2),
            "target_date": goal.target_date.isoformat() if goal.target_date else None,
            "days_remaining": days_remaining,
            "strategy": goal.strategy,
            "total_investments": len(investments),
            "is_completed": progress_percentage >= 100,
            "investments": [
                {
                    "id": str(inv.id),
                    "amount": float(inv.amount_invested or 0),
                    "portfolio_type": inv.portfolio_type,
                    "projected_return": float(inv.projected_return or 0),
                    "invested_at": inv.invested_at.isoformat(),
                }
                for inv in investments
            ],
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid ID format: {e}") from e
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e


@router.get("/summary/overview")
async def get_goals_overview(
    user_id: str = Query(
        default="550e8400-e29b-41d4-a716-************",
        description="User ID to fetch goals overview for",
    ),
    db: Session = Depends(get_db),
) -> dict[str, Any]:
    """Get user's goals overview with summary statistics."""
    try:
        user_uuid = UUID(user_id)

        # Get all user goals
        goals = db.query(Goal).filter(Goal.user_id == user_uuid).all()

        if not goals:
            return {
                "total_goals": 0,
                "active_goals": 0,
                "completed_goals": 0,
                "total_target_amount": 0.0,
                "total_saved_amount": 0.0,
                "overall_progress": 0.0,
                "goals_by_strategy": {},
                "recent_goals": [],
            }

        # Calculate statistics
        total_goals = len(goals)
        total_target = sum(float(g.target_amount or 0) for g in goals)

        # Get investments for all goals to calculate saved amounts
        total_saved = 0.0
        completed_goals = 0
        goals_by_strategy = {}

        # Get all investments for the user once
        all_investments = (
            db.query(Investment).filter(Investment.user_id == user_uuid).all()
        )

        for goal in goals:
            # Filter investments for this specific goal
            goal_investments = [
                inv for inv in all_investments if inv.goal_id == goal.id
            ]
            goal_saved = sum(
                float(inv.amount_invested or 0) for inv in goal_investments
            )
            total_saved += goal_saved

            # Check if goal is completed
            target = float(goal.target_amount or 0)
            if target > 0 and goal_saved >= target:
                completed_goals += 1

            # Strategy breakdown
            strategy = goal.strategy or "Unknown"
            if strategy not in goals_by_strategy:
                goals_by_strategy[strategy] = {
                    "count": 0,
                    "target_amount": 0.0,
                    "saved_amount": 0.0,
                }
            goals_by_strategy[strategy]["count"] += 1
            goals_by_strategy[strategy]["target_amount"] += target
            goals_by_strategy[strategy]["saved_amount"] += goal_saved

        overall_progress = (total_saved / total_target * 100) if total_target > 0 else 0
        active_goals = total_goals - completed_goals

        # Recent goals (last 3)
        recent_goals = sorted(goals, key=lambda x: x.created_at, reverse=True)[:3]
        recent_data = [
            {
                "id": str(g.id),
                "name": g.name,
                "target_amount": float(g.target_amount or 0),
                "target_date": g.target_date.isoformat() if g.target_date else None,
                "strategy": g.strategy,
                "created_at": g.created_at.isoformat(),
            }
            for g in recent_goals
        ]

        return {
            "total_goals": total_goals,
            "active_goals": active_goals,
            "completed_goals": completed_goals,
            "total_target_amount": round(total_target, 2),
            "total_saved_amount": round(total_saved, 2),
            "overall_progress": round(overall_progress, 2),
            "goals_by_strategy": goals_by_strategy,
            "recent_goals": recent_data,
        }

    except ValueError as e:
        raise HTTPException(
            status_code=400, detail=f"Invalid user ID format: {e}"
        ) from e
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error") from e
