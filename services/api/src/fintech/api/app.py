#!/usr/bin/env python3
"""FastAPI app instance for uvicorn with reload support."""

import os
import sys
from pathlib import Path

# Set database URL for local API database
os.environ.setdefault(
    "DATABASE_URL", "postgresql://test_user:test_password@localhost:5433/fintech_test"
)

# Set database environment variables if not already set
os.environ.setdefault("DB_HOST", "localhost")
os.environ.setdefault("DB_PORT", "5433")
os.environ.setdefault("DB_NAME", "fintech_test")
os.environ.setdefault("DB_USER", "test_user")
os.environ.setdefault("DB_PASSWORD", "test_password")

# Import FastAPI and middleware
from fastapi import Depends, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

# Import models and database
from fintech.models import Card, Transaction, User, get_db

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from routes import (
    account_router,
    cards_router,
    goals_router,
    health_router,
    transactions_router,
    users_router,
)
from routes.auth import router as auth_router


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    app = FastAPI(
        title="FinTech API",
        description="FinTech API with PostgreSQL database integration",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            "http://localhost:3000",  # Next.js dev server
            "http://localhost:3001",  # Alternative port
            "http://localhost:3002",  # Alternative port
            "https://your-domain.vercel.app",  # Production frontend
        ],
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
    )

    # Include all routers
    app.include_router(health_router)
    app.include_router(auth_router)  # Auth0 authentication routes
    app.include_router(users_router)
    app.include_router(cards_router)
    app.include_router(transactions_router)
    app.include_router(goals_router)
    app.include_router(account_router)

    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "message": "FinTech API",
            "version": "1.0.0",
            "docs": "/docs",
            "status": "running",
            "database": "PostgreSQL",
            "endpoints": [
                "/health/",
                "/auth/me",
                "/auth/protected",
                "/auth/public",
                "/api/users/profile",
                "/api/users/me",
                "/api/cards/",
                "/api/transactions/",
                "/api/goals/",
                "/api/account/summary",
                "/seed-status",
            ],
        }

    @app.get("/seed-status")
    async def seed_status(db: Session = Depends(get_db)):
        """Check if database has been seeded."""
        user_count = db.query(User).count()
        card_count = db.query(Card).count()
        transaction_count = db.query(Transaction).count()

        return {
            "seeded": user_count > 0,
            "user_count": user_count,
            "card_count": card_count,
            "transaction_count": transaction_count,
            "message": (
                "Database seeded successfully"
                if user_count > 0
                else "Database not seeded"
            ),
        }

    return app


# Create the app instance for uvicorn
app = create_app()
