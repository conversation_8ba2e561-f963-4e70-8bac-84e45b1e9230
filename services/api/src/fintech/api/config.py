"""API configuration settings."""

from pydantic import Field
from pydantic_settings import BaseSettings


class APISettings(BaseSettings):
    """API configuration settings."""

    # API Settings
    title: str = Field(default="FinTech API", description="API title")
    description: str = Field(
        default="FinTech Banking API", description="API description"
    )
    version: str = Field(default="1.0.0", description="API version")
    debug: bool = Field(default=True, description="Debug mode")

    # Server Settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")

    # Security Settings
    secret_key: str = Field(
        default="your-secret-key-change-in-production", description="JWT secret key"
    )
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(
        default=30, description="Access token expiration in minutes"
    )

    # CORS Settings
    cors_origins: list[str] = Field(default=["*"], description="CORS allowed origins")
    cors_methods: list[str] = Field(default=["*"], description="CORS allowed methods")
    cors_headers: list[str] = Field(default=["*"], description="CORS allowed headers")

    class Config:
        env_prefix = "API_"
        case_sensitive = False


# Global settings instance
settings = APISettings()
