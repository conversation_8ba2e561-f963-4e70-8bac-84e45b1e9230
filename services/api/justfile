uv:= require("uv")
docker := require("docker")

run-api:
    # 1. Start PostgreSQL Database
    cd ../../libs/models && {{docker}} compose -f ../../libs/models/docker-compose.test.yml up -d

    # 2. Install Dependencies
    {{uv}} sync

    # 3. Manual Database Seeding (Optional)
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run python src/fintech/api/seed_database.py --reset

    # 4. Start API Server
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000