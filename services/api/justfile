uv:= require("uv")
docker := require("docker")

# Backend Development Commands
# ============================

# Start full backend development environment (PostgreSQL + Auth0 + API)
[group: 'Development']
dev-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting full backend development environment..."

    # Start PostgreSQL and Auth0
    {{docker}} compose -f docker-compose.dev.yml up -d

    # Wait for services to be ready
    echo "⏳ Waiting for services to be ready..."

    # Wait for PostgreSQL
    timeout=60
    counter=0
    while [ $counter -lt $timeout ]; do
        if nc -z localhost 5434 2>/dev/null; then
            echo "✅ PostgreSQL is ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    # Wait for Auth0
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ Auth0 mock is ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    echo "🎉 Backend development environment is ready!"
    echo "   🐘 PostgreSQL: localhost:5434 (fintech_dev)"
    echo "   🔐 Auth0 Mock: http://localhost:3001"
    echo "   👤 Test User: <EMAIL> / Test1234"

# Start only database and Auth0 (no API)
[group: 'Development']
services-start:
    #!/usr/bin/env bash
    echo "🔧 Starting backend services (PostgreSQL + Auth0)..."
    {{docker}} compose -f docker-compose.dev.yml up -d

# Stop development environment
[group: 'Development']
dev-stop:
    #!/usr/bin/env bash
    echo "🛑 Stopping backend development environment..."
    {{docker}} compose -f docker-compose.dev.yml down

# Start API with development database
[group: 'Development']
api-dev:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting API with development database..."

    # Ensure development services are running
    just services-start

    # Install dependencies
    {{uv}} sync

    # Set development database environment variables
    export DB_HOST=localhost
    export DB_PORT=5434
    export DB_NAME=fintech_dev
    export DB_USER=dev_user
    export DB_PASSWORD=dev_password

    # Seed the development database
    echo "🌱 Seeding development database..."
    {{uv}} run python src/fintech/api/seed_database.py --reset

    # Start API server
    echo "🔄 Starting API server with auto-reload..."
    echo "📍 Database: PostgreSQL (localhost:5434)"
    echo "🔗 API: http://localhost:8000"
    echo "📚 Docs: http://localhost:8000/docs"
    echo "🔐 Auth0: http://localhost:3001"
    echo ""

    {{uv}} run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000

# Original test environment command (backward compatibility)
run-api:
    # 1. Start PostgreSQL Database and Auth0
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml up -d

    # 2. Install Dependencies
    {{uv}} sync

    # 3. Manual Database Seeding (Optional)
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run python src/fintech/api/seed_database.py --reset

    # 4. Start API Server
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000

# Testing Commands
# ================

# Start test environment (PostgreSQL + Auth0)
[group: 'Testing']
test-env-start:
    #!/usr/bin/env bash
    echo "🧪 Starting test environment..."
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml up -d

# Stop test environment
[group: 'Testing']
test-env-stop:
    #!/usr/bin/env bash
    echo "🛑 Stopping test environment..."
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml down

# Run API tests with test environment
[group: 'Testing']
test-api:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🧪 Running API tests..."

    # Start test environment
    just test-env-start

    # Wait for services
    echo "⏳ Waiting for test services..."
    timeout=60
    counter=0
    while [ $counter -lt $timeout ]; do
        if nc -z localhost 5433 2>/dev/null && curl -f http://localhost:3002/health > /dev/null 2>&1; then
            echo "✅ Test services are ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    # Run tests
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run pytest test/ -v

    # Cleanup
    just test-env-stop

# Utility Commands
# ================

# View services status
[group: 'Utility']
status:
    #!/usr/bin/env bash
    echo "📊 Backend services status:"
    echo ""
    echo "Development environment:"
    {{docker}} compose -f docker-compose.dev.yml ps
    echo ""
    echo "Test environment:"
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml ps

# View logs
[group: 'Utility']
logs service="" env="dev":
    #!/usr/bin/env bash
    if [ "{{env}}" = "test" ]; then
        compose_file="../../libs/models/docker-compose.test.yml"
        cd ../../libs/models
    else
        compose_file="docker-compose.dev.yml"
    fi

    if [ -n "{{service}}" ]; then
        echo "📝 Viewing logs for {{service}} ({{env}})..."
        {{docker}} compose -f $compose_file logs -f {{service}}
    else
        echo "📝 Viewing all logs ({{env}})..."
        {{docker}} compose -f $compose_file logs -f
    fi

# Clean up all Docker resources
[group: 'Utility']
clean:
    #!/usr/bin/env bash
    echo "🧹 Cleaning up backend Docker resources..."
    {{docker}} compose -f docker-compose.dev.yml down -v || true
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml down -v || true
    {{docker}} image prune -f
    echo "✅ Cleanup completed!"