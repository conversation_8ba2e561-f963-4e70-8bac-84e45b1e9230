uv:= require("uv")
docker := require("docker")

# Backend Development Commands
# ============================

# Start full backend development environment (PostgreSQL + Auth0 + API)
[group: 'Development']
dev-start:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting full backend development environment..."

    # Start PostgreSQL and Auth0
    cd ../../libs/models && {{docker}} compose -f docker-compose.dev.yml up -d

    # Wait for services to be ready
    echo "⏳ Waiting for services to be ready..."

    # Wait for PostgreSQL
    timeout=60
    counter=0
    while [ $counter -lt $timeout ]; do
        if nc -z localhost 5434 2>/dev/null; then
            echo "✅ PostgreSQL is ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    # Wait for Auth0
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ Auth0 mock is ready!"
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done

    echo "🎉 Backend development environment is ready!"
    echo "   🐘 PostgreSQL: localhost:5434 (fintech_dev)"
    echo "   🔐 Auth0 Mock: http://localhost:3001"
    echo "   👤 Test User: <EMAIL> / Test1234"

# Start only database and Auth0 (no API)
[group: 'Development']
services-start:
    #!/usr/bin/env bash
    echo "🔧 Starting backend services (PostgreSQL + Auth0)..."
    cd ../../libs/models && {{docker}} compose -f docker-compose.dev.yml up -d

# Stop development environment
[group: 'Development']
dev-stop:
    #!/usr/bin/env bash
    echo "🛑 Stopping backend development environment..."
    cd ../../libs/models && {{docker}} compose -f docker-compose.dev.yml down

# Start API with development database
[group: 'Development']
api-dev:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Starting API with development database..."

    # Ensure development services are running
    just services-start

    # Install dependencies
    {{uv}} sync

    # Set development database environment variables
    export DB_HOST=localhost
    export DB_PORT=5434
    export DB_NAME=fintech_dev
    export DB_USER=dev_user
    export DB_PASSWORD=dev_password

    # Seed the development database
    echo "🌱 Seeding development database..."
    {{uv}} run python src/fintech/api/seed_database.py --reset

    # Start API server
    echo "🔄 Starting API server with auto-reload..."
    echo "📍 Database: PostgreSQL (localhost:5434)"
    echo "🔗 API: http://localhost:8000"
    echo "📚 Docs: http://localhost:8000/docs"
    echo "🔐 Auth0: http://localhost:3001"
    echo ""

    {{uv}} run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000

# Original test environment command (backward compatibility)
run-api:
    # 1. Start PostgreSQL Database and Auth0
    cd ../../libs/models && {{docker}} compose -f docker-compose.test.yml up -d

    # 2. Install Dependencies
    {{uv}} sync

    # 3. Manual Database Seeding (Optional)
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run python src/fintech/api/seed_database.py --reset

    # 4. Start API Server
    DB_HOST=localhost DB_PORT=5433 DB_NAME=fintech_test DB_USER=test_user DB_PASSWORD=test_password \
    {{uv}} run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000