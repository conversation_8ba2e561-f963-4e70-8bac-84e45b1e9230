services:
  # PostgreSQL Database for development
  postgres-dev:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5434:5432"  # Use 5434 for development to avoid conflict with test DB
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d fintech_dev"]
      interval: 5s
      timeout: 5s
      retries: 5
    tmpfs:
      - /tmp
      - /var/run/postgresql
    networks:
      - fintech-backend-network

  # Local Auth0 Mock for development
  localauth0-dev:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.2
    ports:
      - "3002:3000"  # Expose on 3002 for Auth0 mock to avoid conflict with frontend
    environment:
      - AUTH0_AUDIENCE=my-api
      - AUTH0_CLIENT_ID=my-client-id
      - AUTH0_CLIENT_SECRET=my-client-secret
      - AUTH0_DOMAIN=localhost:3002
      - AUTH0_ISSUER=http://localhost:3002
      - AUTH0_BASE_URL=http://localhost:3002
      # Configure test user with required permissions for API access
      - AUTH0_USERS=[{"email":"<EMAIL>","password":"Test1234","user_id":"auth0|123456789","name":"Test User","roles":["user","admin"],"permissions":["read:profile","read:dashboard","read:investments","read:transactions","read:cards","read:goals","write:profile","write:cards","write:goals"]}]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fintech-backend-network

networks:
  fintech-backend-network:
    driver: bridge

volumes:
  postgres_dev_data:
    driver: local
