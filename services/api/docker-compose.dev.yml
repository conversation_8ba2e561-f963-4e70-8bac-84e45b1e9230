services:
  # PostgreSQL Database for development
  postgres-dev:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5434:5432"  # Use 5434 for development to avoid conflict with test DB
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U dev_user -d fintech_dev"]
      interval: 5s
      timeout: 5s
      retries: 5
    tmpfs:
      - /tmp
      - /var/run/postgresql
    networks:
      - fintech-backend-network

  # Local Auth0 Mock for development with user registration support
  localauth0-dev:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.3
    ports:
      - "3002:3000"  # Expose on 3002 for Auth0 mock to avoid conflict with frontend
    environment:
      LOCALAUTH0_CONFIG: |
        issuer = "http://localhost:3002"

        # Enable user registration and dynamic user creation
        [auth]
        allow_registration = true
        default_password_policy = "weak"  # For testing only

        [user_info]
        # Default user info template for new registrations
        email_verified = true
        picture = "https://via.placeholder.com/150"

        [[audience]]
        name = "my-api"
        permissions = [
          "read:profile",
          "read:dashboard",
          "read:investments",
          "read:transactions",
          "read:cards",
          "read:goals",
          "write:profile",
          "write:cards",
          "write:goals"
        ]

        # Pre-configured test users for E2E testing
        [[users]]
        email = "<EMAIL>"
        password = "Test1234"
        name = "Test User"
        given_name = "Test"
        family_name = "User"
        nickname = "testuser"
        email_verified = true

        [[users]]
        email = "<EMAIL>"
        password = "E2E1234"
        name = "E2E Test User"
        given_name = "E2E"
        family_name = "User"
        nickname = "e2euser"
        email_verified = true
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fintech-backend-network

networks:
  fintech-backend-network:
    driver: bridge

volumes:
  postgres_dev_data:
    driver: local
