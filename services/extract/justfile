set allow-duplicate-variables := true

tf-dir      := './infrastructure'
docker-dir  := './../..'
docker-file := 'service.Dockerfile'

import '../../just/terraform.just'
import '../../just/docker.just'
import '../../just/lambda.just'
import '../../just/versioning.just'

# Build and deploy the elaboration service to the given <env>, tagging it with the given <version>.
build-and-deploy env version=auto-version: (build version) (deploy env version)

# Build the elaboration service's Docker image, tagging it with the given <version>.
build version=auto-version: (docker-build 'fintech/lambda-example' version '--build-arg FINTECH_SERVICE=lambda-example')

# Deploy the elaboration service to the given <env>, using the given <version> of the Docker image.
deploy env version=auto-version: \
    (tf-deploy env) \
    (docker-push-ecr env 'jobfit/extraction' version) \
    (lambda-deploy-docker env 'fintech-lambda' 'fintech/lambda-example' version)
