# --------------------------------------------------
# Project Configuration
# --------------------------------------------------

terraform {
  backend "s3" {
    bucket               = "fintech-tf-states"
    workspace_key_prefix = "fintech-lambda-service"
    key                  = "state.tfstate"
    region               = "eu-central-1"
    encrypt              = true
    kms_key_id           = ""
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.75.1"
    }
  }

  required_version = ">= 1.5.2"
}

provider "aws" {
  region = "eu-central-1"

  assume_role {
    role_arn = local.settings.role_arn
  }
}

provider "aws" {
  alias  = "shared"
  region = "eu-central-1"
}

variable "workspace_settings" {
  type = map(object({
    role_arn          = string
    sensitive_logging = bool
  }))

  default = {
    dev = {
      role_arn          = ""
      sensitive_logging = true
    }

    prod = {
      role_arn          = ""
      sensitive_logging = false
    }
  }
}

locals {
  env                               = terraform.workspace
  op_env                            = local.env == "prod" ? "PROD" : "STAGE"
  fintech_networking_profile_prefix = "fintech-networking"
  marketplace_token_s3_key          = "marketplace-token.json"

  settings = {
    role_arn          = var.workspace_settings[local.env].role_arn
    sensitive_logging = var.workspace_settings[local.env].sensitive_logging
  }

  project = {
    name = "fintech-lambda"
    env  = local.env
  }
}
