locals {
  variant_config = jsonencode({
    for variant, config in local.settings.variants :
    variant => {
      sns_topic                 = aws_sns_topic.variant_topic[variant].arn
      candidate_extraction_type = config.candidate_extraction
      job_extraction_type       = config.job_extraction
    }
  })
}

resource "aws_lambda_function" "extraction_function" {
  function_name = "${local.project.name}-${local.project.env}"
  package_type  = "Image"

  # Will be replaced with the actual image during deployment.
  image_uri = "${data.aws_ecr_repository.dummy.repository_url}:latest"

  memory_size = 1028
  timeout     = 60
  publish     = true

  role = aws_iam_role.lambda_role.arn

  environment {
    variables = {
      INSIGHTS_TABLE_NAME              = data.aws_dynamodb_table.insights.name
      CANDIDATE_EXTRACTIONS_TABLE_NAME = data.aws_dynamodb_table.candidate_extractions.name
      JOB_EXTRACTIONS_TABLE_NAME       = data.aws_dynamodb_table.job_extractions.name
      MARKETPLACE_API_BASE_URL         = local.settings.marketplace_api_base_url
      MARKETPLACE_ATS_BASE_URL         = local.settings.marketplace_ats_base_url
      MARKETPLACE_AUTH_URL             = local.settings.marketplace_auth_url
      AVRO_URL                         = local.settings.avro_url
      MEDIA_API_BASE_URL               = local.settings.media_api_base_url
      DATAPOOL_API_BASE_URL            = local.settings.datapool_api_base_url
      S3_JWT_KEY                       = local.marketplace_token_s3_key
      S3_JWT_BUCKET                    = data.aws_s3_bucket.jwt_token.bucket
      RETRY_SQS_URL                    = aws_sqs_queue.lambda.url
      TEXTRACT_AWS_REGION              = local.settings.textract_aws_region
      VARIANTS                         = local.variant_config
      APPLICATION_VARIANTS             = jsonencode(["b2b-1"])
      APPLICATION_LANGUAGES            = jsonencode(["en"])
      SENSITIVE_LOGGING                = local.settings.sensitive_logging
    }
  }

  dead_letter_config {
    target_arn = aws_sqs_queue.dlq.arn
  }

  vpc_config {
    security_group_ids = [data.aws_security_group.lambda_sg.id]
    subnet_ids         = [data.aws_subnet.private_subnet.id]
  }

  lifecycle {
    ignore_changes = [image_uri]
  }

  depends_on = [aws_iam_role.lambda_role]

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}"
  }
}

resource "aws_cloudwatch_log_group" "lambda_log_group" {
  name              = "/aws/lambda/${aws_lambda_function.extraction_function.function_name}"
  retention_in_days = 90
}

module "lambda_metric_filters" {
  source              = "../../../infrastructure/shared-modules/metric-filters"
  log_group_name      = aws_cloudwatch_log_group.lambda_log_group.name
  metric_namespace    = "${local.project.name}-${local.project.env}"
  alarm_name_prefix   = "${local.project.name}-${local.project.env}-lambda"
  alarm_sns_topic_arn = data.aws_sns_topic.alert_topic.arn
  log_field           = "$.level"
}

/*
  IAM
*/
resource "aws_iam_role" "lambda_role" {
  name = "${local.project.name}-${local.project.env}-function-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
    ]
  })

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-function-role"
  }
}

resource "aws_iam_role_policy" "lambda_policy_cloudwatch" {
  name = "${local.project.name}-${local.project.env}-function-policy-cloudwatch"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = [
          aws_cloudwatch_log_group.lambda_log_group.arn,
          "${aws_cloudwatch_log_group.lambda_log_group.arn}:log-stream:*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_policy_manage_network_interfaces" {
  name = "${local.project.name}-${local.project.env}-function-manage-network-interfaces"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ]

        //IMPORTANT: when you create policy first time, put "*" here, as function doesn't exists.
        Resource = "*"
        //Resource = aws_lambda_function.extraction_function.arn
      },
    ]
  })
}

resource "aws_iam_role_policy" "lambda_policy_dynamodb" {
  name = "${local.project.name}-${local.project.env}-dynamodb"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:DeleteItem",
          "dynamodb:UpdateItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem"
        ]

        Resource = [
          data.aws_dynamodb_table.insights.arn,
          data.aws_dynamodb_table.candidate_extractions.arn,
          data.aws_dynamodb_table.job_extractions.arn
        ]
      },
    ]
  })
}

resource "aws_iam_role_policy" "lambda_policy_marketplace_token" {
  name = "${local.project.name}-${local.project.env}-marketplace-token"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
        ]

        Resource = [
          "${data.aws_s3_bucket.jwt_token.arn}/*",
          "${data.aws_s3_bucket.jwt_token.arn}/${local.marketplace_token_s3_key}"
        ]

      },
    ]
  })
}

resource "aws_lambda_event_source_mapping" "document_kafka_trigger" {
  count                  = local.settings.enable_documents_kafka ? 1 : 0
  function_name          = aws_lambda_function.extraction_function.arn
  topics                 = ["marketplace.ats.entity.document"]
  starting_position      = local.settings.kafka_starting_position
  batch_size             = 1
  maximum_retry_attempts = 0
  enabled                = true

  self_managed_event_source {
    endpoints = {
      KAFKA_BOOTSTRAP_SERVERS = local.settings.kafka_endpoint
    }
  }

  source_access_configuration {
    type = "CLIENT_CERTIFICATE_TLS_AUTH"
    uri  = data.aws_secretsmanager_secret.jobfit_consumer_cert.arn
  }

  source_access_configuration {
    type = "SERVER_ROOT_CA_CERTIFICATE"
    uri  = data.aws_secretsmanager_secret.ca_cert.arn
  }

  destination_config {
    on_failure {
      destination_arn = aws_sqs_queue.dlq.arn
    }
  }
}

resource "aws_iam_role_policy" "lambda_policy_secretsmanager" {
  name = "${local.project.name}-${local.project.env}-secretsmanager"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow"
        Action = "secretsmanager:GetSecretValue"
        Resource = [
          data.aws_secretsmanager_secret.jobfit_consumer_cert.arn,
          data.aws_secretsmanager_secret.ca_cert.arn
        ]
      }
    ]
  })
}


resource "aws_iam_role_policy" "textract_policy" {
  name = "${local.project.name}-${local.project.env}-textract-policy"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "textract:DetectDocumentText",
          "textract:AnalyzeDocument"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_sqs_queue" "lambda" {
  name                       = "${local.project.name}-${local.project.env}-lambda"
  message_retention_seconds  = 3600
  visibility_timeout_seconds = 0

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.dlq.arn
    maxReceiveCount     = 1
  })
}

resource "aws_iam_role_policy" "sqs_policy" {
  name = "${local.project.name}-${local.project.env}-lambda-sqs-policy"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = ["sqs:SendMessage", "sqs:ReceiveMessage", "sqs:DeleteMessage", "sqs:GetQueueAttributes"]
        Resource = [aws_sqs_queue.lambda.arn, aws_sqs_queue.dlq.arn]
      },
      {
        Effect   = "Allow"
        Action   = ["lambda:InvokeFunction"]
        Resource = aws_lambda_function.extraction_function.arn
      }
    ]
  })
}

resource "aws_lambda_event_source_mapping" "lambda_sqs_trigger" {
  event_source_arn = aws_sqs_queue.lambda.arn
  function_name    = aws_lambda_function.extraction_function.arn
  batch_size       = 1
  enabled          = true
}

resource "aws_sqs_queue" "dlq" {
  name = "${local.project.name}-${local.project.env}-lambda-sqs-dlq"
}
