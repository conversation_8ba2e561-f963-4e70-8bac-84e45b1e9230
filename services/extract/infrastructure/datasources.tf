# SPOTTED DEV
data "onepassword_vault" "spotted_dev" {
  name = "Spotted Dev"
}

data "onepassword_item" "datapool_api_token" {
  vault = data.onepassword_vault.spotted_dev.uuid
  title = "Datapool API Token (${local.op_env})"
}

data "onepassword_item" "avro_schema" {
  vault = data.onepassword_vault.spotted_dev.uuid
  title = "Aiven schema registry (${local.op_env})"
}

data "onepassword_item" "marketplace_api_client" {
  vault = data.onepassword_vault.spotted_dev.uuid
  title = "auth0 marketplace-apis_job-application-matcher client (${local.op_env})"
}

data "aws_subnet" "private_subnet" {
  filter {
    name   = "tag:Name"
    values = ["${local.fintech_networking_profile_prefix}-${local.env}-private-subnet"]
  }
}

data "aws_security_group" "lambda_sg" {
  filter {
    name   = "tag:Name"
    values = ["${local.fintech_networking_profile_prefix}-${local.env}-sg-lambda"]
  }
}

data "aws_sns_topic" "alert_topic" {
  name = "${local.fintech_networking_profile_prefix}-${local.env}-alarms"
}

data "aws_dynamodb_table" "insights" {
  name = "jobfit-insights-${local.env}"
}

data "aws_dynamodb_table" "candidate_extractions" {
  name = "jobfit-candidate-extractions-${local.env}"
}

data "aws_dynamodb_table" "job_extractions" {
  name = "jobfit-job-extractions-${local.env}"
}

data "aws_s3_bucket" "jwt_token" {
  bucket = "jobfit-services-shared-${local.env}-utils"
}

data "aws_ecr_repository" "dummy" {
  provider = aws.shared
  name     = "dummy"
}

data "aws_secretsmanager_secret" "jobfit_consumer_cert" {
  name = "jobfit-services-shared/kafka/jobcloud-services/jobfit_consumer/cert"
}

data "aws_secretsmanager_secret" "ca_cert" {
  name = "jobfit-services-shared/kafka/cacert"
}

