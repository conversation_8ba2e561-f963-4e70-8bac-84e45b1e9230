resource "aws_sns_topic" "variant_topic" {
  for_each = toset(keys(local.settings.variants))

  name = "jobfit-extraction-${local.env}-${each.key}-out"
}

resource "aws_iam_role_policy" "lambda_policy_sns" {
  for_each = toset(keys(local.settings.variants))

  name = "jobfit-extraction-${local.env}-${each.key}-lambda-sns"
  role = aws_iam_role.lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:Publish",
        ]

        Resource = aws_sns_topic.variant_topic[each.key].arn
      },
    ]
  })
}
