resource "aws_iam_role_policy" "lambda_ssm_access" {
  role = aws_iam_role.lambda_role.id
  name = "${local.project.name}-${local.project.env}-ssm"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action   = ["ssm:GetParameter", "ssm:GetParameters", "ssm:GetParametersByPath"],
        Effect   = "Allow",
        Resource = ["*"]
      }
    ]
  })
}

resource "aws_ssm_parameter" "config_marketplace_api_client_id" {
  name  = "/${local.project.name}/MARKETPLACE_API_CLIENT_ID"
  type  = "SecureString"
  value = data.onepassword_item.marketplace_api_client.username
}

resource "aws_ssm_parameter" "config_marketplace_api_client_secret" {
  name  = "/${local.project.name}/MARKETPLACE_API_CLIENT_SECRET"
  type  = "SecureString"
  value = data.onepassword_item.marketplace_api_client.password
}

resource "aws_ssm_parameter" "config_avro_username" {
  name  = "/${local.project.name}/AVRO_USERNAME"
  type  = "SecureString"
  value = data.onepassword_item.avro_schema.username
}

resource "aws_ssm_parameter" "config_avro_password" {
  name  = "/${local.project.name}/AVRO_PASSWORD"
  type  = "SecureString"
  value = data.onepassword_item.avro_schema.password
}

resource "aws_ssm_parameter" "config_datapool_token" {
  name  = "/${local.project.name}/DATAPOOL_API_TOKEN"
  type  = "SecureString"
  value = data.onepassword_item.datapool_api_token.password
}
