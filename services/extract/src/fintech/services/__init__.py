"""FinTech Services namespace package for extract service."""
__path__ = __import__("pkgutil").extend_path(__path__, __name__)

# Import services library components to make them available
try:
    import os
    import sys

    # Add the services library to the path if not already there
    services_lib_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "..", "..", "libs", "services", "src")
    services_lib_path = os.path.abspath(services_lib_path)

    if services_lib_path not in sys.path:
        sys.path.append(services_lib_path)

    # Import and re-export services library components
    from fintech.services import (
        account_service,
        card_service,
        transaction_service,
        user_service,
    )
    from fintech.services.account_service import AccountService
    from fintech.services.card_service import CardService
    from fintech.services.transaction_service import TransactionService
    from fintech.services.user_service import UserService

    # Make them available in this namespace
    __all__ = [
        "UserService", "CardService", "TransactionService", "AccountService",
        "user_service", "card_service", "transaction_service", "account_service"
    ]

except ImportError:
    # If services library is not available, that's OK
    pass
