from typing import Annotated, Any

from pydantic import (
    BaseModel,
    Discriminator,
    Field,
    TypeAdapter,
)


# TODO
class _KafkaRecord(BaseModel):
    value: str | None = None
    key: str


class KafkaEvent(BaseModel):
    records: dict[str, list[_KafkaRecord]]


def _determine_type(v: Any) -> str:
    if "Records" in v:
        return "SQSEvent"
    if "records" in v:
        return "KafkaEvent"

    msg = f"Invalid request {type(v)}"
    raise ValueError(msg)


Request = TypeAdapter(
    Annotated[
        Any,
        Field(Discriminator(_determine_type)),
    ]
)
