from pydantic_settings import BaseSettings

from fintech.app import is_running_locally, load_parameters


class AppConfig(BaseSettings):
    def __init__(self, **kwargs):
        shared_param_path = "/fintech-services-shared/"
        own_param_path = "/fintech-extraction/"
        if not is_running_locally():
            load_parameters(shared_param_path, with_decryption=True)
            load_parameters(own_param_path, with_decryption=True)
        super().__init__(**kwargs)

    test_env: str  # ENV: INSIGHTS_TABLE_NAME
