[project]
name = "fintech-lambda-service"
version = "0.0.0"
requires-python = "==3.12.*"

dependencies = [
    # Fintech
    "fintech-app",
    "fintech-database",
    # External dependencies
    "requests",
    "boto3",
    "pydantic",
    "pydantic_settings",
]

[project.optional-dependencies]
typing = ["boto3-stubs"]

[tool.uv.sources]
fintech-app = { path = "../../libs/app", editable = true }
fintech-database = { path = "../../libs/database", editable = true }

[tool.pytest.ini_options]
pythonpath = ["src"]

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
