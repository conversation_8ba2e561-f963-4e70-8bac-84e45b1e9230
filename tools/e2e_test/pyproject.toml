[project]
name = "fintech-tools-e2e_test"
version = "0.0.0"
requires-python = "==3.12.*"

dependencies = [
    # Fintech
    "fintech-database",
    "fintech-lambda-service",
    # External dependencies
    "boto3",
    "pytest"
]

[project.optional-dependencies]
typing = ["boto3-stubs"]

[tool.uv.sources]
fintech-database = { path = "../../libs/database", editable = true }
fintech-lambda-service = { path = "../../services/extract", editable = true }


[tool.pytest.ini_options]
pythonpath = ["src"]

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
