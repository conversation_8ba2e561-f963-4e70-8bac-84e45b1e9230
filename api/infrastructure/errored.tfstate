{"version": 4, "terraform_version": "1.5.2", "serial": 452, "lineage": "dd92caa9-a00f-8fa4-93e1-3e86517f27c8", "outputs": {}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:sts::************:assumed-role/tf-infra-dev-deploy-role/aws-go-sdk-1748590216469328000", "id": "************", "user_id": "AROAV5U4NE2I2JTSJCA7N:aws-go-sdk-1748590216469328000"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_cloudfront_cache_policy", "name": "disabled", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"comment": "Policy with caching disabled", "default_ttl": 0, "etag": "E23ZP02F085DFQ", "id": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "max_ttl": 0, "min_ttl": 0, "name": "Managed-CachingDisabled", "parameters_in_cache_key_and_forwarded_to_origin": [{"cookies_config": [{"cookie_behavior": "none", "cookies": []}], "enable_accept_encoding_brotli": false, "enable_accept_encoding_gzip": false, "headers_config": [{"header_behavior": "none", "headers": []}], "query_strings_config": [{"query_string_behavior": "none", "query_strings": []}]}]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_cloudfront_cache_policy", "name": "optimized", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"comment": "Policy with caching enabled. Supports Gzip and Brotli compression.", "default_ttl": 86400, "etag": "E23ZP02F085DFQ", "id": "658327ea-f89d-4fab-a63d-7e88639e58f6", "max_ttl": 31536000, "min_ttl": 1, "name": "Managed-CachingOptimized", "parameters_in_cache_key_and_forwarded_to_origin": [{"cookies_config": [{"cookie_behavior": "none", "cookies": []}], "enable_accept_encoding_brotli": true, "enable_accept_encoding_gzip": true, "headers_config": [{"header_behavior": "none", "headers": []}], "query_strings_config": [{"query_string_behavior": "none", "query_strings": []}]}]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_cloudfront_origin_request_policy", "name": "all_except_host", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"comment": "Policy to forward all parameters in viewer requests except for the Host header", "cookies_config": [{"cookie_behavior": "all", "cookies": []}], "etag": "E23ZP02F085DFQ", "headers_config": [{"header_behavior": "allExcept", "headers": [{"items": ["host"]}]}], "id": "b689b0a8-53d0-40ab-baf2-68738e2966ac", "name": "Managed-AllViewerExceptHostHeader", "query_strings_config": [{"query_string_behavior": "all", "query_strings": []}]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_cloudfront_response_headers_policy", "name": "simple_cors", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"comment": "Allows all origins for simple CORS requests", "cors_config": [{"access_control_allow_credentials": false, "access_control_allow_headers": [], "access_control_allow_methods": [], "access_control_allow_origins": [{"items": ["*"]}], "access_control_expose_headers": [], "access_control_max_age_sec": 0, "origin_override": false}], "custom_headers_config": [], "etag": "E23ZP02F085DFQ", "id": "60669652-455b-4ae9-85a4-c4c02393f86c", "name": "Managed-SimpleCORS", "remove_headers_config": [], "security_headers_config": [], "server_timing_headers_config": []}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_dynamodb_table", "name": "insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev", "attribute": [{"name": "application_id", "type": "S"}, {"name": "candidate_id", "type": "S"}, {"name": "id", "type": "S"}, {"name": "job_id", "type": "S"}, {"name": "specifier", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [{"hash_key": "application_id", "name": "Application-index", "non_key_attributes": ["variant", "generated", "id", "lang"], "projection_type": "INCLUDE", "range_key": "", "read_capacity": 0, "write_capacity": 0}, {"hash_key": "candidate_id", "name": "Candidate-index", "non_key_attributes": ["application_id", "variant", "generated", "id", "lang"], "projection_type": "INCLUDE", "range_key": "", "read_capacity": 0, "write_capacity": 0}, {"hash_key": "job_id", "name": "Job-index", "non_key_attributes": ["application_id", "variant", "generated", "id", "lang"], "projection_type": "INCLUDE", "range_key": "", "read_capacity": 0, "write_capacity": 0}], "hash_key": "id", "id": "jobfit-insights-dev", "local_secondary_index": [], "name": "jobfit-insights-dev", "point_in_time_recovery": [{"enabled": false}], "range_key": "specifier", "read_capacity": 0, "replica": [], "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Env": "dev", "Name": "jobfit-networking-dev-insights", "Project": "jobfit-networking"}, "ttl": [{"attribute_name": "expires", "enabled": true}], "write_capacity": 0}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_ec2_managed_prefix_list", "name": "office_admin", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"address_family": "IPv4", "arn": "arn:aws:ec2:eu-central-1:************:prefix-list/pl-0cf4ac2fe678f8926", "entries": [{"cidr": "***************/29", "description": ""}, {"cidr": "***************/32", "description": ""}, {"cidr": "************/32", "description": ""}], "filter": null, "id": "pl-0cf4ac2fe678f8926", "max_entries": 10, "name": "office-admin-ips", "owner_id": "************", "tags": {}, "timeouts": null, "version": 1}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_lambda_function", "name": "jobfit_extraction", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:eu-central-1:************:function:jobfit-extraction-dev", "code_sha256": "5e55c9f8e40fdd3645f0d38d82ea35b35b41a3af66045f12a7bfdd577614ed4b", "code_signing_config_arn": "", "dead_letter_config": [{"target_arn": "arn:aws:sqs:eu-central-1:************:jobfit-extraction-dev-lambda-sqs-dlq"}], "description": "", "environment": [{"variables": {"APPLICATION_LANGUAGES": "[\"en\"]", "APPLICATION_VARIANTS": "[\"b2b-1\"]", "AVRO_URL": "https://jobcloud-services-stage-ops-406c.aivencloud.com:24871/schemas/ids", "CANDIDATE_EXTRACTIONS_TABLE_NAME": "jobfit-candidate-extractions-dev", "DATAPOOL_API_BASE_URL": "https://datapool-stage.jobcloud.services", "INSIGHTS_TABLE_NAME": "jobfit-insights-dev", "JOB_EXTRACTIONS_TABLE_NAME": "jobfit-job-extractions-dev", "MARKETPLACE_API_BASE_URL": "https://api.stage.jobcloud.ai", "MARKETPLACE_ATS_BASE_URL": "https://ats.stage.jobcloud.ai", "MARKETPLACE_AUTH_URL": "https://auth.stage.jobcloud.ai/oauth/token", "MEDIA_API_BASE_URL": "https://stage.media.jobs.ch", "RETRY_SQS_URL": "https://sqs.eu-central-1.amazonaws.com/************/jobfit-extraction-dev-lambda", "S3_JWT_BUCKET": "jobfit-services-shared-dev-utils", "S3_JWT_KEY": "marketplace-token.json", "SENSITIVE_LOGGING": "true", "TEXTRACT_AWS_REGION": "eu-west-1", "VARIANTS": "{\"b2b-1\":{\"candidate_extraction_type\":\"textract\",\"job_extraction_type\":\"pass\",\"sns_topic\":\"arn:aws:sns:eu-central-1:************:jobfit-extraction-dev-b2b-1-out\"},\"b2c-1\":{\"candidate_extraction_type\":\"textract\",\"job_extraction_type\":\"requirements\",\"sns_topic\":\"arn:aws:sns:eu-central-1:************:jobfit-extraction-dev-b2c-1-out\"}}"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "function_name": "jobfit-extraction-dev", "handler": "", "id": "jobfit-extraction-dev", "image_uri": "897729132726.dkr.ecr.eu-central-1.amazonaws.com/jobfit/extraction:v0.1.0-54-g6dda623e-6688", "invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:jobfit-extraction-dev/invocations", "kms_key_arn": "", "last_modified": "2025-05-22T12:29:36.000+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/jobfit-extraction-dev", "system_log_level": ""}], "memory_size": 1028, "qualified_arn": "arn:aws:lambda:eu-central-1:************:function:jobfit-extraction-dev:38", "qualified_invoke_arn": "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/arn:aws:lambda:eu-central-1:************:function:jobfit-extraction-dev:38/invocations", "qualifier": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/jobfit-extraction-dev-function-role", "runtime": "", "signing_job_arn": "", "signing_profile_version_arn": "", "source_code_hash": "5e55c9f8e40fdd3645f0d38d82ea35b35b41a3af66045f12a7bfdd577614ed4b", "source_code_size": 0, "tags": {}, "timeout": 60, "tracing_config": [{"mode": "PassThrough"}], "version": "38", "vpc_config": [{"ipv6_allowed_for_dual_stack": false, "security_group_ids": ["sg-0471270ef057ed557"], "subnet_ids": ["subnet-003b545ca8d6fa507"], "vpc_id": "vpc-0ddded94f4f0b15eb"}]}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Europe (Frankfurt)", "endpoint": "ec2.eu-central-1.amazonaws.com", "id": "eu-central-1", "name": "eu-central-1"}, "sensitive_attributes": []}]}, {"mode": "data", "type": "aws_sqs_queue", "name": "jobfit_extraction", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:sqs:eu-central-1:************:jobfit-extraction-dev-lambda", "id": "https://sqs.eu-central-1.amazonaws.com/************/jobfit-extraction-dev-lambda", "name": "jobfit-extraction-dev-lambda", "tags": {}, "url": "https://sqs.eu-central-1.amazonaws.com/************/jobfit-extraction-dev-lambda"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_acm_certificate", "name": "jobfit_api_cert", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].us", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-east-1:************:certificate/8c96b744-3dba-4460-8ea8-33abaf5c8c94", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "dev.jobfit.jobcloud.services", "domain_validation_options": [{"domain_name": "*.dev.jobfit.jobcloud.services", "resource_record_name": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services.", "resource_record_type": "CNAME", "resource_record_value": "_81c665827721a392ff24ec88bc82eaa3.xlfgrmvvlj.acm-validations.aws."}, {"domain_name": "dev.jobfit.jobcloud.services", "resource_record_name": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services.", "resource_record_type": "CNAME", "resource_record_value": "_81c665827721a392ff24ec88bc82eaa3.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-east-1:************:certificate/8c96b744-3dba-4460-8ea8-33abaf5c8c94", "key_algorithm": "RSA_2048", "not_after": "2026-05-01T23:59:59Z", "not_before": "2025-04-02T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["*.dev.jobfit.jobcloud.services", "dev.jobfit.jobcloud.services"], "tags": {}, "tags_all": {}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_route53_zone.jobfit"]}]}, {"mode": "managed", "type": "aws_acm_certificate_validation", "name": "jobfit_api_cert_validation", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"].us", "instances": [{"schema_version": 0, "attributes": {"certificate_arn": "arn:aws:acm:us-east-1:************:certificate/8c96b744-3dba-4460-8ea8-33abaf5c8c94", "id": "2025-04-02 15:39:49.953 +0000 UTC", "timeouts": null, "validation_record_fqdns": ["_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services"]}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo0NTAwMDAwMDAwMDAwfX0=", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_route53_record.jobfit_api_cert_validation", "aws_route53_zone.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_api_key", "name": "api_key", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "jobfit-demo", "schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:eu-central-1::/apikeys/uw5uxw2uzj", "created_date": "2025-02-06T13:13:45Z", "customer_id": "", "description": "Managed by Terraform", "enabled": true, "id": "uw5uxw2uzj", "last_updated_date": "2025-05-15T14:28:58Z", "name": "jobfit-demo", "tags": {}, "tags_all": {}, "value": "Sn6vofs6rH6lsUjf202ZXarWyFvwB0Fp2kZhKn8U"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}, {"index_key": "jobfit-seekers-profile", "schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:eu-central-1::/apikeys/5nvfwcjbw5", "created_date": "2025-05-15T15:34:38Z", "customer_id": "", "description": "Managed by Terraform", "enabled": true, "id": "5nvfwcjbw5", "last_updated_date": "2025-05-15T15:38:25Z", "name": "jobfit-seekers-profile", "tags": {}, "tags_all": {}, "value": "dIFWiOxt77aHIGybwVpuj7oJy0wWCptc7nPo4y8c"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_deployment", "name": "jobfit", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"canary_settings": [], "created_date": "2025-05-30T07:24:52Z", "description": "", "execution_arn": "arn:aws:execute-api:eu-central-1:************:kq9dui6bcf/", "id": "obie58", "invoke_url": "https://kq9dui6bcf.execute-api.eu-central-1.amazonaws.com/", "rest_api_id": "kq9dui6bcf", "stage_description": null, "stage_name": null, "triggers": {"redeployment": "20250530072450"}, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "method_get_insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "dri0sf", "id": "kq9dui6bcf/dri0sf", "location": [{"method": "GET", "name": "", "path": "/insights/{id}/{variant}/{lang}", "status_code": "", "type": "METHOD"}], "properties": "{\"description\":\"Returns insights by internal id, if they exist.\",\"summary\":\"Fetch insights by id\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "method_get_insights_applications", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "yjdgzs", "id": "kq9dui6bcf/yjdgzs", "location": [{"method": "GET", "name": "", "path": "/insights/applications/{app-id}/{variant}/{lang}", "status_code": "", "type": "METHOD"}], "properties": "{\"description\":\"Returns insights for the given job application, if they already exist.\",\"summary\":\"Fetch insights for an application\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "method_get_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "6tr8p9", "id": "kq9dui6bcf/6tr8p9", "location": [{"method": "GET", "name": "", "path": "/insights/jobs/{job-id}/candidates/{cand-id}/{variant}/{lang}", "status_code": "", "type": "METHOD"}], "properties": "{\"description\":\"Returns insights for the given candidate and job pair, if they already exist.\",\"summary\":\"Fetch insights for a candidate and job pair\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "method_post_await_insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "upwkzb", "id": "kq9dui6bcf/upwkzb", "location": [{"method": "POST", "name": "", "path": "/await/insights/{variant}/{lang}", "status_code": "", "type": "METHOD"}], "properties": "{\"description\":\"Accepts information about some job seeker/candidate and some job, and either returns corresponding insights (if they already exist) or generates them synchronously.\\n\\nIf you want to force the insights to be regenerated even if they already exist (which is especially useful if the previous generation has ended in a potentially recoverable error), add `?force=true` to the URL.\",\"summary\":\"Synchronously fetch or generate any custom insights\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_resource.await_insights_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "method_post_await_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "qvehgr", "id": "kq9dui6bcf/qvehgr", "location": [{"method": "POST", "name": "", "path": "/await/insights/jobs/{job-id}/candidates/{cand-id}/{variant}/{lang}", "status_code": "", "type": "METHOD"}], "properties": "{\"description\":\"Given information about some job seeker/candidate and a job identifier (recognized within Datapool), returns corresponding insights (if they already exist) or generates them synchronously.\\n\\nThe insights are going to be associated with the given `job-id` and `cand-id`, so if any of the referenced entities is removed in the future, the insights and any caches will follow.\\n\\nIf you want to force the insights to be regenerated even if they already exist (which is especially useful if the previous generation has ended in a potentially recoverable error), add `?force=true` to the URL.\",\"summary\":\"Synchronously fetch or generate insights for a job-candidate pair\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_app_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "5tg264", "id": "kq9dui6bcf/5tg264", "location": [{"method": "*", "name": "app-id", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"Identifier of a job application (as generated by the application service)\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_cand_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "8aeo6f", "id": "kq9dui6bcf/8aeo6f", "location": [{"method": "*", "name": "cand-id", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"A Jobs or Jobup user id, or any other uuid (but then user removal wouldn't cause cleanup).\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "x2kaq9", "id": "kq9dui6bcf/x2kaq9", "location": [{"method": "*", "name": "id", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"Internal identifier of the insights you would like to access\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_job_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "cpqz5j", "id": "kq9dui6bcf/cpqz5j", "location": [{"method": "*", "name": "job-id", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"Identifier of the job to generate insights for (accessible through Datapool)\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "5rfl92", "id": "kq9dui6bcf/5rfl92", "location": [{"method": "*", "name": "lang", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"Desired language of the insights (de/en/fr)\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "param_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "lcrei9", "id": "kq9dui6bcf/lcrei9", "location": [{"method": "*", "name": "variant", "path": "/", "status_code": "", "type": "PATH_PARAMETER"}], "properties": "{\"description\":\"Desired flavor of the insights to be generated (b2b-1, b2c-1, etc.)\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "query_force", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "eweo9c", "id": "kq9dui6bcf/eweo9c", "location": [{"method": "*", "name": "force", "path": "/", "status_code": "", "type": "QUERY_PARAMETER"}], "properties": "{\"description\":\"Should the insights be regenerated even if they already exist (true/false)?\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "d3mz4d", "id": "kq9dui6bcf/d3mz4d", "location": [{"method": "*", "name": "", "path": "/", "status_code": "200", "type": "RESPONSE"}], "properties": "{\"description\":\"Insights generated\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_400", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "6m35ft", "id": "kq9dui6bcf/6m35ft", "location": [{"method": "*", "name": "", "path": "/", "status_code": "400", "type": "RESPONSE"}], "properties": "{\"description\":\"Malformed request\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_403", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "4a8u4c", "id": "kq9dui6bcf/4a8u4c", "location": [{"method": "*", "name": "", "path": "/", "status_code": "403", "type": "RESPONSE"}], "properties": "{\"description\":\"Access denied\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_404", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "f40uxh", "id": "kq9dui6bcf/f40uxh", "location": [{"method": "*", "name": "", "path": "/", "status_code": "404", "type": "RESPONSE"}], "properties": "{\"description\":\"Insights not found\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_422", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "08kmh8", "id": "kq9dui6bcf/08kmh8", "location": [{"method": "*", "name": "", "path": "/", "status_code": "422", "type": "RESPONSE"}], "properties": "{\"description\":\"Insights could not be generated\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_part", "name": "response_500", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"documentation_part_id": "kxzfo7", "id": "kq9dui6bcf/kxzfo7", "location": [{"method": "*", "name": "", "path": "/", "status_code": "500", "type": "RESPONSE"}], "properties": "{\"description\":\"Internal server error\"}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_documentation_version", "name": "jobfit", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "JobFit API documentation version 20250530072450", "id": "kq9dui6bcf/20250530072450", "rest_api_id": "kq9dui6bcf", "version": "20250530072450"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"], "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "get_insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "ti1l7x", "connection_id": "", "connection_type": "INTERNET", "content_handling": "CONVERT_TO_TEXT", "credentials": "arn:aws:iam::************:role/jobfit-api-dev-apigw-dynamodb-read", "http_method": "GET", "id": "agi-kq9dui6bcf-ti1l7x-GET", "integration_http_method": "POST", "passthrough_behavior": "NEVER", "request_parameters": {}, "request_templates": {"application/json": "{\"TableName\": \"$stageVariables.insightsTable\",\"Key\": {\"id\": {\"S\" : \"$util.urlDecode($input.params('id'))\"},\"specifier\": {\"S\" : \"$input.params('variant')_$input.params('lang')\"}}}"}, "resource_id": "ti1l7x", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS", "uri": "arn:aws:apigateway:eu-central-1:dynamodb:action/GetItem"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit", "aws_iam_role.dynamodb_read", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "get_insights_applications", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "9ul1od", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-kq9dui6bcf-9ul1od-GET", "integration_http_method": "GET", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {"integration.request.header.x-api-key": "method.request.header.x-api-key", "integration.request.path.id": "method.request.path.app-id", "integration.request.path.lang": "method.request.path.lang", "integration.request.path.variant": "method.request.path.variant"}, "request_templates": {}, "resource_id": "9ul1od", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 29000, "tls_config": [], "type": "HTTP", "uri": "https://kq9dui6bcf.execute-api.eu-central-1.amazonaws.com/dev/insights/A_{id}/{variant}/{lang}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "get_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "zyko4s", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-kq9dui6bcf-zyko4s-GET", "integration_http_method": "GET", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {"integration.request.header.x-api-key": "method.request.header.x-api-key", "integration.request.path.cand-id": "method.request.path.cand-id", "integration.request.path.job-id": "method.request.path.job-id", "integration.request.path.lang": "method.request.path.lang", "integration.request.path.variant": "method.request.path.variant"}, "request_templates": {}, "resource_id": "zyko4s", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 29000, "tls_config": [], "type": "HTTP", "uri": "https://kq9dui6bcf.execute-api.eu-central-1.amazonaws.com/dev/insights/J_{job-id}:C_{cand-id}/{variant}/{lang}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "post_await_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "4msrbz", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-kq9dui6bcf-4msrbz-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": {"integration.request.header.x-api-key": "method.request.header.x-api-key", "integration.request.path.lang": "method.request.path.lang", "integration.request.path.variant": "method.request.path.variant"}, "request_templates": {"application/json": "{\"id\" : \"J_$input.params('job-id'):C_$input.params('cand-id')\",\"candidate\" : $input.json('$.candidate'),\"job\" : {\"job_id\" : \"$input.params('job-id')\",\"source\" : \"datapool\"}}"}, "resource_id": "4msrbz", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 45000, "tls_config": [], "type": "HTTP", "uri": "https://kq9dui6bcf.execute-api.eu-central-1.amazonaws.com/dev/await/insights/{variant}/{lang}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "post_await_insights_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "58jwvj", "connection_id": "", "connection_type": "INTERNET", "content_handling": "CONVERT_TO_TEXT", "credentials": "arn:aws:iam::************:role/jobfit-api-dev-api-gateway-to-step-functions-role", "http_method": "POST", "id": "agi-kq9dui6bcf-58jwvj-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_TEMPLATES", "request_parameters": {}, "request_templates": {"application/json": "#if($input.path('$.id') != \"\")#set($id=$input.path('$.id'))#else#set($id=$context.requestId)#end#if($input.params('force') == \"\")#set($force=false)#else#set($force=$input.params('force'))#end{\"input\": \"{\\\"id\\\": \\\"$id\\\", \\\"job\\\": $util.escapeJavaScript($input.json('$.job')), \\\"candidate\\\": $util.escapeJavaScript($input.json('$.candidate')), \\\"variants\\\": [\\\"$util.escapeJavaScript($input.params('variant'))\\\"], \\\"languages\\\": [\\\"$util.escapeJavaScript($input.params('lang'))\\\"], \\\"force\\\": $force  }\",\"name\": \"$context.requestId\",\"stateMachineArn\": \"$util.escapeJavaScript($stageVariables.awaiterFuncArn)\"}"}, "resource_id": "58jwvj", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 45000, "tls_config": [], "type": "AWS", "uri": "arn:aws:apigateway:eu-central-1:states:action/StartSyncExecution"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_variant_lang", "aws_api_gateway_model.model", "aws_api_gateway_request_validator.body", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_resource.await_insights_variant_lang", "aws_api_gateway_rest_api.jobfit", "aws_iam_role.api_gateway_to_step_functions", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "zu1qh2", "connection_id": "", "connection_type": "INTERNET", "content_handling": "CONVERT_TO_TEXT", "credentials": "arn:aws:iam::************:role/jobfit-api-dev-api-gateway-to-step-functions-role", "http_method": "POST", "id": "agi-kq9dui6bcf-zu1qh2-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_TEMPLATES", "request_parameters": {}, "request_templates": {"application/json": "#if($input.path('$.id') != \"\")#set($id=$input.path('$.id'))#else#set($id=$context.requestId)#end#if($input.params('force') == \"\")#set($force=false)#else#set($force=$input.params('force'))#end{\"input\": \"{\\\"id\\\": \\\"$id\\\", \\\"job\\\": $util.escapeJavaScript($input.json('$.job')), \\\"candidate\\\": $util.escapeJavaScript($input.json('$.candidate')), \\\"variants\\\": [\\\"$util.escapeJavaScript($input.params('variant'))\\\"], \\\"languages\\\": [\\\"$util.escapeJavaScript($input.params('lang'))\\\"], \\\"force\\\": $force  }\",\"name\": \"$context.requestId\",\"stateMachineArn\": \"$util.escapeJavaScript($stageVariables.awaiterFuncArn)\"}"}, "resource_id": "zu1qh2", "rest_api_id": "kq9dui6bcf", "timeout_milliseconds": 45000, "tls_config": [], "type": "AWS", "uri": "arn:aws:apigateway:eu-central-1:states:action/StartSyncExecution"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.test", "aws_api_gateway_resource.test", "aws_api_gateway_rest_api.jobfit", "aws_iam_role.api_gateway_to_step_functions", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-400", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "400", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-403", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "403", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-500", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "500", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-404", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "404", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-200", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "200", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_applications_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-9ul1od-GET-422", "resource_id": "9ul1od", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "422", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_applications", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "CONVERT_TO_TEXT", "http_method": "GET", "id": "agir-kq9dui6bcf-ti1l7x-GET-400", "resource_id": "ti1l7x", "response_parameters": {}, "response_templates": {"application/json": "    {\n      \"error\": {\n        \"message\": $input.path(\"$.message\"),\n        \"type\": $input.path(\"$.__type\"),\n        \"code\": 400\n      }\n    }\n"}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "400", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "CONVERT_TO_TEXT", "http_method": "GET", "id": "agir-kq9dui6bcf-ti1l7x-GET-500", "resource_id": "ti1l7x", "response_parameters": {}, "response_templates": {"application/json": "    {\n      \"error\": {\n        \"message\": $input.path(\"$.message\"),\n        \"type\": $input.path(\"$.__type\"),\n        \"code\": 500\n      }\n    }\n"}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "500", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-400", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "400", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-403", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "403", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-500", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "500", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-404", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "404", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-200", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "200", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_jobs_candidates_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-kq9dui6bcf-zyko4s-GET-422", "resource_id": "zyko4s", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "422", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "get_insights_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "CONVERT_TO_TEXT", "http_method": "GET", "id": "agir-kq9dui6bcf-ti1l7x-GET-200", "resource_id": "ti1l7x", "response_parameters": {}, "response_templates": {"application/json": "#** Shows the insights object as <PERSON><PERSON><PERSON>, or an error if one has occurred.In case of an error, the status code will be modified accordingly.Arguments:- $insights (the insights object to show)*##** Terraform arguments:- max_depth (the maximum depth of recursion to allow)*##** Recursively shows a DynamoDB value (object, list or primitive) as JSON.Arguments:- $value (the value to show)- $exclude (a list of keys to exclude from the output)Supported types:- String (S)- Number (N)- Boolean (BOOL)- Map (M)- List (L)If some value's type is unsupported, it will be shown as null.Note: Due to Apache VTL's limit on #define recursions, this snippet isgenerated multiple times, once for each level of recursion.Also, because AWS API Gateway does not allow actual macros (functions),this needs to hack around shadowing by using a list as a stack,with the first (top) element being the actual value to renderat the current depth, removed after the sub-call is finished.*##define( $showDynamo )#set($values=[$value])$showValue0#end#define( $showValue0 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue1#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue1#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue1 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue2#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue2#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue2 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue3#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue3#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue3 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue4#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue4#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showInsights )#if($insights == \"\")#set($context.responseOverride.status = 404){\"error\":{\"message\":\"Insights do not exist!\",\"type\":\"NOT_FOUND\",\"code\":404}}#elseif($insights.error.M != \"\")#set($context.responseOverride.status = 422)#set($err=$insights.error.M){\"id\":\"$insights.id.S\",\"variant\":\"$insights.variant.S\",\"lang\":\"$insights.lang.S\",\"error\":{\"message\":\"An error has prevented insights from being generated!\",\"type\":\"$err.type.S\"}}#else#set($elab=$insights.elaboration.M){\"id\":\"$insights.id.S\",\"variant\":\"$insights.variant.S\",\"lang\":\"$insights.lang.S\",\"bundle\":#set($exclude=[])#set($value=$insights.bundle)$showDynamo ,\"elaboration\":#set($exclude=[\"metadata\"])#set($value=$insights.elaboration)$showDynamo}#end#end#set($insights=$input.path(\"$.Item\"))$showInsights"}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "200", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-400", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "400", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-403", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "403", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-500", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "500", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-404", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "404", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "CONVERT_TO_TEXT", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-200", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-kq9dui6bcf-4msrbz-POST-422", "resource_id": "4msrbz", "response_parameters": {}, "response_templates": {}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "422", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_integration_response", "name": "post_await_insights_variant_lang_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "CONVERT_TO_TEXT", "http_method": "POST", "id": "agir-kq9dui6bcf-58jwvj-POST-200", "resource_id": "58jwvj", "response_parameters": {}, "response_templates": {"application/json": "#** Shows the insights object as <PERSON><PERSON><PERSON>, or an error if one has occurred.In case of an error, the status code will be modified accordingly.Arguments:- $insights (the insights object to show)*##** Terraform arguments:- max_depth (the maximum depth of recursion to allow)*##** Recursively shows a DynamoDB value (object, list or primitive) as JSON.Arguments:- $value (the value to show)- $exclude (a list of keys to exclude from the output)Supported types:- String (S)- Number (N)- Boolean (BOOL)- Map (M)- List (L)If some value's type is unsupported, it will be shown as null.Note: Due to Apache VTL's limit on #define recursions, this snippet isgenerated multiple times, once for each level of recursion.Also, because AWS API Gateway does not allow actual macros (functions),this needs to hack around shadowing by using a list as a stack,with the first (top) element being the actual value to renderat the current depth, removed after the sub-call is finished.*##define( $showDynamo )#set($values=[$value])$showValue0#end#define( $showValue0 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue1#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue1#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue1 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue2#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue2#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue2 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue3#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue3#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showValue3 )#if($values.get(0).S != \"\")\"$values.get(0).S\"#elseif($values.get(0).N != \"\")$values.get(0).N#elseif($values.get(0).BOOL != \"\")$values.get(0).BOOL#elseif($values.get(0).M != \"\"){#foreach($key in $values.get(0).M.keySet())#if(!$exclude.contains($key))#set($dummy=$values.add(0, $values.get(0).M.get($key)))\"$key\":$showValue4#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end#end}#elseif($values.get(0).L != \"\")[#foreach($item in $values.get(0).L)#set($dummy=$values.add(0, $item))$showValue4#if($foreach.hasNext()),#end#set($dummy=$values.remove(0))#end]#else#[[null]]##end#end#define( $showInsights )#if($insights == \"\")#set($context.responseOverride.status = 404){\"error\":{\"message\":\"Insights do not exist!\",\"type\":\"NOT_FOUND\",\"code\":404}}#elseif($insights.error.M != \"\")#set($context.responseOverride.status = 422)#set($err=$insights.error.M){\"id\":\"$insights.id.S\",\"variant\":\"$insights.variant.S\",\"lang\":\"$insights.lang.S\",\"error\":{\"message\":\"An error has prevented insights from being generated!\",\"type\":\"$err.type.S\"}}#else#set($elab=$insights.elaboration.M){\"id\":\"$insights.id.S\",\"variant\":\"$insights.variant.S\",\"lang\":\"$insights.lang.S\",\"bundle\":#set($exclude=[])#set($value=$insights.bundle)$showDynamo ,\"elaboration\":#set($exclude=[\"metadata\"])#set($value=$insights.elaboration)$showDynamo}#end#end#set($insights=$util.parseJson($input.path(\"$.output\")))$showInsights"}, "rest_api_id": "kq9dui6bcf", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_method.post_await_insights_variant_lang", "aws_api_gateway_model.model", "aws_api_gateway_request_validator.body", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_resource.await_insights_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "get_insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": true, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-kq9dui6bcf-ti1l7x-GET", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.path.id": true, "method.request.path.lang": true, "method.request.path.variant": true}, "request_validator_id": "", "resource_id": "ti1l7x", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "get_insights_applications", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": true, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-kq9dui6bcf-9ul1od-GET", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.header.x-api-key": true, "method.request.path.app-id": true, "method.request.path.lang": true, "method.request.path.variant": true}, "request_validator_id": "", "resource_id": "9ul1od", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "get_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": true, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-kq9dui6bcf-zyko4s-GET", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.header.x-api-key": true, "method.request.path.cand-id": true, "method.request.path.job-id": true, "method.request.path.lang": true, "method.request.path.variant": true}, "request_validator_id": "", "resource_id": "zyko4s", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "post_await_insights_jobs_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": true, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-kq9dui6bcf-4msrbz-POST", "operation_name": "", "request_models": {"application/json": "InsightsRequestFixedJob"}, "request_parameters": {"method.request.header.x-api-key": true, "method.request.path.lang": true, "method.request.path.variant": true, "method.request.querystring.force": false}, "request_validator_id": "", "resource_id": "4msrbz", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "post_await_insights_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": true, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-kq9dui6bcf-58jwvj-POST", "operation_name": "", "request_models": {"application/json": "InsightsRequest"}, "request_parameters": {"method.request.path.lang": true, "method.request.path.variant": true, "method.request.querystring.force": false}, "request_validator_id": "ly5vwg", "resource_id": "58jwvj", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_model.model", "aws_api_gateway_request_validator.body", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_resource.await_insights_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-kq9dui6bcf-zu1qh2-POST", "operation_name": "", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "zu1qh2", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.test", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-400", "resource_id": "9ul1od", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_bad_request", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-403", "resource_id": "9ul1od", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_forbidden", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-500", "resource_id": "9ul1od", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_internal_server_error", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-404", "resource_id": "9ul1od", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_not_found", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-200", "resource_id": "9ul1od", "response_models": {"application/json": "InsightsResponse"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_ok", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_applications_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-9ul1od-GET-422", "resource_id": "9ul1od", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_applications_unprocessable_entity", "aws_api_gateway_method.get_insights_applications", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_resource.insights_applications_app_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-ti1l7x-GET-400", "resource_id": "ti1l7x", "response_models": {}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_bad_request", "aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-ti1l7x-GET-500", "resource_id": "ti1l7x", "response_models": {}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_internal_server_error", "aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-400", "resource_id": "zyko4s", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_bad_request", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-403", "resource_id": "zyko4s", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_forbidden", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-500", "resource_id": "zyko4s", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_internal_server_error", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-404", "resource_id": "zyko4s", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_not_found", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-200", "resource_id": "zyko4s", "response_models": {"application/json": "InsightsResponse"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_ok", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_jobs_candidates_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-zyko4s-GET-422", "resource_id": "zyko4s", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_jobs_candidates_unprocessable_entity", "aws_api_gateway_method.get_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "get_insights_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-kq9dui6bcf-ti1l7x-GET-200", "resource_id": "ti1l7x", "response_models": {}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.get_insights_ok", "aws_api_gateway_method.get_insights", "aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_resource.insights_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_bad_request", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-400", "resource_id": "4msrbz", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "400"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_bad_request", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_forbidden", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-403", "resource_id": "4msrbz", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "403"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_forbidden", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_internal_server_error", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-500", "resource_id": "4msrbz", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "500"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_internal_server_error", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_not_found", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-404", "resource_id": "4msrbz", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "404"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_not_found", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-200", "resource_id": "4msrbz", "response_models": {"application/json": "InsightsResponse"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_ok", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_jobs_j_id_candidates_c_id_variant_lang_unprocessable_entity", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-4msrbz-POST-422", "resource_id": "4msrbz", "response_models": {"application/json": "ProtocolError"}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "422"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_jobs_j_id_candidates_c_id_variant_lang_unprocessable_entity", "aws_api_gateway_method.post_await_insights_jobs_candidates", "aws_api_gateway_model.model", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_method_response", "name": "post_await_insights_variant_lang_ok", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-kq9dui6bcf-58jwvj-POST-200", "resource_id": "58jwvj", "response_models": {}, "response_parameters": {}, "rest_api_id": "kq9dui6bcf", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_integration_response.post_await_insights_variant_lang_ok", "aws_api_gateway_method.post_await_insights_variant_lang", "aws_api_gateway_model.model", "aws_api_gateway_request_validator.body", "aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_resource.await_insights_variant_lang", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_model", "name": "model", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ApplicationInsightsId", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "An internal insights identifier for a job application", "id": "9lb4iv", "name": "ApplicationInsightsId", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":\"A_559be270-4cc1-4080-95b4-678ad71b4513\",\"description\":\"An internal insights identifier for a job application\",\"pattern\":\"^A_[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$\",\"title\":\"ApplicationInsightsId\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "Candidate", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A description of a candidate", "id": "n079fk", "name": "Candidate", "rest_api_id": "kq9dui6bcf", "schema": "{\n    \"$schema\" : \"http://json-schema.org/draft-04/schema#\",\n    \"title\": \"Candidate\",\n    \"description\": \"A description of a candidate\",\n    \"type\": \"object\",\n    \"properties\": {\n        \"cv\": {\n            \"$ref\": \"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/FileSource\"\n        }\n    },\n    \"required\": [\n        \"cv\"\n    ]\n}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "CustomInsightsId", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "An internal insights identifier for custom requests", "id": "qyg0o5", "name": "CustomInsightsId", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":\"2ebc3726-e2b1-45a8-a955-1cd69f71b9b5\",\"description\":\"An internal insights identifier for custom requests\",\"format\":\"uuid\",\"title\":\"CustomInsightsId\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "DatapoolJob", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A job that can be fetched from the Datapool API", "id": "2ruagl", "name": "DatapoolJob", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A job that can be fetched from the Datapool API\",\"properties\":{\"job_id\":{\"description\":\"Identifier of a vacancy within Datapool\",\"format\":\"uuid\",\"type\":\"string\"},\"source\":{\"description\":\"Must be \\\"datapool\\\"\",\"enum\":[\"datapool\"],\"type\":\"string\"}},\"required\":[\"source\",\"job_id\"],\"title\":\"DatapoolJob\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "Elaboration", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A textual or structured elaboration aboutnnnnn the candidate's fit to the job. The specific structure depends on the variant of the insights.", "id": "m2m4to", "name": "Elaboration", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"anyOf\":[{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/ElaborationB2B1\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/ElaborationB2C1\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/ElaborationGeneric\"}],\"description\":\"A textual or structured elaboration aboutnnnnn the candidate's fit to the job. The specific structure depends on the variant of the insights.\",\"title\":\"Elaboration\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "ElaborationB2B1", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A b2b-1 variant of elaboration, intended for recruiters.", "id": "9seeiu", "name": "ElaborationB2B1", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":{\"career_alignment\":\"The candidate's educational background in computer science and experience in technology may not directly align with the financial qualifications required for the Treuhandexpert position, which typically necessitates specific diplomas and certifications in accounting or finance.\",\"experience\":\"The candidate's experience as a Head of Engineering and CTO aligns with the job's requirement for leadership and team coaching. However, the focus on software engineering may not directly translate to the specific needs of a Treuhandexpert role, which emphasizes financial and consulting expertise.\",\"hard_skills\":\"The candidate possesses extensive technical skills, including expertise in cloud architecture, software development, and DevOps practices. They are proficient in various programming languages such as Python, Scala, and JavaScript, and have experience with AWS, Docker, and CI/CD processes.\",\"language_skills\":\"The candidate is fluent in Polish and English, with a communicative level of German (B1-B2), which may facilitate interaction in a multilingual work environment.\",\"leadership\":\"The candidate has a substantial leadership background, having grown a product division from 1 to 17 people and established various organizational processes, indicating strong team management capabilities.\",\"learning\":\"The candidate shows a commitment to professional growth, having transitioned from software development to leadership roles and continuously seeking new challenges, such as relocating to Switzerland for new opportunities.\",\"seniority\":\"The candidate is at a senior career level, having held leadership positions and managed significant responsibilities within their previous roles.\",\"soft_skills\":\"The candidate demonstrates strong leadership abilities, having managed teams and established processes. They also exhibit communication skills and an analytical mindset, which are essential for collaboration and problem-solving.\",\"summary\":\"The candidate exhibits strong technical and leadership skills, making them well-suited for roles that require team management and technical expertise. However, there is a gap in relevant financial qualifications and experience specific to the Treuhandexpert position, which may limit their fit for this role.\"},\"description\":\"A b2b-1 variant of elaboration, intended for recruiters.\",\"properties\":{\"career_alignment\":{\"description\":\"How the candidate's career path aligns with the job requirements.\",\"type\":\"string\"},\"experience\":{\"description\":\"The candidate's professional experience and how it relates to the job.\",\"type\":\"string\"},\"hard_skills\":{\"description\":\"The candidate's technical skills relevant to the job.\",\"type\":\"string\"},\"language_skills\":{\"description\":\"The candidate's language skills and proficiency levels.\",\"type\":\"string\"},\"leadership\":{\"description\":\"The candidate's leadership experience and capabilities.\",\"type\":\"string\"},\"learning\":{\"description\":\"The candidate's willingness and ability to learn new skills or adapt to new environments.\",\"type\":\"string\"},\"seniority\":{\"description\":\"The candidate's career level and seniority in their field.\",\"type\":\"string\"},\"soft_skills\":{\"description\":\"The candidate's soft skills relevant to the job.\",\"type\":\"string\"},\"summary\":{\"description\":\"A brief summary of the candidate's fit for the job.\",\"type\":\"string\"}},\"required\":[\"summary\"],\"title\":\"ElaborationB2B1\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "ElaborationB2C1", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A b2c-1 variant of elaboration, intended for seekers.", "id": "q6a686", "name": "ElaborationB2C1", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":{\"requirement_fit\":[{\"content\":\"You are a certified trust expert with solid practical experience.\",\"crucial\":true,\"fulfilled\":\"fully\"},{\"content\":\"You are communicative, empathetic, and team-oriented.\",\"crucial\":false,\"explanation\":\"You are communicative, empathetic, and team-oriented. However, your CV doesn't mention any experience related to it.\",\"fulfilled\":\"partially\"},{\"content\":\"You think entrepreneurially and have a strong customer orientation.\",\"crucial\":false,\"fulfilled\":\"no_evidence\"}]},\"description\":\"A b2c-1 variant of elaboration, intended for seekers.\",\"properties\":{\"requirement_fit\":{\"description\":\"A list of important job requirements with associated information on how well the candidate fulfills them.\",\"items\":{\"properties\":{\"content\":{\"description\":\"A description of the requirement in the desired language.\",\"type\":\"string\"},\"crucial\":{\"description\":\"Indicates whether the requirement is crucial for the job (true) or just nice-to-have (false).\",\"type\":\"boolean\"},\"explanation\":{\"description\":\"In case of a partial fit, an explanation for the seeker on why the requirement is not fully met or how to improve. Will not be present if 'fulfilled' is 'fully' or 'no_evidence'.\",\"type\":\"string\"},\"fulfilled\":{\"description\":\"How well does the candidate meet the requirement? Can be 'fully', 'partially' (more or less, but something is off) or 'no_evidence' (no indication in the CV).\",\"enum\":[\"fully\",\"partially\",\"no_evidence\"],\"type\":\"string\"}},\"required\":[\"crucial\",\"fulfilled\",\"content\"],\"type\":\"object\"},\"type\":\"array\"}},\"title\":\"ElaborationB2C1\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "ElaborationGeneric", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "Any other elaboration structure not documented in the schema", "id": "kryhwn", "name": "ElaborationGeneric", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"additionalProperties\":true,\"default\":{\"whatever\":\"variant-dependent\"},\"description\":\"Any other elaboration structure not documented in the schema\",\"minProperties\":1,\"properties\":{},\"title\":\"ElaborationGeneric\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "FileSource", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A description on how to fetch some source file", "id": "8e5l6d", "name": "FileSource", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A description on how to fetch some source file\",\"oneOf\":[{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/MediaApiFileSource\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/RawFileSource\"}],\"title\":\"FileSource\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "InsightsError", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A processing error that has prevented insights from being generated", "id": "d9l354", "name": "InsightsError", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A processing error that has prevented insights from being generated\",\"properties\":{\"error\":{\"description\":\"The error that has occurred\",\"properties\":{\"code\":{\"description\":\"To be removed\",\"type\":\"integer\"},\"message\":{\"description\":\"A human-readable description of the error\",\"type\":\"string\"},\"type\":{\"description\":\"Type of an exception that has caused the error\",\"type\":\"string\"}},\"required\":[\"message\"],\"type\":\"object\"},\"id\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/InsightsId\",\"description\":\"To be removed\"}},\"required\":[\"error\"],\"title\":\"InsightsError\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "InsightsId", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "An internal insights identifier, just for reference", "id": "1oansa", "name": "InsightsId", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"An internal insights identifier, just for reference\",\"oneOf\":[{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/JobCandidateInsightsId\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/ApplicationInsightsId\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/CustomInsightsId\"}],\"title\":\"InsightsId\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "InsightsRequest", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A request to generate insights about some candidate's fit to some vacancy", "id": "9w5yyi", "name": "InsightsRequest", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A request to generate insights about some candidate's fit to some vacancy\",\"properties\":{\"candidate\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Candidate\"},\"id\":{\"anyOf\":[{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/JobCandidateInsightsId\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/CustomInsightsId\"}],\"default\":\"dbfa43b5-13a9-4e0e-bd61-52cea933515c\",\"description\":\"An internal identifier that should be used to identify the generated insights. Normally, you do not need to set this value, as it will be generated automatically, depending on the API endpoint you use.\",\"type\":\"string\"},\"job\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Job\"}},\"required\":[\"candidate\",\"job\"],\"title\":\"InsightsRequest\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "InsightsRequestFixedJob", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A request to generate insights about the given candidate's fit to some fixed job", "id": "kxluox", "name": "InsightsRequestFixedJob", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A request to generate insights about the given candidate's fit to some fixed job\",\"properties\":{\"candidate\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Candidate\"}},\"required\":[\"candidate\"],\"title\":\"InsightsRequestFixedJob\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "InsightsResponse", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "JobFit insights about a candidate's fit to a job", "id": "zx0vdd", "name": "InsightsResponse", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-07/schema#\",\"description\":\"JobFit insights about a candidate's fit to a job\",\"properties\":{\"bundle\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/SourceBundle\"},\"elaboration\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Elaboration\"},\"error\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/InsightsError\"},\"id\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/InsightsId\"},\"lang\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Language\"},\"variant\":{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/Variant\"}},\"required\":[\"id\",\"variant\",\"lang\"],\"title\":\"InsightsResponse\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "Job", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "Information about a vacancy", "id": "r8kumb", "name": "Job", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"Information about a vacancy\",\"oneOf\":[{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/MarketplaceJob\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/RawJob\"},{\"$ref\":\"https://apigateway.amazonaws.com/restapis/kq9dui6bcf/models/DatapoolJob\"}],\"title\":\"Job\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "JobCandidateInsightsId", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "An internal insights identifier for a job-candidate pair", "id": "t0h5xi", "name": "JobCandidateInsightsId", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":\"J_d7c4d2a3-ef64-41e4-a155-e186b6b53f18:C_fc1be1e7-4b58-4d15-8ae3-e3e65f6f91e2\",\"description\":\"An internal insights identifier for a job-candidate pair\",\"pattern\":\"^J_[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}:C_[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$\",\"title\":\"JobCandidateInsightsId\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "Language", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "Desired language of the insights", "id": "w7p6zn", "name": "Language", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":\"de\",\"description\":\"Desired language of the insights\",\"enum\":[\"de\",\"en\",\"fr\"],\"title\":\"Language\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "MarketplaceJob", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A vacancy that can be fetched from the Marketplace Core API", "id": "jh953d", "name": "MarketplaceJob", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A vacancy that can be fetched from the Marketplace Core API\",\"properties\":{\"source\":{\"description\":\"Must be \\\"marketplace\\\"\",\"enum\":[\"marketplace\"],\"type\":\"string\"},\"vacancy_id\":{\"description\":\"Identifier of a vacancy within Marketplace\",\"format\":\"uuid\",\"type\":\"string\"}},\"required\":[\"source\",\"vacancy_id\"],\"title\":\"MarketplaceJob\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "MediaApiFileSource", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A file stored in JobCloud's Media API, associated with a valid download token", "id": "5lcil9", "name": "MediaApiFileSource", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A file stored in JobCloud's Media API, associated with a valid download token\",\"properties\":{\"media_file_id\":{\"description\":\"Identifier of the file within Media API\",\"format\":\"uuid\",\"type\":\"string\"},\"source\":{\"description\":\"Must be \\\"media_api\\\"\",\"enum\":[\"media_api\"],\"type\":\"string\"},\"token\":{\"description\":\"A valid authentication token allowing for the file to be downloaded\",\"type\":\"string\"}},\"required\":[\"source\",\"media_file_id\",\"token\"],\"title\":\"MediaApiFileSource\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "ProtocolError", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A protocol-level (HTTP) error that has occurred while processing the request", "id": "tk0l5w", "name": "ProtocolError", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A protocol-level (HTTP) error that has occurred while processing the request\",\"properties\":{\"error\":{\"properties\":{\"code\":{\"description\":\"An associated HTTP status code\",\"type\":\"integer\"},\"message\":{\"description\":\"A human-readable description of the error\",\"type\":\"string\"},\"type\":{\"description\":\"An underlying HTTP error type\",\"type\":\"string\"}},\"type\":\"object\"}},\"title\":\"ProtocolError\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "RawFileSource", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A PDF file with explicitly provided data (Base64-encoded)", "id": "uhu5au", "name": "RawFileSource", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A PDF file with explicitly provided data (Base64-encoded)\",\"properties\":{\"pdf_base64\":{\"description\":\"Base64-encoded PDF bytes\",\"minLength\":1,\"pattern\":\"^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$\",\"type\":\"string\"},\"source\":{\"description\":\"Must be \\\"raw\\\"\",\"enum\":[\"raw\"],\"type\":\"string\"}},\"required\":[\"source\",\"pdf_base64\"],\"title\":\"RawFileSource\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "<PERSON><PERSON><PERSON>", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "A generic, directly provided job description", "id": "h4xaxt", "name": "<PERSON><PERSON><PERSON>", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"description\":\"A generic, directly provided job description\",\"properties\":{\"description\":{\"description\":\"Textual description of a job\",\"minLength\":3,\"type\":\"string\"},\"source\":{\"description\":\"Must be \\\"raw\\\"\",\"enum\":[\"raw\"],\"type\":\"string\"},\"title\":{\"description\":\"Job title\",\"minLength\":3,\"type\":\"string\"}},\"required\":[\"source\",\"description\"],\"title\":\"RawJob\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "SourceBundle", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "Information about the static media files used to generate the insights, if any", "id": "gjjh6e", "name": "SourceBundle", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-07/schema#\",\"default\":{\"media_api_candidate\":{\"cv\":{\"id\":\"71e20325-de51-42b1-a4dc-aa4ef8353dd9\"}}},\"description\":\"Information about the static media files used to generate the insights, if any\",\"properties\":{\"media_api_candidate\":{\"description\":\"Media files describing the candidate\",\"properties\":{\"cv\":{\"description\":\"The candidate's CV media file\",\"properties\":{\"id\":{\"description\":\"The ID of the CV media file\",\"format\":\"uuid\",\"type\":\"string\"}},\"required\":[\"id\"],\"type\":\"object\"}},\"type\":\"object\"}},\"title\":\"SourceBundle\",\"type\":\"object\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}, {"index_key": "<PERSON><PERSON><PERSON>", "schema_version": 0, "attributes": {"content_type": "application/json", "description": "Desired flavor of the insights to be generated", "id": "2aweg0", "name": "<PERSON><PERSON><PERSON>", "rest_api_id": "kq9dui6bcf", "schema": "{\"$schema\":\"http://json-schema.org/draft-04/schema#\",\"default\":\"b2b-1\",\"description\":\"Desired flavor of the insights to be generated\",\"maxLength\":16,\"minLength\":1,\"pattern\":\"^[a-z0-9\\\\-]+-[0-9]+$\",\"title\":\"Variant\",\"type\":\"string\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_request_validator", "name": "body", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "ly5vwg", "name": "Validate body", "rest_api_id": "kq9dui6bcf", "validate_request_body": true, "validate_request_parameters": false}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "j<PERSON><PERSON><PERSON>", "parent_id": "rf5fo4p78e", "path": "/await", "path_part": "await", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "6m9prh", "parent_id": "j<PERSON><PERSON><PERSON>", "path": "/await/insights", "path_part": "insights", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "emp3bf", "parent_id": "6m9prh", "path": "/await/insights/jobs", "path_part": "jobs", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs_j_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "l0hoqp", "parent_id": "emp3bf", "path": "/await/insights/jobs/{job-id}", "path_part": "{job-id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs_j_id_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "nl2798", "parent_id": "l0hoqp", "path": "/await/insights/jobs/{job-id}/candidates", "path_part": "candidates", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs_j_id_candidates_c_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "xtoxok", "parent_id": "nl2798", "path": "/await/insights/jobs/{job-id}/candidates/{cand-id}", "path_part": "{cand-id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs_j_id_candidates_c_id_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "z9i9gt", "parent_id": "xtoxok", "path": "/await/insights/jobs/{job-id}/candidates/{cand-id}/{variant}", "path_part": "{variant}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_jobs_j_id_candidates_c_id_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "4msrbz", "parent_id": "z9i9gt", "path": "/await/insights/jobs/{job-id}/candidates/{cand-id}/{variant}/{lang}", "path_part": "{lang}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_jobs", "aws_api_gateway_resource.await_insights_jobs_j_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id", "aws_api_gateway_resource.await_insights_jobs_j_id_candidates_c_id_variant", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "ceyuhm", "parent_id": "6m9prh", "path": "/await/insights/{variant}", "path_part": "{variant}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "await_insights_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "58jwvj", "parent_id": "ceyuhm", "path": "/await/insights/{variant}/{lang}", "path_part": "{lang}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_api_gateway_resource.await", "aws_api_gateway_resource.await_insights", "aws_api_gateway_resource.await_insights_variant", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "k8l1a8", "parent_id": "rf5fo4p78e", "path": "/insights", "path_part": "insights", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_applications", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "r8dcpl", "parent_id": "k8l1a8", "path": "/insights/applications", "path_part": "applications", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_applications_app_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "psy7ow", "parent_id": "r8dcpl", "path": "/insights/applications/{app-id}", "path_part": "{app-id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_applications_app_id_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dfp0xo", "parent_id": "psy7ow", "path": "/insights/applications/{app-id}/{variant}", "path_part": "{variant}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_applications_app_id_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "9ul1od", "parent_id": "dfp0xo", "path": "/insights/applications/{app-id}/{variant}/{lang}", "path_part": "{lang}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_applications", "aws_api_gateway_resource.insights_applications_app_id", "aws_api_gateway_resource.insights_applications_app_id_variant", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "ujb3dq", "parent_id": "k8l1a8", "path": "/insights/{id}", "path_part": "{id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_id_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "skmenx", "parent_id": "ujb3dq", "path": "/insights/{id}/{variant}", "path_part": "{variant}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_id_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "ti1l7x", "parent_id": "skmenx", "path": "/insights/{id}/{variant}/{lang}", "path_part": "{lang}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_id", "aws_api_gateway_resource.insights_id_variant", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "u98b66", "parent_id": "k8l1a8", "path": "/insights/jobs", "path_part": "jobs", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs_job_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "mx5z9e", "parent_id": "u98b66", "path": "/insights/jobs/{job-id}", "path_part": "{job-id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs_job_id_candidates", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "2pxkmm", "parent_id": "mx5z9e", "path": "/insights/jobs/{job-id}/candidates", "path_part": "candidates", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs_job_id_candidates_cand_id", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "aogm55", "parent_id": "2pxkmm", "path": "/insights/jobs/{job-id}/candidates/{cand-id}", "path_part": "{cand-id}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs_job_id_candidates_cand_id_variant", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "mngn2e", "parent_id": "aogm55", "path": "/insights/jobs/{job-id}/candidates/{cand-id}/{variant}", "path_part": "{variant}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "insights_jobs_job_id_candidates_cand_id_variant_lang", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "zyko4s", "parent_id": "mngn2e", "path": "/insights/jobs/{job-id}/candidates/{cand-id}/{variant}/{lang}", "path_part": "{lang}", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_resource.insights", "aws_api_gateway_resource.insights_jobs", "aws_api_gateway_resource.insights_jobs_job_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id", "aws_api_gateway_resource.insights_jobs_job_id_candidates_cand_id_variant", "aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_resource", "name": "test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "zu1qh2", "parent_id": "rf5fo4p78e", "path": "/test", "path_part": "test", "rest_api_id": "kq9dui6bcf"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_rest_api.jobfit"]}]}, {"mode": "managed", "type": "aws_api_gateway_rest_api", "name": "jobfit", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:eu-central-1::/restapis/kq9dui6bcf", "binary_media_types": [], "body": null, "created_date": "2024-12-17T09:45:27Z", "description": "", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"types": ["REGIONAL"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:eu-central-1:************:kq9dui6bcf", "fail_on_warnings": null, "id": "kq9dui6bcf", "minimum_compression_size": "", "name": "jobfit", "parameters": null, "policy": "", "put_rest_api_mode": "overwrite", "root_resource_id": "rf5fo4p78e", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_api_gateway_stage", "name": "jobfit", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_log_settings": [], "arn": "arn:aws:apigateway:eu-central-1::/restapis/kq9dui6bcf/stages/dev", "cache_cluster_enabled": false, "cache_cluster_size": "", "canary_settings": [], "client_certificate_id": "", "deployment_id": "obie58", "description": "", "documentation_version": "20250530072450", "execution_arn": "arn:aws:execute-api:eu-central-1:************:kq9dui6bcf/dev", "id": "ags-kq9dui6bcf-dev", "invoke_url": "https://kq9dui6bcf.execute-api.eu-central-1.amazonaws.com/dev", "rest_api_id": "kq9dui6bcf", "stage_name": "dev", "tags": {}, "tags_all": {}, "variables": {"awaiterFuncArn": "arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights", "insightsTable": "jobfit-insights-dev"}, "web_acl_arn": "arn:aws:wafv2:eu-central-1:************:regional/webacl/jobfit-api-dev-alb/0c6555d7-cc6e-4c25-8603-e4baf5b09536", "xray_tracing_enabled": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_usage_plan", "name": "demo", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_stages": [{"api_id": "kq9dui6bcf", "stage": "dev", "throttle": []}], "arn": "arn:aws:apigateway:eu-central-1::/usageplans/rs99bs", "description": "Usage plan for the Jobfit API for demo purposes", "id": "rs99bs", "name": "jobfit-demo", "product_code": "", "quota_settings": [{"limit": 1000, "offset": 0, "period": "DAY"}], "tags": {}, "tags_all": {}, "throttle_settings": [{"burst_limit": 15, "rate_limit": 3}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_usage_plan", "name": "seeker_browser", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_stages": [{"api_id": "kq9dui6bcf", "stage": "dev", "throttle": []}], "arn": "arn:aws:apigateway:eu-central-1::/usageplans/w1bg2p", "description": "Usage plan for the Jobfit API when used publicly from Seekers' browsers", "id": "w1bg2p", "name": "seeker-browser", "product_code": "", "quota_settings": [], "tags": {}, "tags_all": {}, "throttle_settings": [{"burst_limit": 100, "rate_limit": 10}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_api_gateway_usage_plan_key", "name": "api_key_association", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "jobfit-demo", "schema_version": 0, "attributes": {"id": "uw5uxw2uzj", "key_id": "uw5uxw2uzj", "key_type": "API_KEY", "name": "jobfit-demo", "usage_plan_id": "rs99bs", "value": "Sn6vofs6rH6lsUjf202ZXarWyFvwB0Fp2kZhKn8U"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_api_key.api_key", "aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}, {"index_key": "jobfit-seekers-profile", "schema_version": 0, "attributes": {"id": "5nvfwcjbw5", "key_id": "5nvfwcjbw5", "key_type": "API_KEY", "name": "jobfit-seekers-profile", "usage_plan_id": "w1bg2p", "value": "dIFWiOxt77aHIGybwVpuj7oJy0wWCptc7nPo4y8c"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_api_gateway_api_key.api_key", "aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_cloudfront_cache_policy", "name": "short_lived", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"comment": "Caching policy intended for frequently changing static assets, cached for up to 5 minutes", "default_ttl": 60, "etag": "E23ZP02F085DFQ", "id": "487ac732-454b-4e70-8897-e9f88103da95", "max_ttl": 300, "min_ttl": 1, "name": "ShortLivedCache", "parameters_in_cache_key_and_forwarded_to_origin": [{"cookies_config": [{"cookie_behavior": "none", "cookies": []}], "enable_accept_encoding_brotli": true, "enable_accept_encoding_gzip": true, "headers_config": [{"header_behavior": "none", "headers": []}], "query_strings_config": [{"query_string_behavior": "none", "query_strings": []}]}]}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ=="}]}, {"mode": "managed", "type": "aws_cloudfront_distribution", "name": "jobfit_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"aliases": ["api.dev.jobfit.jobcloud.services"], "arn": "arn:aws:cloudfront::************:distribution/E37DIOU8BMJX23", "caller_reference": "f5574e46-daae-423a-ba0f-79c84c79ad47", "comment": "JobFit public API (dev)", "continuous_deployment_policy_id": "", "custom_error_response": [], "default_cache_behavior": [{"allowed_methods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "cache_policy_id": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "cached_methods": ["GET", "HEAD", "OPTIONS"], "compress": false, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [], "function_association": [{"event_type": "viewer-request", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-preflight-handler"}, {"event_type": "viewer-response", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-cors-enricher"}], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "b689b0a8-53d0-40ab-baf2-68738e2966ac", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "jobfit-apigw", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "https-only"}], "default_root_object": "", "domain_name": "d38b54n0pmre0l.cloudfront.net", "enabled": true, "etag": "E28J6R9WU27TGX", "hosted_zone_id": "Z2FDTNDATAQYW2", "http_version": "http2and3", "id": "E37DIOU8BMJX23", "in_progress_validation_batches": 0, "is_ipv6_enabled": true, "last_modified_time": "2025-05-14 09:47:38.025 +0000 UTC", "logging_config": [], "ordered_cache_behavior": [{"allowed_methods": ["GET", "HEAD"], "cache_policy_id": "487ac732-454b-4e70-8897-e9f88103da95", "cached_methods": ["GET", "HEAD"], "compress": true, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [], "function_association": [], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "", "path_pattern": "/demo*", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "jobfit-assets-s3", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}, {"allowed_methods": ["GET", "HEAD"], "cache_policy_id": "487ac732-454b-4e70-8897-e9f88103da95", "cached_methods": ["GET", "HEAD"], "compress": true, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [], "function_association": [], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "", "path_pattern": "/docs*", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "jobfit-assets-s3", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "redirect-to-https"}, {"allowed_methods": ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"], "cache_policy_id": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "cached_methods": ["GET", "HEAD", "OPTIONS"], "compress": false, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [], "function_association": [{"event_type": "viewer-request", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-jwt-validator"}, {"event_type": "viewer-response", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-cors-enricher"}], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "b689b0a8-53d0-40ab-baf2-68738e2966ac", "path_pattern": "/await/insights/jobs/????????-????-????-????-????????????/?????/??", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "jobfit-apigw", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "https-only"}, {"allowed_methods": ["GET", "HEAD"], "cache_policy_id": "4135ea2d-6df8-44a3-9df3-4b5a84be39ad", "cached_methods": ["GET", "HEAD"], "compress": false, "default_ttl": 0, "field_level_encryption_id": "", "forwarded_values": [], "function_association": [{"event_type": "viewer-request", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-jwt-validator"}, {"event_type": "viewer-response", "function_arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-cors-enricher"}], "lambda_function_association": [], "max_ttl": 0, "min_ttl": 0, "origin_request_policy_id": "b689b0a8-53d0-40ab-baf2-68738e2966ac", "path_pattern": "/insights/jobs/????????-????-????-????-????????????/?????/??", "realtime_log_config_arn": "", "response_headers_policy_id": "", "smooth_streaming": false, "target_origin_id": "jobfit-apigw", "trusted_key_groups": [], "trusted_signers": [], "viewer_protocol_policy": "https-only"}], "origin": [{"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [{"http_port": 80, "https_port": 443, "origin_keepalive_timeout": 5, "origin_protocol_policy": "http-only", "origin_read_timeout": 30, "origin_ssl_protocols": ["SSLv3", "TLSv1", "TLSv1.1", "TLSv1.2"]}], "domain_name": "jobfit-api-assets-dev.s3-website.eu-central-1.amazonaws.com", "origin_access_control_id": "", "origin_id": "jobfit-assets-s3", "origin_path": "", "origin_shield": [], "s3_origin_config": []}, {"connection_attempts": 3, "connection_timeout": 10, "custom_header": [], "custom_origin_config": [{"http_port": 80, "https_port": 443, "origin_keepalive_timeout": 5, "origin_protocol_policy": "https-only", "origin_read_timeout": 30, "origin_ssl_protocols": ["TLSv1.2"]}], "domain_name": "kq9dui6bcf.execute-api.eu-central-1.amazonaws.com", "origin_access_control_id": "", "origin_id": "jobfit-apigw", "origin_path": "/dev", "origin_shield": [], "s3_origin_config": []}], "origin_group": [], "price_class": "PriceClass_100", "restrictions": [{"geo_restriction": [{"locations": ["AT", "BE", "BG", "CH", "CY", "CZ", "DE", "DK", "EE", "ES", "FI", "FR", "GR", "HR", "HU", "IE", "IT", "LI", "LT", "LU", "LV", "MT", "NL", "PL", "PT", "RO", "RS", "SE", "SI", "SK"], "restriction_type": "whitelist"}]}], "retain_on_delete": false, "staging": false, "status": "Deployed", "tags": {}, "tags_all": {}, "trusted_key_groups": [{"enabled": false, "items": []}], "trusted_signers": [{"enabled": false, "items": []}], "viewer_certificate": [{"acm_certificate_arn": "arn:aws:acm:us-east-1:************:certificate/8c96b744-3dba-4460-8ea8-33abaf5c8c94", "cloudfront_default_certificate": false, "iam_certificate_id": "", "minimum_protocol_version": "TLSv1.2_2021", "ssl_support_method": "sni-only"}], "wait_for_deployment": true, "web_acl_id": ""}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_api_gateway_rest_api.jobfit", "aws_cloudfront_cache_policy.short_lived", "aws_cloudfront_function.cors_enricher", "aws_cloudfront_function.jwt_validator", "aws_cloudfront_function.preflight_handler", "aws_cloudfront_key_value_store.secrets", "aws_route53_zone.jobfit", "aws_s3_bucket.jobfit_api_assets", "aws_s3_bucket_website_configuration.jobfit_api_assets", "data.aws_cloudfront_cache_policy.disabled", "data.aws_cloudfront_origin_request_policy.all_except_host", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_cloudfront_function", "name": "cors_enricher", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-cors-enricher", "code": "// Extracts and normalizes the request's origin.\n// If not provided or provided as 'null', returns 'null' uniformly (e.g. when triggered from local files).\nfunction normalizedOrigin(request) {\n    return request.headers.origin? request.headers.origin.value : 'null';\n}\n\n// Checks whether the given request origin is allowed for CORS.\nfunction originAllowed(origin) {\n    return [\"jobs.ch\",\"jobup.ch\",\"jobcloud.services\",\"null\"].some(orig => origin.endsWith(orig));\n}\n\n// Extends the given response with CORS headers.\nfunction corsEnrichResponse(response, origin) {\n    if (!response.headers) {\n        response.headers = {};\n    }\n\n    Object.assign(response.headers, {\n        'access-control-allow-origin': { value: origin },\n        'access-control-allow-methods': { value: [\"GET\",\"POST\",\"OPTIONS\"].join(', ') },\n        'access-control-allow-credentials': { value: 'true' },\n        'access-control-allow-headers': { value: [\"x-api-key\",\"content-type\"].join(', ') },\n        'access-control-max-age': { value: '30' }\n    });\n}\n\n// Handles a preflight OPTIONS request, if applicable.\nfunction handlePreflightOptions(event) {\n    if (event.request.method === 'OPTIONS') {\n        const origin = normalizedOrigin(event.request);\n        \n        if (originAllowed(origin)) {\n            const response = {\n                statusCode: 200,\n                statusDescription: 'OK'\n            };\n\n            corsEnrichResponse(response, origin);\n            return response;\n        }\n    \n        return {\n            statusCode: 403,\n            statusDescription: 'Forbidden'\n        };\n    }\n}\n\n\nfunction handler(event) {\n    const origin = normalizedOrigin(event.request);\n    \n    if (originAllowed(origin)) {\n        corsEnrichResponse(event.response, origin);\n    }\n    \n    return event.response;\n}\n", "comment": "", "etag": "E1VC38T7YXB528", "id": "jobfit-api-dev-cors-enricher", "key_value_store_associations": [], "live_stage_etag": "E1F83G8C2ARO7P", "name": "jobfit-api-dev-cors-enricher", "publish": true, "runtime": "cloudfront-js-2.0", "status": "DEPLOYED"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudfront_function", "name": "jwt_validator", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-jwt-validator", "code": "import crypto from 'crypto';\nimport cf from 'cloudfront';\n\nconst JWT_ALGO = 'HS256';\nconst HASH_ALGO = 'sha256';\n\nconst response401 = {\n    statusCode: 401,\n    statusDescription: 'Unauthorized'\n};\n\nconst loggingEnabled = true;\n\n// Extracts and normalizes the request's origin.\n// If not provided or provided as 'null', returns 'null' uniformly (e.g. when triggered from local files).\nfunction normalizedOrigin(request) {\n    return request.headers.origin? request.headers.origin.value : 'null';\n}\n\n// Checks whether the given request origin is allowed for CORS.\nfunction originAllowed(origin) {\n    return [\"jobs.ch\",\"jobup.ch\",\"jobcloud.services\",\"null\"].some(orig => origin.endsWith(orig));\n}\n\n// Extends the given response with CORS headers.\nfunction corsEnrichResponse(response, origin) {\n    if (!response.headers) {\n        response.headers = {};\n    }\n\n    Object.assign(response.headers, {\n        'access-control-allow-origin': { value: origin },\n        'access-control-allow-methods': { value: [\"GET\",\"POST\",\"OPTIONS\"].join(', ') },\n        'access-control-allow-credentials': { value: 'true' },\n        'access-control-allow-headers': { value: [\"x-api-key\",\"content-type\"].join(', ') },\n        'access-control-max-age': { value: '30' }\n    });\n}\n\n// Handles a preflight OPTIONS request, if applicable.\nfunction handlePreflightOptions(event) {\n    if (event.request.method === 'OPTIONS') {\n        const origin = normalizedOrigin(event.request);\n        \n        if (originAllowed(origin)) {\n            const response = {\n                statusCode: 200,\n                statusDescription: 'OK'\n            };\n\n            corsEnrichResponse(response, origin);\n            return response;\n        }\n    \n        return {\n            statusCode: 403,\n            statusDescription: 'Forbidden'\n        };\n    }\n}\n\n\nasync function handler(event) {\n    const preflightResponse = handlePreflightOptions(event);\n    if (preflightResponse) {\n        return preflightResponse;\n    }\n\n    try {\n        let request = event.request;\n\n        if (!request.headers['authorization']) {\n            throw new Error('No JWT in the Authorization');\n        }\n\n        const jwtToken = request.headers['authorization'].value;\n        const token = jwtToken.split('Bearer ')[1]\n        const tokenDecoded = await jwtDecode(token);\n        const tokenPayload = tokenDecoded.payload;\n        const secret = tokenDecoded.secret;\n\n        if (tokenPayload.exp && Date.now() > tokenPayload.exp * 1000) {\n            throw new Error('Token expired');\n        }\n\n        const candidateId = tokenPayload['candidate_id'];\n        if (!candidateId) {\n            throw new Error(`Missing candidate_id in JWT`);\n        }\n\n        const pattern = /^(\\/(?:await\\/)?insights\\/jobs\\/)([^\\/]+)\\/([^\\/]+)\\/([^\\/]+)$/\n        if (!pattern.test(request.uri)) {\n            throw new Error('Invalid request path');\n        }\n\n        request.uri = request.uri.replace(pattern, '$1$2/candidates/' + candidateId + '/$3/$4');\n        request.headers['x-api-key'] = {value: secret};\n        return request;\n    } catch (e) {\n        log(`Exception: ${e}`);\n        return response401;\n    }\n}\n\nasync function jwtDecode(token) {\n    const splitToken = token.split('.');\n    const header = splitToken[0];\n    const payload = splitToken[1];\n    const signature = splitToken[2];\n    if (!header || !payload || !signature) {\n        throw new Error('Malformed JWT, must have 3 parts');\n    }\n\n    verifyHeader(header);\n\n    const payloadObj = b64ToJson(payload);\n    if (!payloadObj.iss) {\n        throw new Error('Missing issuer in JWT');\n    }\n    const secret = await getSecret(payloadObj.iss);\n    if (!secret) {\n        throw new Error('Unrecognized issuer: ' + payloadObj.iss);\n    }\n\n    verifySignature(secret, header, payload, signature);\n\n    return {payload: payloadObj, secret: secret};\n}\n\nfunction b64ToJson(blob) {\n    return JSON.parse(Buffer.from(blob, 'base64').toString('utf8'));\n}\n\nfunction verifyHeader(header) {\n    const headerObj = b64ToJson(header);\n    if (headerObj.typ !== 'JWT') {\n        throw new Error(`Not a JWT: ${headerObj.typ}`);\n    } else if (headerObj.alg !== JWT_ALGO) {\n        throw new Error(`Unsupported algorithm: ${headerObj.alg}`);\n    }\n}\n\nfunction verifySignature(secret, header, payload, signature) {\n    const expectedSignature = crypto.createHmac(HASH_ALGO, secret)\n        .update(`${header}.${payload}`)\n        .digest('base64url');\n\n    if (signature !== expectedSignature) {\n        throw new Error('Invalid signature');\n    }\n}\n\nasync function getSecret(key) {\n    try {\n        const kvsHandle = cf.kvs();\n        return await kvsHandle.get(key);\n    } catch (err) {\n        throw new Error(`Error reading value for key: ${key}, error: ${err}`);\n    }\n}\n\nfunction log(message) {\n    if (loggingEnabled) {\n        console.log(message);\n    }\n}\n", "comment": "", "etag": "E3D1N3J5SXSYPF", "id": "jobfit-api-dev-jwt-validator", "key_value_store_associations": ["arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814"], "live_stage_etag": "EZDCMCLKM91C3", "name": "jobfit-api-dev-jwt-validator", "publish": true, "runtime": "cloudfront-js-2.0", "status": "DEPLOYED"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudfront_key_value_store.secrets"]}]}, {"mode": "managed", "type": "aws_cloudfront_function", "name": "preflight_handler", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:function/jobfit-api-dev-preflight-handler", "code": "// Extracts and normalizes the request's origin.\n// If not provided or provided as 'null', returns 'null' uniformly (e.g. when triggered from local files).\nfunction normalizedOrigin(request) {\n    return request.headers.origin? request.headers.origin.value : 'null';\n}\n\n// Checks whether the given request origin is allowed for CORS.\nfunction originAllowed(origin) {\n    return [\"jobs.ch\",\"jobup.ch\",\"jobcloud.services\",\"null\"].some(orig => origin.endsWith(orig));\n}\n\n// Extends the given response with CORS headers.\nfunction corsEnrichResponse(response, origin) {\n    if (!response.headers) {\n        response.headers = {};\n    }\n\n    Object.assign(response.headers, {\n        'access-control-allow-origin': { value: origin },\n        'access-control-allow-methods': { value: [\"GET\",\"POST\",\"OPTIONS\"].join(', ') },\n        'access-control-allow-credentials': { value: 'true' },\n        'access-control-allow-headers': { value: [\"x-api-key\",\"content-type\"].join(', ') },\n        'access-control-max-age': { value: '30' }\n    });\n}\n\n// Handles a preflight OPTIONS request, if applicable.\nfunction handlePreflightOptions(event) {\n    if (event.request.method === 'OPTIONS') {\n        const origin = normalizedOrigin(event.request);\n        \n        if (originAllowed(origin)) {\n            const response = {\n                statusCode: 200,\n                statusDescription: 'OK'\n            };\n\n            corsEnrichResponse(response, origin);\n            return response;\n        }\n    \n        return {\n            statusCode: 403,\n            statusDescription: 'Forbidden'\n        };\n    }\n}\n\n\nfunction handler(event) {\n    const preflightResponse = handlePreflightOptions(event);\n    if (preflightResponse) {\n        return preflightResponse;\n    }\n\n    return event.request;\n}\n", "comment": "", "etag": "E3R76HOPU0Z2CB", "id": "jobfit-api-dev-preflight-handler", "key_value_store_associations": [], "live_stage_etag": "E1VC38T7YXB528", "name": "jobfit-api-dev-preflight-handler", "publish": true, "runtime": "cloudfront-js-2.0", "status": "DEPLOYED"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_cloudfront_key_value_store", "name": "secrets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814", "comment": "Secrets for CloudFront validator function", "etag": "E3UN6WX5RRO2AG", "id": "jobfit-api-dev-await-insights-jwt-validator-secrets", "last_modified_time": "2025-04-17T10:24:10Z", "name": "jobfit-api-dev-await-insights-jwt-validator-secrets", "timeouts": null}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_cloudwatch_log_group", "name": "awaiter", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:eu-central-1:************:log-group:/aws/states/jobfit-await-insights", "id": "/aws/states/jobfit-await-insights", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/states/jobfit-await-insights", "name_prefix": "", "retention_in_days": 90, "skip_destroy": false, "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "api_gateway_to_step_functions", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/jobfit-api-dev-api-gateway-to-step-functions-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-03-28T12:39:47Z", "description": "", "force_detach_policies": false, "id": "jobfit-api-dev-api-gateway-to-step-functions-role", "inline_policy": [{"name": "jobfit-api-dev-apigateway-to-step-functions-policy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"states:StartSyncExecution\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "jobfit-api-dev-api-gateway-to-step-functions-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV5U4NE2IRLJDMVZJF"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "awaiter", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/jobfit-api-dev-await-insights", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"states.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-03-31T08:58:07Z", "description": "", "force_detach_policies": false, "id": "jobfit-api-dev-await-insights", "inline_policy": [{"name": "jobfit-api-dev-await-insights-dynamodb", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:DeleteItem\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev\"}]}"}, {"name": "jobfit-api-dev-await-insights-logs", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogDelivery\",\"logs:CreateLogStream\",\"logs:GetLogDelivery\",\"logs:UpdateLogDelivery\",\"logs:DeleteLogDelivery\",\"logs:ListLogDeliveries\",\"logs:PutLogEvents\",\"logs:PutResourcePolicy\",\"logs:DescribeResourcePolicies\",\"logs:DescribeLogGroups\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}, {"name": "jobfit-api-dev-await-insights-sqs", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"sqs:SendMessage\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:eu-central-1:************:jobfit-extraction-dev-lambda\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "jobfit-api-dev-await-insights", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV5U4NE2I2PCAPPU6N"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "dynamodb_read", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/jobfit-api-dev-apigw-dynamodb-read", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-04-03T15:30:43Z", "description": "", "force_detach_policies": false, "id": "jobfit-api-dev-apigw-dynamodb-read", "inline_policy": [{"name": "jobfit-api-dev-apigw-dynamodb-read", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:GetItem\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev\",\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev/*\"]}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "jobfit-api-dev-apigw-dynamodb-read", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV5U4NE2I5BJLSAQXJ"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "api_gateway_to_step_functions", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jobfit-api-dev-api-gateway-to-step-functions-role:jobfit-api-dev-apigateway-to-step-functions-policy", "name": "jobfit-api-dev-apigateway-to-step-functions-policy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"states:StartSyncExecution\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights\"}]}", "role": "jobfit-api-dev-api-gateway-to-step-functions-role"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_cloudwatch_log_group.awaiter", "aws_iam_role.api_gateway_to_step_functions", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "awaiter_dynamodb", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jobfit-api-dev-await-insights:jobfit-api-dev-await-insights-dynamodb", "name": "jobfit-api-dev-await-insights-dynamodb", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:DeleteItem\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev\"}]}", "role": "jobfit-api-dev-await-insights"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.awaiter", "data.aws_dynamodb_table.insights"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "awaiter_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jobfit-api-dev-await-insights:jobfit-api-dev-await-insights-logs", "name": "jobfit-api-dev-await-insights-logs", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogDelivery\",\"logs:CreateLogStream\",\"logs:GetLogDelivery\",\"logs:UpdateLogDelivery\",\"logs:DeleteLogDelivery\",\"logs:ListLogDeliveries\",\"logs:PutLogEvents\",\"logs:PutResourcePolicy\",\"logs:DescribeResourcePolicies\",\"logs:DescribeLogGroups\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "jobfit-api-dev-await-insights"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.awaiter"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "awaiter_sqs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jobfit-api-dev-await-insights:jobfit-api-dev-await-insights-sqs", "name": "jobfit-api-dev-await-insights-sqs", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":\"sqs:SendMessage\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:sqs:eu-central-1:************:jobfit-extraction-dev-lambda\"}]}", "role": "jobfit-api-dev-await-insights"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.awaiter", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_iam_role_policy", "name": "dynamodb_read", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "jobfit-api-dev-apigw-dynamodb-read:jobfit-api-dev-apigw-dynamodb-read", "name": "jobfit-api-dev-apigw-dynamodb-read", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"dynamodb:GetItem\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev\",\"arn:aws:dynamodb:eu-central-1:************:table/jobfit-insights-dev/*\"]}]}", "role": "jobfit-api-dev-apigw-dynamodb-read"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.dynamodb_read", "data.aws_dynamodb_table.insights"]}]}, {"mode": "managed", "type": "aws_route53_record", "name": "jobfit_api_alias", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "A", "schema_version": 2, "attributes": {"alias": [{"evaluate_target_health": false, "name": "d38b54n0pmre0l.cloudfront.net", "zone_id": "Z2FDTNDATAQYW2"}], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "api.dev.jobfit.jobcloud.services", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z0184337WEGUS144X3NO_api.dev.jobfit.jobcloud.services_A", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "api.dev.jobfit.jobcloud.services", "records": [], "set_identifier": "", "ttl": 0, "type": "A", "weighted_routing_policy": [], "zone_id": "Z0184337WEGUS144X3NO"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_api_gateway_rest_api.jobfit", "aws_cloudfront_cache_policy.short_lived", "aws_cloudfront_distribution.jobfit_api", "aws_cloudfront_function.cors_enricher", "aws_cloudfront_function.jwt_validator", "aws_cloudfront_function.preflight_handler", "aws_cloudfront_key_value_store.secrets", "aws_route53_zone.jobfit", "aws_s3_bucket.jobfit_api_assets", "aws_s3_bucket_website_configuration.jobfit_api_assets", "data.aws_cloudfront_cache_policy.disabled", "data.aws_cloudfront_origin_request_policy.all_except_host", "data.aws_region.current"]}, {"index_key": "AAAA", "schema_version": 2, "attributes": {"alias": [{"evaluate_target_health": false, "name": "d38b54n0pmre0l.cloudfront.net", "zone_id": "Z2FDTNDATAQYW2"}], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "api.dev.jobfit.jobcloud.services", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z0184337WEGUS144X3NO_api.dev.jobfit.jobcloud.services_AAAA", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "api.dev.jobfit.jobcloud.services", "records": [], "set_identifier": "", "ttl": 0, "type": "AAAA", "weighted_routing_policy": [], "zone_id": "Z0184337WEGUS144X3NO"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_api_gateway_rest_api.jobfit", "aws_cloudfront_cache_policy.short_lived", "aws_cloudfront_distribution.jobfit_api", "aws_cloudfront_function.cors_enricher", "aws_cloudfront_function.jwt_validator", "aws_cloudfront_function.preflight_handler", "aws_cloudfront_key_value_store.secrets", "aws_route53_zone.jobfit", "aws_s3_bucket.jobfit_api_assets", "aws_s3_bucket_website_configuration.jobfit_api_assets", "data.aws_cloudfront_cache_policy.disabled", "data.aws_cloudfront_origin_request_policy.all_except_host", "data.aws_region.current"]}]}, {"mode": "managed", "type": "aws_route53_record", "name": "jobfit_api_cert_validation", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "*.dev.jobfit.jobcloud.services", "schema_version": 2, "attributes": {"alias": [], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z0184337WEGUS144X3NO__8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services_CNAME", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services", "records": ["_81c665827721a392ff24ec88bc82eaa3.xlfgrmvvlj.acm-validations.aws."], "set_identifier": "", "ttl": 60, "type": "CNAME", "weighted_routing_policy": [], "zone_id": "Z0184337WEGUS144X3NO"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_route53_zone.jobfit"]}, {"index_key": "dev.jobfit.jobcloud.services", "schema_version": 2, "attributes": {"alias": [], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z0184337WEGUS144X3NO__8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services_CNAME", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "_8b17f40ec37a7217e640060b2cea46db.dev.jobfit.jobcloud.services", "records": ["_81c665827721a392ff24ec88bc82eaa3.xlfgrmvvlj.acm-validations.aws."], "set_identifier": "", "ttl": 60, "type": "CNAME", "weighted_routing_policy": [], "zone_id": "Z0184337WEGUS144X3NO"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["aws_acm_certificate.jobfit_api_cert", "aws_route53_zone.jobfit"]}]}, {"mode": "managed", "type": "aws_route53_zone", "name": "jobfit", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:route53:::hostedzone/Z0184337WEGUS144X3NO", "comment": "Managed by Terraform", "delegation_set_id": "", "force_destroy": false, "id": "Z0184337WEGUS144X3NO", "name": "dev.jobfit.jobcloud.services", "name_servers": ["ns-1465.awsdns-55.org", "ns-1579.awsdns-05.co.uk", "ns-405.awsdns-50.com", "ns-772.awsdns-32.net"], "primary_name_server": "ns-1579.awsdns-05.co.uk", "tags": {}, "tags_all": {}, "vpc": [], "zone_id": "Z0184337WEGUS144X3NO"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "jobfit_api_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::jobfit-api-assets-dev", "bucket": "jobfit-api-assets-dev", "bucket_domain_name": "jobfit-api-assets-dev.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "jobfit-api-assets-dev.s3.eu-central-1.amazonaws.com", "cors_rule": [], "force_destroy": null, "grant": [{"id": "51944ffca5389ac0ce8d98b0716e1f2d57481c5cb467b5953a64ee8682869266", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z21DNDUVLTQW6Q", "id": "jobfit-api-assets-dev", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::jobfit-api-assets-dev/*\",\"Sid\":\"PublicReadAccess\"}],\"Version\":\"2012-10-17\"}", "region": "eu-central-1", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": true}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [{"error_document": "", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website.eu-central-1.amazonaws.com", "website_endpoint": "jobfit-api-assets-dev.s3-website.eu-central-1.amazonaws.com"}, "sensitive_attributes": [], "private": "************************************************************************************************************************************************************************************************************"}]}, {"mode": "managed", "type": "aws_s3_bucket_policy", "name": "jobfit_api_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "jobfit-api-assets-dev", "id": "jobfit-api-assets-dev", "policy": "{\"Statement\":[{\"Action\":\"s3:GetObject\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::jobfit-api-assets-dev/*\",\"Sid\":\"PublicReadAccess\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_s3_bucket.jobfit_api_assets"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "jobfit_api_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "jobfit-api-assets-dev", "id": "jobfit-api-assets-dev", "ignore_public_acls": true, "restrict_public_buckets": false}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjAifQ==", "dependencies": ["aws_s3_bucket.jobfit_api_assets"]}]}, {"mode": "managed", "type": "aws_s3_bucket_website_configuration", "name": "jobfit_api_assets", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "jobfit-api-assets-dev", "error_document": [], "expected_bucket_owner": "", "id": "jobfit-api-assets-dev", "index_document": [{"suffix": "index.html"}], "redirect_all_requests_to": [], "routing_rule": [], "routing_rules": "", "website_domain": "s3-website.eu-central-1.amazonaws.com", "website_endpoint": "jobfit-api-assets-dev.s3-website.eu-central-1.amazonaws.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.jobfit_api_assets"]}]}, {"mode": "managed", "type": "aws_sfn_state_machine", "name": "awaiter", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights", "creation_date": "2025-03-31T08:58:17Z", "definition": "{\n  \"StartAt\": \"Prepare variables\",\n  \"States\": {\n    \"Prepare variables\": {\n      \"Type\": \"Pass\",\n      \"Next\": \"Regeneration triggered?\",\n      \"Assign\": {\n        \"request\": \"{% $states.context.Execution.Input %}\",\n        \"insightsId\": \"{% $states.context.Execution.Input.id %}\",\n        \"variant\": \"{% ($variants := $states.context.Execution.Input.variants; $count($variants) = 1? $variants[0] : $error('Exactly one variant must be specified!')) %}\",\n        \"lang\": \"{% ($langs := $states.context.Execution.Input.languages; $count($langs) = 1? $langs[0] : $error('Exactly one language must be specified!')) %}\",\n        \"generationTriggered\": false,\n        \"forceGenerate\": \"{% $states.context.Execution.Input.force ? $states.context.Execution.Input.force : false %}\"\n      }\n    },\n    \"Regeneration triggered?\": {\n      \"Type\": \"Choice\",\n      \"Choices\": [\n        {\n          \"Condition\": \"{% $forceGenerate = true %}\",\n          \"Next\": \"Try to delete insights\",\n          \"Comment\": \"Force generation requested\"\n        }\n      ],\n      \"Default\": \"Try to get insights\"\n    },\n    \"Try to delete insights\": {\n      \"Type\": \"Task\",\n      \"Resource\": \"arn:aws:states:::dynamodb:deleteItem\",\n      \"Arguments\": {\n        \"TableName\": \"jobfit-insights-dev\",\n        \"Key\": {\n          \"id\": {\n            \"S\": \"{% $insightsId %}\"\n          },\n          \"specifier\": {\n            \"S\": \"{% $variant & '_' & $lang %}\"\n          }\n        }\n      },\n      \"Next\": \"SQS SendMessage\"\n    },\n    \"Try to get insights\": {\n      \"Type\": \"Task\",\n      \"Resource\": \"arn:aws:states:::dynamodb:getItem\",\n      \"Arguments\": {\n        \"TableName\": \"jobfit-insights-dev\",\n        \"Key\": {\n          \"id\": {\n            \"S\": \"{% $insightsId %}\"\n          },\n          \"specifier\": {\n            \"S\": \"{% $variant & '_' & $lang %}\"\n          }\n        }\n      },\n      \"Next\": \"Do they already exist?\"\n    },\n    \"Do they already exist?\": {\n      \"Type\": \"Choice\",\n      \"Choices\": [\n        {\n          \"Next\": \"Return insights\",\n          \"Condition\": \"{% $exists($states.input.Item) %}\",\n          \"Comment\": \"Yes\",\n          \"Output\": \"{% $states.input.Item %}\"\n        }\n      ],\n      \"Default\": \"Generation already triggered?\"\n    },\n    \"Generation already triggered?\": {\n      \"Type\": \"Choice\",\n      \"Choices\": [\n        {\n          \"Next\": \"Wait\",\n          \"Condition\": \"{% $generationTriggered %}\",\n          \"Comment\": \"Yes\"\n        }\n      ],\n      \"Default\": \"SQS SendMessage\"\n    },\n    \"SQS SendMessage\": {\n      \"Type\": \"Task\",\n      \"Resource\": \"arn:aws:states:::sqs:sendMessage\",\n      \"Arguments\": {\n        \"MessageBody\": \"{% $request %}\",\n        \"QueueUrl\": \"https://sqs.eu-central-1.amazonaws.com/************/jobfit-extraction-dev-lambda\"\n      },\n      \"Next\": \"Wait\",\n      \"Assign\": {\n        \"generationTriggered\": true\n      }\n    },\n    \"Return insights\": {\n      \"Type\": \"Succeed\"\n    },\n    \"Wait\": {\n      \"Type\": \"Wait\",\n      \"Seconds\": 1,\n      \"Next\": \"Try to get insights\"\n    }\n  },\n  \"QueryLanguage\": \"JSONata\",\n  \"TimeoutSeconds\": 60,\n  \"Comment\": \"Synchronously generates insights (if not already available)\"\n}\n", "description": "", "encryption_configuration": [{"kms_data_key_reuse_period_seconds": 0, "kms_key_id": "", "type": "AWS_OWNED_KEY"}], "id": "arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights", "logging_configuration": [{"include_execution_data": true, "level": "ALL", "log_destination": "arn:aws:logs:eu-central-1:************:log-group:/aws/states/jobfit-await-insights:*"}], "name": "jobfit-api-dev-await-insights", "name_prefix": "", "publish": false, "revision_id": "b7d3f5c4-dd7b-4965-b004-e720a4cbaf3e", "role_arn": "arn:aws:iam::************:role/jobfit-api-dev-await-insights", "state_machine_version_arn": "arn:aws:states:eu-central-1:************:stateMachine:jobfit-api-dev-await-insights:5", "status": "ACTIVE", "tags": {}, "tags_all": {}, "timeouts": null, "tracing_configuration": [{"enabled": false}], "type": "EXPRESS", "version_description": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwfX0=", "dependencies": ["aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "aws_wafv2_ip_set", "name": "blocklist_malicious_ips", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"addresses": ["*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "*************/32", "***********/32", "*************/32"], "arn": "arn:aws:wafv2:eu-central-1:************:regional/ipset/jobfit-api-dev-malicious-ips/5052c997-2e9c-49ae-865e-737c3ad8317f", "description": "Malicious IPs that are blocked by CORE", "id": "5052c997-2e9c-49ae-865e-737c3ad8317f", "ip_address_version": "IPV4", "lock_token": "8a8ded64-1a5f-4ebe-a18f-e6b79b99e9f3", "name": "jobfit-api-dev-malicious-ips", "scope": "REGIONAL", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_wafv2_ip_set", "name": "office_ips", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"addresses": ["***************/29", "***************/32", "************/32"], "arn": "arn:aws:wafv2:eu-central-1:************:regional/ipset/jobfit-api-dev-office-ips/81e3a0c1-34c7-4446-aa52-fb3990465ada", "description": "Office IP addresses for generic access", "id": "81e3a0c1-34c7-4446-aa52-fb3990465ada", "ip_address_version": "IPV4", "lock_token": "97ec2314-500f-4836-a798-ec1e3eef8d80", "name": "jobfit-api-dev-office-ips", "scope": "REGIONAL", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["data.aws_ec2_managed_prefix_list.office_admin"]}]}, {"mode": "managed", "type": "aws_wafv2_web_acl", "name": "jobfit_waf_rules", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"application_integration_url": "", "arn": "arn:aws:wafv2:eu-central-1:************:regional/webacl/jobfit-api-dev-alb/0c6555d7-cc6e-4c25-8603-e4baf5b09536", "association_config": [], "capacity": 1042, "captcha_config": [], "challenge_config": [], "custom_response_body": [{"content": "{\"error\":\"Forbidden\"}", "content_type": "APPLICATION_JSON", "key": "403_forbidden"}, {"content": "{\"error\":\"Too Many Requests\"}", "content_type": "APPLICATION_JSON", "key": "429_too_many_requests"}], "default_action": [{"allow": [{"custom_request_handling": []}], "block": []}], "description": "", "id": "0c6555d7-cc6e-4c25-8603-e4baf5b09536", "lock_token": "0612b0aa-3c7b-44db-ac8a-f18235e0d12f", "name": "jobfit-api-dev-alb", "rule": [{"action": [{"allow": [{"custom_request_handling": []}], "block": [], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-zurich-office-ips", "override_action": [], "priority": 1, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [{"arn": "arn:aws:wafv2:eu-central-1:************:regional/ipset/jobfit-api-dev-office-ips/81e3a0c1-34c7-4446-aa52-fb3990465ada", "ip_set_forwarded_ip_config": []}], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-zurich-office-ips", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": [{"custom_response_body_key": "403_forbidden", "response_code": 403, "response_header": []}]}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-blocked-countries", "override_action": [], "priority": 3, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["CN", "RU", "KR", "KP"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-blocked-countries", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": [{"custom_response_body_key": "429_too_many_requests", "response_code": 429, "response_header": []}]}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-strict-rate-limiting", "override_action": [], "priority": 6, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [{"aggregate_key_type": "IP", "custom_key": [], "evaluation_window_sec": 300, "forwarded_ip_config": [], "limit": 300, "scope_down_statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [{"statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["AT", "DE", "CH", "LI", "IE", "FR"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-strict-rate-limiting", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": [{"custom_response_body_key": "429_too_many_requests", "response_code": 429, "response_header": []}]}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-strict-rate-limiting-headers", "override_action": [], "priority": 7, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [{"aggregate_key_type": "FORWARDED_IP", "custom_key": [], "evaluation_window_sec": 300, "forwarded_ip_config": [{"fallback_behavior": "MATCH", "header_name": "X-Forwarded-For"}], "limit": 300, "scope_down_statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [{"statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["AT", "DE", "CH", "LI", "IE", "FR"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-strict-rate-limiting-headers", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": []}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-malicious-ips", "override_action": [], "priority": 2, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [{"arn": "arn:aws:wafv2:eu-central-1:************:regional/ipset/jobfit-api-dev-malicious-ips/5052c997-2e9c-49ae-865e-737c3ad8317f", "ip_set_forwarded_ip_config": []}], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-malicious-ips", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": []}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-soft-rate-limiting", "override_action": [], "priority": 4, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [{"aggregate_key_type": "IP", "custom_key": [], "evaluation_window_sec": 300, "forwarded_ip_config": [], "limit": 9000, "scope_down_statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["AT", "DE", "CH", "LI", "IE", "FR"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-soft-rate-limiting", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [{"custom_response": []}], "captcha": [], "challenge": [], "count": []}], "captcha_config": [], "name": "jobfit-api-dev-alb-soft-rate-limiting-headers", "override_action": [], "priority": 5, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [], "rate_based_statement": [{"aggregate_key_type": "FORWARDED_IP", "custom_key": [], "evaluation_window_sec": 300, "forwarded_ip_config": [{"fallback_behavior": "MATCH", "header_name": "X-Forwarded-For"}], "limit": 9000, "scope_down_statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["AT", "DE", "CH", "LI", "IE", "FR"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb-soft-rate-limiting-headers", "sampled_requests_enabled": true}]}, {"action": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": [{"insert_header": [{"name": "country", "value": "CH"}]}]}]}], "captcha_config": [], "name": "jobfit-api-dev-alb-set-country-header-ch", "override_action": [], "priority": 0, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [], "not_statement": [], "or_statement": [{"statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["CH"], "forwarded_ip_config": []}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}, {"and_statement": [], "byte_match_statement": [], "geo_match_statement": [{"country_codes": ["CH"], "forwarded_ip_config": [{"fallback_behavior": "MATCH", "header_name": "X-Forwarded-For"}]}], "ip_set_reference_statement": [], "label_match_statement": [], "not_statement": [], "or_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}]}], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "apis-jobfit-api-dev-alb-country-header-ch", "sampled_requests_enabled": true}]}, {"action": [], "captcha_config": [], "name": "AWS-AWSManagedRulesAmazonIpReputationList", "override_action": [{"count": [], "none": [{}]}], "priority": 8, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [{"managed_rule_group_configs": [], "name": "AWSManagedRulesAmazonIpReputationList", "rule_action_override": [], "scope_down_statement": [], "vendor_name": "AWS", "version": ""}], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "AWS-AWSManagedRulesAmazonIpReputationList", "sampled_requests_enabled": true}]}, {"action": [], "captcha_config": [], "name": "AWS-AWSManagedRulesCommonRuleSet", "override_action": [{"count": [], "none": [{}]}], "priority": 9, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [{"managed_rule_group_configs": [], "name": "AWSManagedRulesCommonRuleSet", "rule_action_override": [{"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "NoUserAgent_HEADER"}, {"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "SizeRestrictions_QUERYSTRING"}, {"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "SizeRestrictions_BODY"}, {"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "UserAgent_BadBots_HEADER"}, {"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "CrossSiteScripting_BODY"}, {"action_to_use": [{"allow": [], "block": [], "captcha": [], "challenge": [], "count": [{"custom_request_handling": []}]}], "name": "GenericLFI_BODY"}], "scope_down_statement": [], "vendor_name": "AWS", "version": ""}], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "AWS-AWSManagedRulesCommonRuleSet", "sampled_requests_enabled": true}]}, {"action": [], "captcha_config": [], "name": "AWS-AWSManagedRulesKnownBadInputsRuleSet", "override_action": [{"count": [], "none": [{}]}], "priority": 10, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [{"managed_rule_group_configs": [], "name": "AWSManagedRulesKnownBadInputsRuleSet", "rule_action_override": [], "scope_down_statement": [], "vendor_name": "AWS", "version": ""}], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "AWS-AWSManagedRulesKnownBadInputsRuleSet", "sampled_requests_enabled": true}]}, {"action": [], "captcha_config": [], "name": "AWS-AWSManagedRulesPHPRuleSet", "override_action": [{"count": [], "none": [{}]}], "priority": 11, "rule_label": [], "statement": [{"and_statement": [], "byte_match_statement": [], "geo_match_statement": [], "ip_set_reference_statement": [], "label_match_statement": [], "managed_rule_group_statement": [{"managed_rule_group_configs": [], "name": "AWSManagedRulesPHPRuleSet", "rule_action_override": [], "scope_down_statement": [], "vendor_name": "AWS", "version": ""}], "not_statement": [], "or_statement": [], "rate_based_statement": [], "regex_match_statement": [], "regex_pattern_set_reference_statement": [], "rule_group_reference_statement": [], "size_constraint_statement": [], "sqli_match_statement": [], "xss_match_statement": []}], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "AWS-AWSManagedRulesPHPRuleSet", "sampled_requests_enabled": true}]}], "rule_json": "", "scope": "REGIONAL", "tags": {}, "tags_all": {}, "token_domains": [], "visibility_config": [{"cloudwatch_metrics_enabled": true, "metric_name": "jobfit-api-dev-alb", "sampled_requests_enabled": true}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_wafv2_ip_set.blocklist_malicious_ips", "aws_wafv2_ip_set.office_ips", "data.aws_ec2_managed_prefix_list.office_admin"]}]}, {"mode": "managed", "type": "aws_wafv2_web_acl_association", "name": "apigw_v2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "arn:aws:wafv2:eu-central-1:************:regional/webacl/jobfit-api-dev-alb/0c6555d7-cc6e-4c25-8603-e4baf5b09536,arn:aws:apigateway:eu-central-1::/restapis/kq9dui6bcf/stages/dev", "resource_arn": "arn:aws:apigateway:eu-central-1::/restapis/kq9dui6bcf/stages/dev", "timeouts": null, "web_acl_arn": "arn:aws:wafv2:eu-central-1:************:regional/webacl/jobfit-api-dev-alb/0c6555d7-cc6e-4c25-8603-e4baf5b09536"}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "aws_wafv2_ip_set.blocklist_malicious_ips", "aws_wafv2_ip_set.office_ips", "aws_wafv2_web_acl.jobfit_waf_rules", "data.aws_dynamodb_table.insights", "data.aws_ec2_managed_prefix_list.office_admin", "data.aws_sqs_queue.jobfit_extraction"]}]}, {"mode": "managed", "type": "terraform_data", "name": "jwt_secret", "provider": "provider[\"terraform.io/builtin/terraform\"]", "instances": [{"index_key": "jobfit-demo", "schema_version": 0, "attributes": {"id": "2fefcd0e-0feb-82ce-e68a-f3e762bcac43", "input": {"value": {"env": "dev", "name": "jobfit-demo", "store": "arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814"}, "type": ["object", {"env": "string", "name": "string", "store": "string"}]}, "output": {"value": {"env": "dev", "name": "jobfit-demo", "store": "arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814"}, "type": ["object", {"env": "string", "name": "string", "store": "string"}]}, "triggers_replace": {"value": ["arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814", "jobfit-demo", "Sn6vofs6rH6lsUjf202ZXarWyFvwB0Fp2kZhKn8U", true], "type": ["tuple", ["string", "string", "string", "bool"]]}}, "sensitive_attributes": [[{"type": "get_attr", "value": "triggers_replace"}, {"type": "index", "value": {"value": 2, "type": "number"}}]], "dependencies": ["aws_api_gateway_api_key.api_key", "aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudfront_key_value_store.secrets", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}, {"index_key": "jobfit-seekers-profile", "schema_version": 0, "attributes": {"id": "5ace9925-e85e-d674-f790-07613705548a", "input": {"value": {"env": "dev", "name": "jobfit-seekers-profile", "store": "arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814"}, "type": ["object", {"env": "string", "name": "string", "store": "string"}]}, "output": {"value": {"env": "dev", "name": "jobfit-seekers-profile", "store": "arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814"}, "type": ["object", {"env": "string", "name": "string", "store": "string"}]}, "triggers_replace": {"value": ["arn:aws:cloudfront::************:key-value-store/87b81625-c6be-410a-adf1-bade6c246814", "jobfit-seekers-profile", "dIFWiOxt77aHIGybwVpuj7oJy0wWCptc7nPo4y8c", true], "type": ["tuple", ["string", "string", "string", "bool"]]}}, "sensitive_attributes": [[{"type": "get_attr", "value": "triggers_replace"}, {"type": "index", "value": {"value": 2, "type": "number"}}]], "dependencies": ["aws_api_gateway_api_key.api_key", "aws_api_gateway_deployment.jobfit", "aws_api_gateway_documentation_version.jobfit", "aws_api_gateway_rest_api.jobfit", "aws_api_gateway_stage.jobfit", "aws_api_gateway_usage_plan.demo", "aws_api_gateway_usage_plan.seeker_browser", "aws_cloudfront_key_value_store.secrets", "aws_cloudwatch_log_group.awaiter", "aws_iam_role.awaiter", "aws_iam_role_policy.awaiter_dynamodb", "aws_iam_role_policy.awaiter_logs", "aws_iam_role_policy.awaiter_sqs", "aws_sfn_state_machine.awaiter", "data.aws_dynamodb_table.insights", "data.aws_sqs_queue.jobfit_extraction"]}]}], "check_results": null}