# --------------------------------------------------
# Project Configuration
# --------------------------------------------------

terraform {
  backend "s3" {
    bucket               = "fintech-tf-states"
    workspace_key_prefix = "fintech-api-service"
    key                  = "state.tfstate"
    region               = "eu-central-1"
    encrypt              = true
    kms_key_id           = "TODO-add-key"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.75.1"
    }
  }

  required_version = ">= 1.5.2"
}

provider "aws" {
  region = "eu-central-1"
  assume_role {
    role_arn = local.settings.role_arn
  }
}

locals {
  env = terraform.workspace

  project = {
    name = "fintech-api-service"
    env  = local.env
  }

  settings = {
    role_arn = var.workspace_settings[local.env].role_arn
  }

  # Service configuration
  service_name = "fintech-api"
  container_port = 8000
  
  # Resource naming
  cluster_name = "${local.project.name}-${local.env}"
  service_name_full = "${local.service_name}-${local.env}"
  task_definition_name = "${local.service_name}-${local.env}"
}

# --------------------------------------------------
# Variables
# --------------------------------------------------

variable "workspace_settings" {
  type = map(object({
    role_arn = string
  }))

  default = {
    dev = {
      role_arn = "arn:aws:iam::407276168849:role/tf-infra-dev-deploy-role",
    }

    prod = {
      role_arn = "arn:aws:iam::304848207041:role/tf-infra-prod-deploy-role",
    }
  }
}

variable "image_tag" {
  description = "Docker image tag to deploy"
  type        = string
  default     = "latest"
}

variable "desired_count" {
  description = "Desired number of tasks"
  type        = number
  default     = 2
}

variable "cpu" {
  description = "CPU units for the task"
  type        = number
  default     = 512
}

variable "memory" {
  description = "Memory for the task"
  type        = number
  default     = 1024
}

# --------------------------------------------------
# Data Sources
# --------------------------------------------------

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Get networking resources
data "terraform_remote_state" "networking" {
  backend = "s3"
  config = {
    bucket = "fintech-tf-states"
    key    = "fintech-networking/state.tfstate"
    region = "eu-central-1"
  }
  workspace = local.env
}

# Get ECR repository
data "aws_ecr_repository" "api_service" {
  name = "fintech/api-service"
}
