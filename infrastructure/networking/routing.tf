# --------------------------------------------------
# NAT
# --------------------------------------------------

resource "aws_eip" "nat_eip" {
  domain = "vpc"

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-nat-eip"
  }
}

resource "aws_nat_gateway" "nat_gateway" {
  allocation_id = aws_eip.nat_eip.id
  subnet_id     = aws_subnet.public_subnet.id

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-nat-gateway"
  }
}

# --------------------------------------------------
# Internet Gateway
# --------------------------------------------------

resource "aws_internet_gateway" "internet_gateway" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-internet-gateway"
  }
}

# IPv6 Egress-only Internet Gateway
resource "aws_egress_only_internet_gateway" "ipv6_egress_igw" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-internet-gateway-v6-egress-only"
  }
}

# --------------------------------------------------
# Routing
# --------------------------------------------------

resource "aws_route_table" "public_route_table" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-rtb-public"
  }
}

resource "aws_route_table_association" "public_subnet_routing" {
  route_table_id = aws_route_table.public_route_table.id
  subnet_id      = aws_subnet.public_subnet.id
}

resource "aws_route" "public_route_igw" {
  route_table_id         = aws_route_table.public_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.internet_gateway.id
}

resource "aws_route_table" "private_route_table" {
  vpc_id = aws_vpc.vpc.id

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-rtb-private"
  }
}

resource "aws_route" "private_ipv6_egress_igw" {
  route_table_id              = aws_route_table.private_route_table.id
  destination_ipv6_cidr_block = "::/0"
  egress_only_gateway_id      = aws_egress_only_internet_gateway.ipv6_egress_igw.id
}

resource "aws_route" "private_route_nat" {
  route_table_id         = aws_route_table.private_route_table.id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.nat_gateway.id
}

resource "aws_route_table_association" "private_subnet_routing" {
  route_table_id = aws_route_table.private_route_table.id
  subnet_id      = aws_subnet.private_subnet.id
}
