resource "aws_subnet" "public_subnet" {

  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = local.public_subnet_cidr_block
  ipv6_cidr_block         = cidrsubnet(aws_vpc.vpc.ipv6_cidr_block, 8, 0)
  availability_zone_id    = local.availability_zone_id
  map_public_ip_on_launch = true

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-public-subnet"
  }
}

resource "aws_subnet" "private_subnet" {
  vpc_id                  = aws_vpc.vpc.id
  cidr_block              = local.private_subnet_cidr_block
  ipv6_cidr_block         = cidrsubnet(aws_vpc.vpc.ipv6_cidr_block, 8, 1)
  availability_zone_id    = local.availability_zone_id
  map_public_ip_on_launch = false

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-private-subnet"
  }
}
