resource "aws_security_group" "sg_lambda" {
  description = "Allow outgoing connections on port 443 to the internet"
  vpc_id      = aws_vpc.vpc.id
  name        = "${local.project.name}-${local.project.env}-sg-lambda"

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-sg-lambda"
  }
}

resource "aws_security_group_rule" "out_lambda_https_lambda_ipv4" {
  protocol          = "tcp"
  security_group_id = aws_security_group.sg_lambda.id
  cidr_blocks       = ["0.0.0.0/0"]
  from_port         = 443
  to_port           = 443
  type              = "egress"
}

resource "aws_security_group_rule" "out_lambda_https_lambda_ipv6" {
  protocol          = "tcp"
  security_group_id = aws_security_group.sg_lambda.id
  ipv6_cidr_blocks  = ["::/0"]
  from_port         = 443
  to_port           = 443
  type              = "egress"
}

resource "aws_security_group_rule" "out_lambda_avro_schema_ipv4" {
  protocol          = "tcp"
  from_port         = 24871
  to_port           = 24871
  security_group_id = aws_security_group.sg_lambda.id
  cidr_blocks       = ["0.0.0.0/0"]
  type              = "egress"
}

resource "aws_security_group_rule" "out_lambda_avro_schema_ipv6" {
  protocol          = "tcp"
  from_port         = 24871
  to_port           = 24871
  security_group_id = aws_security_group.sg_lambda.id
  ipv6_cidr_blocks  = ["::/0"]
  type              = "egress"
}

resource "aws_security_group" "sg_pipe" {
  vpc_id      = aws_vpc.vpc.id
  name        = "${local.project.name}-${local.project.env}-sg-pipe"

  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-sg-pipe"
  }
}

resource "aws_security_group_rule" "out_pipe_https_lambda_ipv4" {
  protocol          = "tcp"
  security_group_id = aws_security_group.sg_pipe.id
  cidr_blocks       = ["0.0.0.0/0"]
  from_port         = 443
  to_port           = 443
  type              = "egress"
}

resource "aws_security_group_rule" "out_pipe_https_lambda_ipv6" {
  protocol          = "tcp"
  security_group_id = aws_security_group.sg_pipe.id
  ipv6_cidr_blocks  = ["::/0"]
  from_port         = 443
  to_port           = 443
  type              = "egress"
}

resource "aws_security_group_rule" "out_pipe_avro_schema_ipv4" {
  protocol          = "tcp"
  from_port         = 24868
  to_port           = 24868
  security_group_id = aws_security_group.sg_pipe.id
  cidr_blocks       = ["0.0.0.0/0"]
  type              = "egress"
}

resource "aws_security_group_rule" "out_pipe_avro_schema_ipv6" {
  protocol          = "tcp"
  from_port         = 24868
  to_port           = 24868
  security_group_id = aws_security_group.sg_pipe.id
  ipv6_cidr_blocks  = ["::/0"]
  type              = "egress"
}