# --------------------------------------------------
# Project Configuration
# --------------------------------------------------

terraform {
  backend "s3" {
    bucket               = "fintech-tf-states"
    workspace_key_prefix = "fintech-networking"
    key                  = "state.tfstate"
    region               = "eu-central-1"
    encrypt              = true
    kms_key_id           = "TODO-add-key"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.75.1"
    }
  }

  required_version = ">= 1.5.2"
}

provider "aws" {
  region = "eu-central-1"
  assume_role {
    role_arn = local.settings.role_arn
  }
}

locals {
  env                       = terraform.workspace
  vpc_cidr_blocks           = "10.0.0.0/16"
  public_subnet_cidr_block  = "10.0.0.0/22"
  private_subnet_cidr_block = "10.0.4.0/22"

  availability_zone_id = "euc1-az3"

  project = {
    name = "fintech-networking"
    env  = local.env
  }

  settings = {
    role_arn = var.workspace_settings[local.env].role_arn
  }
}

# --------------------------------------------------
# Variables
# --------------------------------------------------

variable "workspace_settings" {
  type = map(object({
    role_arn = string
  }))

  default = {
    dev = {
      role_arn = "arn:aws:iam::407276168849:role/tf-infra-dev-deploy-role",
    }

    prod = {
      role_arn = "arn:aws:iam::304848207041:role/tf-infra-prod-deploy-role",
    }
  }
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
