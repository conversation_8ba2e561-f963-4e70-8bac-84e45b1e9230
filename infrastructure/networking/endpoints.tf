# VPC Endpoint for ECR
resource "aws_vpc_endpoint" "ecr" {
  vpc_id            = aws_vpc.vpc.id
  vpc_endpoint_type = "Interface"
  service_name      = "com.amazonaws.${data.aws_region.current.name}.ecr.api"
  subnet_ids        = [aws_subnet.private_subnet.id]
}

resource "aws_vpc_endpoint" "ecr_docker" {
  vpc_id            = aws_vpc.vpc.id
  vpc_endpoint_type = "Interface"
  service_name      = "com.amazonaws.${data.aws_region.current.name}.ecr.dkr"
  subnet_ids        = [aws_subnet.private_subnet.id]
}

resource "aws_vpc_endpoint" "dynamodb" {
  vpc_id          = aws_vpc.vpc.id
  service_name    = "com.amazonaws.${data.aws_region.current.name}.dynamodb"
  route_table_ids = [aws_route_table.private_route_table.id]

  policy = data.aws_iam_policy_document.dynamodb_endpoint_policy.json
  tags = {
    Project = local.project.name
    Env     = local.project.env
    Name    = "${local.project.name}-${local.project.env}-fintech-elaboration"
  }
}

data "aws_iam_policy_document" "dynamodb_endpoint_policy" {
  statement {
    effect = "Allow"

    actions = [
      "dynamodb:GetItem",
      "dynamodb:PutItem",
      "dynamodb:DeleteItem",
      "dynamodb:UpdateItem",
      "dynamodb:Query",
      "dynamodb:Scan",
      "dynamodb:BatchGetItem",
      "dynamodb:BatchWriteItem"
    ]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    resources = "*"
  }
}
