# Auth0 OAuth2 Integration

This document describes the complete Auth0 OAuth2 integration for the fintech monorepo, including Next.js frontend and Python FastAPI backend.

## Overview

The integration provides:
- **Frontend**: Next.js 15 with Auth0 authentication using `@auth0/nextjs-auth0`
- **Backend**: FastAPI with JWT validation against Auth0's JWKS
- **Quality**: Full test coverage, linting, formatting, and type checking
- **CI/CD**: GitHub Actions workflow for automated testing

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js App   │    │   Auth0 Tenant  │    │  FastAPI Backend│
│                 │    │                 │    │                 │
│ - Login/Logout  │◄──►│ - User Auth     │    │ - JWT Validation│
│ - User Profile  │    │ - Token Issue   │    │ - Protected APIs│
│ - API Calls     │    │ - JWKS Endpoint │    │ - User Context  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              ▲
         │                                              │
         └──────────────────────────────────────────────┘
                    Bearer Token in API calls
```

## Frontend Implementation

### 1. Dependencies
```bash
npm install @auth0/nextjs-auth0 @types/jsonwebtoken
```

### 2. Environment Configuration
Create `.env.local`:
```env
AUTH0_SECRET='use [openssl rand -hex 32] to generate a 32 bytes value'
AUTH0_BASE_URL='http://localhost:3000'
AUTH0_ISSUER_BASE_URL='https://your-domain.auth0.com'
AUTH0_CLIENT_ID='your-auth0-client-id'
AUTH0_CLIENT_SECRET='your-auth0-client-secret'
API_BASE_URL='http://localhost:8000'
```

### 3. Key Components

#### Auth0 API Routes
- `/api/auth/[...auth0]` - Handles all Auth0 authentication flows

#### Authentication Components
- `LoginButton` - Login/logout functionality
- `UserProfile` - Display user information
- `AuthNavigation` - Navigation with user dropdown

#### API Client
- `ApiClient` - Handles authenticated API calls with automatic token inclusion

### 4. Protected Pages
Pages can be protected using `withPageAuthRequired`:
```typescript
export default withPageAuthRequired(function Profile() {
  return <div>Protected content</div>;
});
```

## Backend Implementation

### 1. Dependencies
```bash
uv add "python-jose[cryptography]" python-multipart
```

### 2. Environment Configuration
Create `.env`:
```env
AUTH0_DOMAIN=your-domain.auth0.com
AUTH0_API_AUDIENCE=https://your-api.example.com
AUTH0_ALGORITHMS=RS256
```

### 3. Key Components

#### Auth0 JWT Bearer
- `Auth0JWTBearer` - JWT token validation class
- `User` - Pydantic model for user data
- `get_current_user` - FastAPI dependency for protected routes

#### Protected Routes
```python
@router.get("/protected")
async def protected_endpoint(current_user: User = Depends(get_current_user)):
    return {"message": "Hello authenticated user!", "user": current_user}
```

### 4. Available Endpoints

#### Authentication Routes (`/auth`)
- `GET /auth/me` - Get current user info
- `GET /auth/protected` - Example protected endpoint
- `GET /auth/public` - Example public endpoint
- `GET /auth/optional-auth` - Example optional auth endpoint

#### User Routes (`/api/users`)
- `GET /api/users/me` - Get current user from Auth0
- `GET /api/users/me/summary` - Get user account summary

## Quality Assurance

### Frontend Testing
```bash
cd webapp/fintech-web-app
npm run test          # Run tests
npm run lint          # Run ESLint
npm run type-check    # Run TypeScript check
npm run format        # Run Prettier
```

### Backend Testing
```bash
cd services/api
uv run pytest tests/ -v              # Run tests
uv run ruff check src --fix          # Run linter
uv run black src                     # Run formatter
uv run mypy src --ignore-missing-imports  # Run type checker
```

### Test Coverage
- **Frontend**: 15 tests covering Auth0 components and API client
- **Backend**: Comprehensive tests for JWT validation and protected routes

## CI/CD Pipeline

GitHub Actions workflow (`.github/workflows/ci.yml`) includes:

1. **Frontend Tests**
   - Dependency installation
   - Linting and type checking
   - Unit tests
   - Build verification

2. **Backend Tests**
   - Python environment setup
   - Linting and type checking
   - Unit tests with mocked Auth0

3. **Integration Tests**
   - End-to-end API testing
   - Health check verification

## Auth0 Configuration

### 1. Create Auth0 Application
1. Go to Auth0 Dashboard
2. Create new Single Page Application
3. Configure allowed URLs:
   - **Allowed Callback URLs**: `http://localhost:3000/api/auth/callback`
   - **Allowed Logout URLs**: `http://localhost:3000`
   - **Allowed Web Origins**: `http://localhost:3000`

### 2. Create Auth0 API
1. Create new API in Auth0 Dashboard
2. Set identifier (use as `AUTH0_API_AUDIENCE`)
3. Enable RBAC if needed

### 3. Configure Environment Variables
Update both frontend and backend `.env` files with your Auth0 credentials.

## Development Workflow

### 1. Start Backend
```bash
cd services/api
uv run uvicorn fintech.api.app:app --reload --host 0.0.0.0 --port 8000
```

### 2. Start Frontend
```bash
cd webapp/fintech-web-app
npm run dev
```

### 3. Test Integration
1. Visit `http://localhost:3000`
2. Click login to authenticate with Auth0
3. Visit `/profile` to see protected content
4. Visit `/demo` to test API integration

## Security Considerations

1. **Token Validation**: Backend validates JWT signatures against Auth0 JWKS
2. **CORS Configuration**: Properly configured for frontend-backend communication
3. **Environment Variables**: Sensitive data stored in environment variables
4. **HTTPS**: Use HTTPS in production
5. **Token Expiration**: Tokens have expiration times enforced

## Troubleshooting

### Common Issues

1. **CORS Errors**: Check CORS configuration in FastAPI app
2. **Token Validation Fails**: Verify Auth0 domain and audience configuration
3. **Login Redirect Issues**: Check Auth0 callback URLs
4. **API Calls Fail**: Ensure API base URL is correct

### Debug Mode
Enable debug logging by setting environment variables:
```env
DEBUG=true
LOG_LEVEL=debug
```

## Production Deployment

### Frontend (Vercel/Netlify)
1. Set environment variables in deployment platform
2. Update Auth0 URLs for production domain
3. Configure build settings

### Backend (Docker/ECS)
1. Build Docker image with environment variables
2. Deploy to container service
3. Update CORS origins for production frontend

## Next Steps

1. **Role-Based Access Control (RBAC)**: Implement user roles and permissions
2. **Multi-Factor Authentication**: Enable MFA in Auth0
3. **Social Logins**: Add Google, GitHub, etc. login options
4. **User Management**: Build admin interface for user management
5. **Audit Logging**: Add comprehensive audit trails

## Support

For issues or questions:
1. Check Auth0 documentation: https://auth0.com/docs
2. Review FastAPI security docs: https://fastapi.tiangolo.com/tutorial/security/
3. Check Next.js Auth0 guide: https://auth0.com/docs/quickstart/webapp/nextjs
