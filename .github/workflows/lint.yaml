name: Linter

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  check:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: <PERSON><PERSON> (standard)
        uses: astral-sh/ruff-action@v2
        with:
          src: >-
            libs
            services
            tools

      - name: <PERSON><PERSON> (imports)
        uses: astral-sh/ruff-action@v2
        with:
          src: >-
            libs
            services
            tools
          args: check --select I
