name: Test

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  run:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    env:
      SSH_AUTH_SOCK: /tmp/ssh_agent.sock

    strategy:
      fail-fast: false
      matrix:
        module:
          - name: fintech-clients
            path: libs/clients

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v4

      - name: Set up Python
        uses: actions/setup-python@v5

      - name: Add SSH key
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -v github.com >> ~/.ssh/known_hosts
          ssh-agent -a $SSH_AUTH_SOCK > /dev/null
          ssh-add -v - <<< $SSH_KEY
        env:
          SSH_KEY: ${{ secrets.FINTECH_GITHUB_PK_DECRYPTED }}

      - name: Setup git
        run: git config --global url."ssh://**************/".insteadOf https://github.com/

      - name: Test ${{ matrix.module.name }}
        working-directory: ${{ matrix.module.path }}
        run: |
          set -o pipefail
          uv run --extra test ${{ matrix.module.extra-deps }} pytest --junitxml=pytest.xml
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}

      - uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.module.name }}
          path: |
            ${{ matrix.module.path }}/pytest.xml
          retention-days: 1