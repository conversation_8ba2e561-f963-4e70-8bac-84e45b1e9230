name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  frontend:
    name: Frontend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./webapp/fintech-web-app

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: ./webapp/fintech-web-app/package-lock.json

    - name: Install dependencies
      run: npm ci --legacy-peer-deps

    - name: Run linter
      run: npm run lint

    - name: Run type check
      run: npm run type-check

    - name: Run tests
      run: npm run test

    - name: Check formatting
      run: npm run format:check

    - name: Build
      run: npm run build

  backend:
    name: Backend Tests
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./services/api

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: fintech_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.11

    - name: Install dependencies
      run: uv sync --all-extras --dev

    - name: Run linter
      run: uv run lint

    - name: Run type check
      run: uv run type-check

    - name: Check formatting
      run: uv run format-check

    - name: Run tests
      run: uv run test
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/fintech_test
        AUTH0_DOMAIN: test.auth0.com
        AUTH0_API_AUDIENCE: test-audience
        AUTH0_ALGORITHMS: RS256

  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [frontend, backend]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: fintech_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: ./webapp/fintech-web-app/package-lock.json

    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.11

    - name: Install frontend dependencies
      working-directory: ./webapp/fintech-web-app
      run: npm ci --legacy-peer-deps

    - name: Install backend dependencies
      working-directory: ./services/api
      run: uv sync --all-extras --dev

    - name: Build frontend
      working-directory: ./webapp/fintech-web-app
      run: npm run build

    - name: Start backend server
      working-directory: ./services/api
      run: |
        uv run dev &
        sleep 10
      env:
        DATABASE_URL: postgresql://test_user:test_password@localhost:5432/fintech_test
        AUTH0_DOMAIN: test.auth0.com
        AUTH0_API_AUDIENCE: test-audience

    - name: Test API endpoints
      run: |
        curl -f http://localhost:8000/health/ || exit 1
        curl -f http://localhost:8000/auth/public || exit 1
