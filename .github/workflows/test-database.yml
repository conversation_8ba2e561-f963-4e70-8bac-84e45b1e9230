name: Database Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  UV_CACHE_DIR: /tmp/.uv-cache

jobs:
  test-database:
    name: Database Tests (PostgreSQL)
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: ["3.13"]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Set up Docker Compose
      uses: docker/setup-compose-action@v1
      with:
        version: latest

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"
        enable-cache: true

    - name: Restore uv cache
      uses: actions/cache@v4
      with:
        path: /tmp/.uv-cache
        key: uv-${{ runner.os }}-${{ hashFiles('libs/models/pyproject.toml') }}
        restore-keys: |
          uv-${{ runner.os }}-
    
    - uses: extractions/setup-just@v3
      with:
        github-token: ${{ secrets.MY_GITHUB_TOKEN }}
    
    - name: Check Docker
      run: |
        docker --version
        docker info
        docker pull postgres:15-alpine
        
        docker compose -f libs/models/docker-compose.test.yml up -d postgres-test
    
    - name: Install dependencies
      run: |
        cd libs/models
        uv sync --extra test
    
    - name: Run database tests
      run: just test-database

    - name: Minimize uv cache
      run: uv cache prune --ci