name: Typer

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  check:
    permissions:
      id-token: write
      contents: read
    env:
      SSH_AUTH_SOCK: /tmp/ssh_agent.sock

    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        module:
          - libs/app
          - libs/database
          - libs/clients
          - libs/observability
          - libs/testutils
          - services/extract

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v4

      - name: Set up Python
        uses: actions/setup-python@v5

      - name: Add SSH key
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -v github.com >> ~/.ssh/known_hosts
          ssh-agent -a $SSH_AUTH_SOCK > /dev/null
          ssh-add -v - <<< $SSH_KEY
        env:
          SSH_KEY: ${{ secrets.FINTECH_GITHUB_PK_DECRYPTED }}

      - name: Setup git
        run: git config --global url."ssh://**************/".insteadOf https://github.com/

      - name: Sync ${{ matrix.module }}
        working-directory: ${{ matrix.module }}
        run: |
          uv sync --all-extras --all-groups
          source .venv/bin/activate
          echo "$PWD/.venv/bin" >> $GITHUB_PATH

      - name: Typecheck ${{ matrix.module }}
        uses: jakebailey/pyright-action@v2
        with:
          working-directory: ${{ matrix.module }}
