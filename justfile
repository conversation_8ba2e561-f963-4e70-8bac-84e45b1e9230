# FinTech Monorepo - Just Commands
# Run with: just <command>

# Default recipe - show available commands
default:
    @just --list

# Database Testing Commands
# =========================

# Run database tests with PostgreSQL in Docker (default)
test-database:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🐘 Running database tests with PostgreSQL in Docker..."
    echo "📍 Working directory: libs/models"
    cd libs/models

    # Check if Dock<PERSON> is running
    if ! docker info >/dev/null 2>&1; then
        echo "❌ Docker is not running. Please start Docker and try again."
        exit 1
    fi

    # Check if docker compose is available
    if ! command -v docker compose >/dev/null 2>&1; then
        echo "❌ docker compose is not available. Please install docker compose."
        exit 1
    fi

    # Install dependencies if needed
    echo "📦 Installing dependencies..."
    uv sync --extra test

    # Run tests with PostgreSQL
    echo "🧪 Running tests against PostgreSQL..."
    uv run pytest test/ -v --tb=short

    echo "✅ Database tests completed successfully!"



# Check Docker status
check-docker:
    #!/usr/bin/env bash
    echo "🐳 Checking Docker status..."
    if docker info >/dev/null 2>&1; then
        echo "✅ Docker is running"
        echo "📋 Docker version:"
        docker --version
        echo "🐘 Pulling PostgreSQL image..."
        docker pull postgres:15-alpine
        echo "✅ PostgreSQL image ready"
    else
        echo "❌ Docker is not running"
        echo "Please start Docker Desktop and try again"
        exit 1
    fi

# Clean up Docker containers and images
clean-docker:
    #!/usr/bin/env bash
    echo "🧹 Cleaning up Docker containers and images..."

    # Stop docker compose services
    echo "🛑 Stopping docker compose services..."
    cd libs/models
    docker compose -f docker-compose.test.yml down -v || true

    # Stop and remove testcontainers (if any)
    echo "🛑 Stopping testcontainers..."
    docker ps -a --filter "label=org.testcontainers" -q | xargs -r docker rm -f || true

    # Remove unused images
    echo "🗑️  Removing unused Docker images..."
    docker image prune -f

    echo "✅ Docker cleanup completed!"

# Development Commands
# ====================

# Install all dependencies for models library
install-models:
    #!/usr/bin/env bash
    echo "📦 Installing models library dependencies..."
    cd libs/models
    uv sync --extra test --extra typing
    echo "✅ Models library dependencies installed!"

# Run linting and formatting
lint-models:
    #!/usr/bin/env bash
    echo "🔍 Linting models library..."
    cd libs/models
    
    # Install dev dependencies if needed
    uv sync --extra test
    
    # Run ruff for linting and formatting
    echo "🔧 Running ruff check..."
    uv run ruff check src/ test/ --fix
    
    echo "🎨 Running ruff format..."
    uv run ruff format src/ test/
    
    echo "✅ Linting completed!"

# Run type checking
typecheck-models:
    #!/usr/bin/env bash
    echo "🔍 Type checking models library..."
    cd libs/models
    
    # Install typing dependencies if needed
    uv sync --extra typing
    
    # Run mypy
    echo "🔧 Running mypy..."
    uv run mypy src/fintech/models
    
    echo "✅ Type checking completed!"

# CI/CD Commands
# ==============

# Run all checks (used in CI)
ci-check-models:
    #!/usr/bin/env bash
    set -euo pipefail
    echo "🚀 Running CI checks for models library..."
    
    # Run linting
    just lint-models
    
    # Run type checking
    just typecheck-models
    
    # Run tests
    just test-database
    
    echo "✅ All CI checks passed!"


