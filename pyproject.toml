# WARNING: This is just a meta root-project for LOCAL USE. It's NOT used for actual deployment of anything.
#          This way, when working on Fintech locally, you can just use a single virtualenv and get along well with PyCharm.
#
# DO NOT PUT any dependencies other than fintech modules here. These belong to the individual modules.
# PLEASE UPDATE any time you add a new module to the project.

[project]
name = "fintech-root"
version = "0.0.0"
requires-python = "==3.12.*"

dependencies = [
    "fintech-app",
    "fintech-models",
    "fintech-services",
    "fintech-database",
    "fintech-testutils",
    "fintech-observability",
    "fintech-clients",
    "fintech-lambda-service",
    "fintech-tools-e2e_test",
    "ruff>=0.10.0",
    "httpx>=0.28.1",
    "fastapi>=0.115.13",
    "pytest-asyncio>=1.0.0",
    "pyjwt>=2.10.1",
]

[tool.uv.sources]
fintech-app = { path = "libs/app", editable = true }
fintech-models = { path = "libs/models", editable = true }
fintech-services = { path = "libs/services", editable = true }
fintech-database = { path = "libs/database", editable = true }
fintech-observability = { path = "libs/observability", editable = true }
fintech-clients = { path = "libs/clients", editable = true }
fintech-lambda-service = { path = "services/extract", editable = true }
fintech-tools-e2e_test = { path = "tools/e2e_test", editable = true }
fintech-testutils = { path = "libs/testutils", editable = true }

[tool.ruff]
line-length = 88
target-version = "py312"

[tool.ruff.lint]
select = ["E", "W", "F", "I", "N", "UP", "YTT", "S", "BLE", "B", "A", "C4", "T10", "ISC", "ICN", "G", "PIE", "T20", "PYI", "PT", "Q", "RSE", "RET", "SLF", "SIM", "TID", "TCH", "ARG", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "NPY", "RUF"]
ignore = [
    "E501",     # Line too long (handled by formatter)
    "S101",     # Use of assert (acceptable in tests)
    "S104",     # Binding to all interfaces (acceptable for server config)
    "S105",     # Hardcoded password (acceptable in tests)
    "S106",     # Hardcoded password (acceptable in tests and examples)
    "S603",     # Subprocess call (acceptable for test infrastructure)
    "S607",     # Partial executable path (acceptable for docker-compose)
    "PLR0913",  # Too many arguments (acceptable for some functions)
    "PLR2004",  # Magic value used in comparison
    "F403",     # Star imports (acceptable in __init__.py files)
    "F405",     # Name may be undefined from star imports (acceptable in __init__.py)
    "F821",     # Undefined name (acceptable for SQLAlchemy string forward references)
    "RUF022",   # __all__ not sorted (we organize by category, not alphabetically)
    "B008",     # Function call in argument defaults (acceptable for FastAPI Depends)
    "ARG001",   # Unused function argument (acceptable for API handlers)
    "TRY300",   # Consider moving to else block (acceptable pattern)
    "SIM105",   # Use contextlib.suppress (acceptable for simple cases)
    "SLF001",   # Private member access (acceptable in tests)
]

[tool.ruff.lint.isort]
known-first-party = ["fintech"]

[dependency-groups]
dev = [
    "boto3-stubs>=1.36.4",
    "pyright>=1.1.392.post0",
]

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --ignore=libs/services/test --ignore=services/api/test/test_database_integration.py"
