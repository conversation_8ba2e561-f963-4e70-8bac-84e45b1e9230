[project]
name = "fintech-database"
version = "0.0.0"

dependencies = [
    # External:
    "boto3"
]

[project.optional-dependencies]
test = ["fintech-testutils", "moto[dynamodb]"]
typing = ["boto3-stubs"]

[tool.uv.sources]
fintech-testutils = { path = "../testutils", editable = true }

[tool.pytest.ini_options]
pythonpath = ["src"]

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
