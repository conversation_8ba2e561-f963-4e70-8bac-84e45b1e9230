[project]
name = "fintech-app"
version = "0.0.0"

dependencies = [
    "boto3",
    "fintech-observability",
    "pydantic",
]

[project.optional-dependencies]
flask = ["flask"]
typing = ["mypy>=1.0.0", "types-boto3"]

[tool.uv.sources]
fintech-observability = { path = "../observability", editable = true }

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"