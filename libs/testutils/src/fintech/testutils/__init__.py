import json
from base64 import b64encode
from pathlib import Path

DATA_PATH = Path(__file__).parent / "data"
CV_PDF_PATH = DATA_PATH / "cv.pdf"
CV_PDF_PATH_MULTIPAGE = DATA_PATH / "cv_multipage.pdf"
CV_PNG_PATH = DATA_PATH / "cv.png"
CV_JPEG_PATH = DATA_PATH / "cv.jpeg"
CV_JSON_PATH = DATA_PATH / "cv.json"
CV_TXT_PATH = DATA_PATH / "cv.txt"
JOB_PDF_PATH = DATA_PATH / "job.pdf"
JOB_HTML_PATH = DATA_PATH / "job.html"
JOB_JSON_PATH = DATA_PATH / "job.json"
JOB_TXT_PATH = DATA_PATH / "job.txt"


def get_candidate_obj() -> dict:
    return {
        "cv": {
            "source": "raw",
            "pdf_base64": get_pdf_cv_base64(),
        },
    }


def get_job_obj() -> dict:
    return {
        "source": "raw",
        "description": get_html_job(),
    }


def get_pdf_cv() -> bytes:
    return _get_file_bytes(CV_PDF_PATH)


def get_pdf_cv_base64() -> str:
    return _get_file_base64(CV_PDF_PATH)


def get_png_cv() -> bytes:
    return _get_file_bytes(CV_PNG_PATH)


def get_png_cv_base64() -> str:
    return _get_file_base64(CV_PNG_PATH)


def get_jpeg_cv() -> bytes:
    return _get_file_bytes(CV_JPEG_PATH)


def get_jpeg_cv_base64() -> str:
    return _get_file_base64(CV_JPEG_PATH)


def get_pdf_cv_multipage() -> bytes:
    return _get_file_bytes(CV_PDF_PATH_MULTIPAGE)


def get_pdf_cv_multipage_base64() -> str:
    return _get_file_base64(CV_PDF_PATH_MULTIPAGE)


def get_json_cv() -> dict:
    with CV_JSON_PATH.open() as f:
        return json.load(f)


def get_txt_cv() -> str:
    return _get_file_bytes(CV_TXT_PATH).decode()


def get_pdf_job() -> bytes:
    return _get_file_bytes(JOB_PDF_PATH)


def get_pdf_job_base64() -> str:
    return _get_file_base64(JOB_PDF_PATH)


def get_html_job() -> bytes:
    return _get_file_bytes(JOB_HTML_PATH)


def get_html_job_base64() -> str:
    return _get_file_base64(JOB_HTML_PATH)


def get_json_job() -> dict:
    with JOB_JSON_PATH.open() as f:
        return json.load(f)


def get_txt_job() -> str:
    return _get_file_bytes(JOB_TXT_PATH).decode()


def _get_file_bytes(path: str | Path) -> bytes:
    with Path(path).open("rb") as f:
        return f.read()


def _get_file_base64(path: str | Path) -> str:
    return b64encode(_get_file_bytes(path)).decode()


def format_mock_response(mock_response: str) -> dict:
    return {
        "choices": [
            {
                "message": {"content": mock_response},
                "logprobs": {"content": [{"logprob": -1.0, "token": "test"}]},
                "finish_reason": "stop",
                "content_filter_results": {},
                "index": 0,
            }
        ],
        "id": "fake_id",
        "usage": {
            "prompt_tokens": 1,
            "completion_tokens": 2,
            "total_tokens": 3,
        },
    }
