import os

import pytest


@pytest.fixture
def mock_env_vars():
    mock_env_vars = {}

    orig_env = {k: os.getenv(k) for k in mock_env_vars}

    # Patch the environment variables
    for k, v in mock_env_vars.items():
        os.environ[k] = v

    # Yield control to the test code
    yield

    # Restore original environment variables
    for k, v in orig_env.items():
        if v is not None:
            os.environ[k] = v
        else:
            os.environ.pop(k, None)


# TODO migrate existing tests
def openai_available():
    return os.getenv("OPENAI_API_KEY") is not None


def is_ci():
    return os.getenv("CI") is not None
