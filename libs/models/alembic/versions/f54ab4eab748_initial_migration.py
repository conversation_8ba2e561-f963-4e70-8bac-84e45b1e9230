"""Initial migration

Revision ID: f54ab4eab748
Revises:
Create Date: 2025-06-10 22:20:16.447403

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f54ab4eab748"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "users",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("email", sa.Text(), nullable=False),
        sa.Column("password_hash", sa.Text(), nullable=False),
        sa.Column("first_name", sa.Text(), nullable=True),
        sa.Column("last_name", sa.Text(), nullable=True),
        sa.Column("country", sa.Text(), nullable=True),
        sa.Column("currency", sa.String(length=3), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email"),
    )
    op.create_table(
        "user_settings",
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("enable_roundups", sa.Boolean(), nullable=False),
        sa.Column("enable_percentage_saving", sa.Boolean(), nullable=False),
        sa.Column("enable_recurring", sa.Boolean(), nullable=False),
        sa.Column("roundup_to", sa.Integer(), nullable=False),
        sa.Column(
            "percentage_saving", sa.Numeric(precision=5, scale=2), nullable=False
        ),
        sa.Column("recurring_amount", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("recurring_interval", sa.String(length=20), nullable=True),
        sa.Column("last_updated", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("user_id"),
    )
    op.create_table(
        "cards",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("card_name", sa.Text(), nullable=True),
        sa.Column("card_type", sa.String(length=10), nullable=True),
        sa.Column("last_four", sa.String(length=4), nullable=True),
        sa.Column("spending_limit", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "goals",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("name", sa.Text(), nullable=True),
        sa.Column("target_amount", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("target_date", sa.Date(), nullable=True),
        sa.Column("strategy", sa.String(length=20), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.CheckConstraint(
            "strategy IN ('fixed_income', 'standard', 'growth')",
            name="check_goal_strategy",
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "mandates",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("provider", sa.Text(), nullable=True),
        sa.Column("mandate_reference", sa.String(length=100), nullable=True),
        sa.Column("signed_at", sa.DateTime(), nullable=True),
        sa.Column("valid", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("mandate_reference"),
    )
    op.create_table(
        "transactions",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("card_id", sa.UUID(), nullable=True),
        sa.Column("amount", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("merchant_name", sa.Text(), nullable=True),
        sa.Column("category", sa.String(length=50), nullable=True),
        sa.Column("timestamp", sa.DateTime(), nullable=False),
        sa.Column("original_transaction_id", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["card_id"],
            ["cards.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "contributions",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("type", sa.String(length=20), nullable=False),
        sa.Column("amount", sa.Numeric(precision=10, scale=2), nullable=False),
        sa.Column("source_transaction_id", sa.UUID(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.CheckConstraint(
            "type IN ('roundup', 'percentage', 'recurring')",
            name="check_contribution_type",
        ),
        sa.ForeignKeyConstraint(
            ["source_transaction_id"],
            ["transactions.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "investments",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("user_id", sa.UUID(), nullable=False),
        sa.Column("goal_id", sa.UUID(), nullable=True),
        sa.Column("portfolio_type", sa.String(length=20), nullable=True),
        sa.Column("amount_invested", sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column("projected_return", sa.Numeric(precision=5, scale=2), nullable=True),
        sa.Column("invested_at", sa.DateTime(), nullable=False),
        sa.CheckConstraint(
            "portfolio_type IN ('fixed_income', 'standard', 'growth')",
            name="check_investment_portfolio_type",
        ),
        sa.ForeignKeyConstraint(
            ["goal_id"],
            ["goals.id"],
        ),
        sa.ForeignKeyConstraint(["user_id"], ["users.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("investments")
    op.drop_table("contributions")
    op.drop_table("transactions")
    op.drop_table("mandates")
    op.drop_table("goals")
    op.drop_table("cards")
    op.drop_table("user_settings")
    op.drop_table("users")
    # ### end Alembic commands ###
