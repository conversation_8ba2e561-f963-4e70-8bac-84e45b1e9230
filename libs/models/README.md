# FinTech Models Library

A comprehensive database models library for the FinTech application, featuring PostgreSQL integration, comprehensive testing, and production-ready architecture.

## 🏗️ Architecture

This library provides:

- **SQLAlchemy Models**: User, Card, Transaction, Contribution, Goal, Investment, Mandate
- **Pydantic Schemas**: Request/response validation with Pydantic v2
- **Repository Pattern**: Clean data access layer with sync/async support
- **Service Layer**: Business logic with user authentication and management
- **Database Configuration**: PostgreSQL with async support via asyncpg
- **Security**: Password hashing with bcrypt

## 🧪 Testing

### PostgreSQL Testing (Default)

The library uses **PostgreSQL in Docker** for comprehensive database testing:

```bash
# Run all database tests with PostgreSQL
just test-database

# Check Docker status
just check-docker
```



### Test Suite

**42 tests** covering:

- ✅ **Schema Validation** (16 tests) - Pydantic schema validation
- ✅ **Database Models** (6 tests) - SQLAlchemy model operations
- ✅ **Repository Layer** (6 tests) - Data access with sync/async
- ✅ **Service Layer** (9 tests) - Business logic and authentication
- ✅ **Integration Tests** (5 tests) - End-to-end workflows

## 🚀 Quick Start

### Prerequisites

- **Docker** with **Docker Compose V2** (for PostgreSQL testing)
- **Python 3.12+**
- **uv** package manager
- **just** command runner

### Installation

```bash
# Install dependencies
cd libs/models
uv sync --extra test

# Run tests
just test-database
```

### Usage

```python
from fintech.models import (
    User, UserCreate, UserService,
    get_sync_db, get_async_db
)

# Create a user
user_service = UserService()
user_data = UserCreate(
    email="<EMAIL>",
    password="secure_password",
    first_name="John",
    last_name="Doe"
)

# Sync usage
with get_sync_db() as db:
    user = user_service.create_user(db, user_data)

# Async usage
async with get_async_db() as db:
    user = await user_service.create_user_async(db, user_data)
```

## 🐳 Docker Configuration

### PostgreSQL Test Container

The library uses `docker-compose.test.yml` with Docker Compose V2 for PostgreSQL testing:

```yaml
services:
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d fintech_test"]
      interval: 5s
      timeout: 5s
      retries: 5
```

### Test Configuration

Tests automatically:
- Start PostgreSQL container
- Create database tables
- Run tests with proper isolation
- Clean up containers and volumes

## 🔧 Development Commands

```bash
# Install dependencies
just install-models

# Run linting and formatting
just lint-models

# Run type checking
just typecheck-models

# Run all CI checks
just ci-check-models

# Clean up Docker resources
just clean-docker
```

## 📊 CI/CD Integration

### GitHub Actions

The library includes a comprehensive GitHub Actions workflow (`.github/workflows/test-database.yml`):

- **Multi-Python Testing**: Python 3.12 and 3.13
- **PostgreSQL Integration**: Real database testing in CI
- **Artifact Upload**: Test results and reports

### Workflow Triggers

- Push to `main` or `develop` branches
- Pull requests affecting models library
- Changes to justfile or workflow files

## 🏛️ Database Schema

### Core Models

- **User**: User accounts with authentication
- **UserSettings**: User preferences and configurations
- **Card**: Payment cards linked to users
- **Transaction**: Financial transactions
- **Contribution**: Savings contributions (roundups, etc.)
- **Goal**: Savings goals and targets
- **Investment**: Investment records
- **Mandate**: Legal mandates and agreements

### Relationships

```
User (1) ←→ (1) UserSettings
User (1) ←→ (*) Card
User (1) ←→ (*) Transaction
User (1) ←→ (*) Contribution
User (1) ←→ (*) Goal
User (1) ←→ (*) Investment
User (1) ←→ (*) Mandate

Card (1) ←→ (*) Transaction
Transaction (1) ←→ (0..1) Contribution
Goal (1) ←→ (*) Investment
```

## 🔒 Security Features

- **Password Hashing**: bcrypt with salt
- **Email Validation**: Pydantic email validation
- **Input Sanitization**: Automatic via Pydantic schemas
- **SQL Injection Protection**: SQLAlchemy ORM
- **Type Safety**: Full type hints with mypy

## 📈 Performance

- **Connection Pooling**: SQLAlchemy connection pools
- **Async Support**: asyncpg for high-performance async operations
- **Query Optimization**: Efficient repository patterns
- **Test Isolation**: Fast test execution with proper cleanup

## 🤝 Contributing

1. Make changes to models, schemas, repositories, or services
2. Add corresponding tests
3. Run the test suite: `just test-database`
4. Verify all tests pass: `just test-database`
5. Run linting: `just lint-models`
6. Submit pull request

## 📝 License

This project is part of the FinTech monorepo and follows the same licensing terms.
