version: '3.8'

services:
  # PostgreSQL Database for testing and development
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d fintech_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    tmpfs:
      - /tmp
      - /var/run/postgresql
    networks:
      - fintech-backend-network

  # Local Auth0 Mock for testing
  localauth0:
    image: public.ecr.aws/primaassicurazioni/localauth0:0.8.2
    ports:
      - "3002:3000"  # Use different port for testing to avoid conflict with dev
    environment:
      - AUTH0_AUDIENCE=my-api
      - AUTH0_CLIENT_ID=my-client-id
      - AUTH0_CLIENT_SECRET=my-client-secret
      - AUTH0_DOMAIN=localhost:3002
      - AUTH0_ISSUER=http://localhost:3002
      - AUTH0_BASE_URL=http://localhost:3002
      # Configure test user with required permissions for API access
      - AUTH0_USERS=[{"email":"<EMAIL>","password":"Test1234","user_id":"auth0|123456789","name":"Test User","roles":["user","admin"],"permissions":["read:profile","read:dashboard","read:investments","read:transactions","read:cards","read:goals","write:profile","write:cards","write:goals"]}]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - fintech-backend-network

networks:
  fintech-backend-network:
    driver: bridge

volumes:
  postgres_test_data:
    driver: local
