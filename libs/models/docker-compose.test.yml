version: '3.8'

services:
  postgres-test:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: fintech_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5433:5432"  # Use different port to avoid conflicts
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d fintech_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    tmpfs:
      - /tmp
      - /var/run/postgresql

volumes:
  postgres_test_data:
    driver: local
