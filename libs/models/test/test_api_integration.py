"""Test API integration with database using the same test infrastructure."""

import traceback
from uuid import UUID, uuid4

from fastapi import Depends, FastAPI, HTTPException
from fastapi.testclient import TestClient
from sqlalchemy import text
from sqlalchemy.orm import Session

from fintech.models import get_db
from fintech.models.repositories import user_repository
from fintech.models.users import User

# These are integration tests that require a real PostgreSQL database
# They use the Docker-based test infrastructure defined in conftest.py


class TestAPIIntegration:
    """Test API integration with database."""

    def test_api_user_profile_endpoint(self, db_session):
        """Test that API endpoint can connect to database and fetch user data."""

        # Create a test user
        test_user_id = uuid4()
        test_user = User(
            id=test_user_id,
            email="<EMAIL>",
            password_hash="hashed_password",
            first_name="API",
            last_name="Test",
            country="US",
            currency="USD"
        )

        db_session.add(test_user)
        db_session.commit()
        db_session.refresh(test_user)

        # Create FastAPI app with database endpoint
        app = FastAPI(title="FinTech API Test")

        # Override the database dependency to use the test session
        def get_test_db():
            return db_session

        app.dependency_overrides[get_db] = get_test_db

        @app.get("/health")
        async def health_check():
            return {"status": "healthy", "service": "fintech-api"}

        @app.get("/api/users/profile")
        async def get_user_profile(
            user_id: str,
            db: Session = Depends(get_db),
        ):
            try:
                user_uuid = UUID(user_id)
                user = user_repository.get(db, record_id=user_uuid)

                if not user:
                    raise HTTPException(
                        status_code=404,
                        detail=f"User with ID {user_id} not found"
                    )

                return {
                    "id": str(user.id),
                    "email": user.email,
                    "first_name": user.first_name or "",
                    "last_name": user.last_name or "",
                    "country": user.country or "",
                    "currency": user.currency or "EUR",
                    "welcome_message": f"Welcome back, {user.first_name or 'User'}!"
                }

            except ValueError as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid user ID format: {e}"
                ) from e
            except HTTPException:
                # Re-raise HTTP exceptions (like 404)
                raise
            except Exception as e:
                # Log the actual error for debugging
                traceback.print_exc()
                raise HTTPException(
                    status_code=500,
                    detail=f"Internal server error: {e!s}"
                ) from e

        # Test the API
        client = TestClient(app)

        # Test health endpoint
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"

        # Test user profile endpoint with our test user
        response = client.get(f"/api/users/profile?user_id={test_user_id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(test_user_id)
        assert data["email"] == "<EMAIL>"
        assert data["first_name"] == "API"
        assert data["last_name"] == "Test"
        assert data["country"] == "US"
        assert data["currency"] == "USD"
        assert "Welcome back, API!" in data["welcome_message"]

        # Test with non-existent user
        fake_user_id = uuid4()
        response = client.get(f"/api/users/profile?user_id={fake_user_id}")
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()

        # Test with invalid UUID
        response = client.get("/api/users/profile?user_id=invalid-uuid")
        assert response.status_code == 400
        data = response.json()
        assert "invalid" in data["detail"].lower()

        # Clean up test user
        db_session.delete(test_user)
        db_session.commit()

    def test_api_database_dependency_injection(self, db_session):
        """Test that FastAPI dependency injection works with database sessions."""

        # Create a simple endpoint that uses database dependency
        app = FastAPI()

        # Override the database dependency to use the test session
        def get_test_db():
            return db_session

        app.dependency_overrides[get_db] = get_test_db

        @app.get("/test-db")
        async def test_database_connection(db: Session = Depends(get_db)):
            # Try to execute a simple query
            result = db.execute(text("SELECT 1 as test_value"))
            row = result.fetchone()
            return {"database_connected": True, "test_value": row[0]}

        client = TestClient(app)
        response = client.get("/test-db")

        assert response.status_code == 200
        data = response.json()
        assert data["database_connected"] is True
        assert data["test_value"] == 1

    def test_api_repository_integration(self, db_session):
        """Test that API can use repository pattern with database."""

        # Create a test user
        test_user = User(
            id=uuid4(),
            email="<EMAIL>",
            password_hash="hashed_password",
            first_name="Repository",
            last_name="Test"
        )

        db_session.add(test_user)
        db_session.commit()
        db_session.refresh(test_user)

        # Create API endpoint that uses repository
        app = FastAPI()

        # Override the database dependency to use the test session
        def get_test_db():
            return db_session

        app.dependency_overrides[get_db] = get_test_db

        @app.get("/api/users/count")
        async def get_user_count(db: Session = Depends(get_db)):
            users = user_repository.get_multi(db, limit=1000)  # Get up to 1000 users
            return {"user_count": len(users)}

        @app.get("/api/users/by-email")
        async def get_user_by_email(email: str, db: Session = Depends(get_db)):
            user = user_repository.get_by_email(db, email=email)
            if not user:
                raise HTTPException(status_code=404, detail="User not found")
            return {"id": str(user.id), "email": user.email}

        client = TestClient(app)

        # Test user count endpoint
        response = client.get("/api/users/count")
        assert response.status_code == 200
        data = response.json()
        assert data["user_count"] >= 1  # At least our test user

        # Test get user by email
        response = client.get(f"/api/users/by-email?email={test_user.email}")
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["id"] == str(test_user.id)

        # Test with non-existent email
        response = client.get("/api/users/by-email?email=<EMAIL>")
        assert response.status_code == 404

        # Clean up
        db_session.delete(test_user)
        db_session.commit()
