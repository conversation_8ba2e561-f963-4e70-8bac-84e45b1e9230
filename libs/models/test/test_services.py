"""Test service operations."""

import pytest

from fintech.models import UserCreate, UserService, UserUpdate, verify_password

from .test_utils import unique_email


class TestUserService:
    """Test UserService."""

    def test_create_user(self, db_session):
        """Test creating a user through service."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(
            email=email,
            password="testpassword123",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            country="US",
        )

        user = service.create_user(db_session, user_in=user_data)

        assert user.id is not None
        assert user.email == email
        assert user.first_name == "<PERSON>"
        assert user.last_name == "Doe"
        assert user.country == "US"
        assert user.password_hash is not None
        assert user.password_hash != "testpassword123"  # Should be hashed

        # Check that user settings were created
        assert user.settings is not None
        assert user.settings.enable_roundups is True

    def test_create_user_duplicate_email(self, db_session):
        """Test creating a user with duplicate email."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(email=email, password="testpassword123")

        # Create first user
        service.create_user(db_session, user_in=user_data)

        # Try to create second user with same email
        with pytest.raises(ValueError, match="Email already registered"):
            service.create_user(db_session, user_in=user_data)

    def test_authenticate_user_success(self, db_session):
        """Test successful user authentication."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(email=email, password="testpassword123")

        # Create user
        created_user = service.create_user(db_session, user_in=user_data)

        # Authenticate user
        authenticated_user = service.authenticate_user(
            db_session, email=email, password="testpassword123"
        )

        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        assert authenticated_user.email == email

    def test_authenticate_user_wrong_password(self, db_session):
        """Test user authentication with wrong password."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(email=email, password="testpassword123")

        # Create user
        service.create_user(db_session, user_in=user_data)

        # Try to authenticate with wrong password
        authenticated_user = service.authenticate_user(
            db_session, email=email, password="wrongpassword"
        )

        assert authenticated_user is None

    def test_authenticate_user_nonexistent(self, db_session):
        """Test user authentication with nonexistent email."""
        service = UserService()

        # Try to authenticate nonexistent user
        authenticated_user = service.authenticate_user(
            db_session, email="<EMAIL>", password="testpassword123"
        )

        assert authenticated_user is None

    def test_update_user(self, db_session):
        """Test updating a user."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(
            email=email, password="testpassword123", first_name="John"
        )

        # Create user
        created_user = service.create_user(db_session, user_in=user_data)

        # Update user
        update_data = UserUpdate(first_name="Jane", last_name="Smith", country="CA")

        updated_user = service.update_user(
            db_session, user=created_user, user_in=update_data
        )

        assert updated_user is not None
        assert updated_user.id == created_user.id
        assert updated_user.first_name == "Jane"
        assert updated_user.last_name == "Smith"
        assert updated_user.country == "CA"
        assert updated_user.email == email  # Should remain unchanged

    def test_update_user_password(self, db_session):
        """Test updating user password."""
        service = UserService()

        user_data = UserCreate(email=unique_email(), password="oldpassword123")

        # Create user
        created_user = service.create_user(db_session, user_in=user_data)
        old_password_hash = created_user.password_hash

        # Update password using the dedicated method
        updated_user = service.update_user_password(
            db_session, user=created_user, new_password="newpassword123"
        )

        assert updated_user is not None
        assert updated_user.password_hash != old_password_hash
        assert verify_password("newpassword123", updated_user.password_hash)
        assert not verify_password("oldpassword123", updated_user.password_hash)

    @pytest.mark.asyncio
    async def test_create_user_async(self, async_db_session):
        """Test creating a user asynchronously."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(
            email=email, password="testpassword123", first_name="John", last_name="Doe"
        )

        user = await service.create_user_async(async_db_session, user_in=user_data)

        # Refresh to ensure all attributes are loaded
        await async_db_session.refresh(user)

        assert user.id is not None
        assert user.email == email
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.password_hash is not None

    @pytest.mark.asyncio
    async def test_authenticate_user_async(self, async_db_session):
        """Test user authentication asynchronously."""
        service = UserService()

        email = unique_email()
        user_data = UserCreate(email=email, password="testpassword123")

        # Create user
        created_user = await service.create_user_async(
            async_db_session, user_in=user_data
        )

        # Authenticate user
        authenticated_user = await service.authenticate_user_async(
            async_db_session, email=email, password="testpassword123"
        )

        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id
        assert authenticated_user.email == email
