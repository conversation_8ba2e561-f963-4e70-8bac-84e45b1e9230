"""Test database models."""

from decimal import Decimal

from fintech.models import (
    Card,
    Contribution,
    Goal,
    Transaction,
    User,
    UserSettings,
)

from .test_utils import (
    unique_card_data,
    unique_contribution_data,
    unique_goal_data,
    unique_transaction_data,
    unique_user_data,
)


class TestUserModel:
    """Test User model."""

    def test_create_user(self, db_session):
        """Test creating a user."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        assert user.id is not None
        assert user.email == user_data["email"]
        assert user.first_name == user_data["first_name"]
        assert user.last_name == user_data["last_name"]
        assert user.country == user_data["country"]
        assert user.currency == user_data["currency"]
        assert user.created_at is not None
        assert user.updated_at is not None

    def test_user_relationships(self, db_session):
        """Test user relationships."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        # Test settings relationship
        settings = UserSettings(
            user_id=user.id, enable_roundups=True, percentage_saving=Decimal("5.00")
        )

        db_session.add(settings)
        db_session.commit()

        assert user.settings is not None
        assert user.settings.enable_roundups is True


class TestCardModel:
    """Test Card model."""

    def test_create_card(self, db_session):
        """Test creating a card."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        card_data = unique_card_data(user.id)
        card = Card(**card_data)

        db_session.add(card)
        db_session.commit()
        db_session.refresh(card)

        assert card.id is not None
        assert card.user_id == user.id
        assert card.card_name == card_data["card_name"]
        assert card.card_type == card_data["card_type"]
        assert card.last_four == card_data["last_four"]
        assert card.spending_limit == card_data["spending_limit"]


class TestTransactionModel:
    """Test Transaction model."""

    def test_create_transaction(self, db_session):
        """Test creating a transaction."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        transaction_data = unique_transaction_data(user.id)
        transaction = Transaction(**transaction_data)

        db_session.add(transaction)
        db_session.commit()
        db_session.refresh(transaction)

        assert transaction.id is not None
        assert transaction.user_id == user.id
        assert transaction.amount == transaction_data["amount"]
        assert transaction.merchant_name == transaction_data["merchant_name"]
        assert transaction.category == transaction_data["category"]


class TestContributionModel:
    """Test Contribution model."""

    def test_create_contribution(self, db_session):
        """Test creating a contribution."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        contribution_data = unique_contribution_data(user.id)
        contribution = Contribution(**contribution_data)

        db_session.add(contribution)
        db_session.commit()
        db_session.refresh(contribution)

        assert contribution.id is not None
        assert contribution.user_id == user.id
        assert contribution.type == contribution_data["type"]
        assert contribution.amount == contribution_data["amount"]


class TestGoalModel:
    """Test Goal model."""

    def test_create_goal(self, db_session):
        """Test creating a goal."""
        user_data = unique_user_data()
        user = User(**user_data)

        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)

        goal_data = unique_goal_data(user.id)
        goal = Goal(**goal_data)

        db_session.add(goal)
        db_session.commit()
        db_session.refresh(goal)

        assert goal.id is not None
        assert goal.user_id == user.id
        assert goal.name == goal_data["name"]
        assert goal.target_amount == goal_data["target_amount"]
        assert goal.strategy == goal_data["strategy"]
