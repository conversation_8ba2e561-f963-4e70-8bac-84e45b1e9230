"""Test Pydantic schemas."""

import uuid
from datetime import date
from decimal import Decimal

import pytest
from pydantic import ValidationError

from fintech.models import (
    CardCreate,
    ContributionCreate,
    GoalCreate,
    TransactionCreate,
    UserCreate,
    UserSettingsCreate,
    UserUpdate,
)


class TestUserSchemas:
    """Test User schemas."""

    def test_user_create_valid(self):
        """Test valid user creation schema."""
        user_data = {
            "email": "<EMAIL>",
            "password": "testpassword123",
            "first_name": "<PERSON>",
            "last_name": "Doe",
            "country": "US",
            "currency": "USD",
        }

        user = UserCreate(**user_data)

        assert user.email == "<EMAIL>"
        assert user.password == "testpassword123"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.country == "US"
        assert user.currency == "USD"

    def test_user_create_invalid_email(self):
        """Test user creation with invalid email."""
        user_data = {"email": "invalid-email", "password": "testpassword123"}

        with pytest.raises(ValidationError):
            UserCreate(**user_data)

    def test_user_create_short_password(self):
        """Test user creation with short password."""
        user_data = {"email": "<EMAIL>", "password": "short"}

        with pytest.raises(ValidationError):
            UserCreate(**user_data)

    def test_user_update_partial(self):
        """Test partial user update."""
        update_data = {"first_name": "Jane", "country": "CA"}

        user_update = UserUpdate(**update_data)

        assert user_update.first_name == "Jane"
        assert user_update.country == "CA"
        assert user_update.last_name is None
        assert user_update.currency is None


class TestUserSettingsSchemas:
    """Test UserSettings schemas."""

    def test_user_settings_create_defaults(self):
        """Test user settings creation with defaults."""
        settings = UserSettingsCreate()

        assert settings.enable_roundups is True
        assert settings.enable_percentage_saving is False
        assert settings.enable_recurring is False
        assert settings.roundup_to == 1
        assert settings.percentage_saving == Decimal("5.00")

    def test_user_settings_create_custom(self):
        """Test user settings creation with custom values."""
        settings_data = {
            "enable_roundups": False,
            "enable_percentage_saving": True,
            "percentage_saving": Decimal("10.00"),
            "recurring_amount": Decimal("50.00"),
            "recurring_interval": "weekly",
        }

        settings = UserSettingsCreate(**settings_data)

        assert settings.enable_roundups is False
        assert settings.enable_percentage_saving is True
        assert settings.percentage_saving == Decimal("10.00")
        assert settings.recurring_amount == Decimal("50.00")
        assert settings.recurring_interval == "weekly"

    def test_user_settings_invalid_percentage(self):
        """Test user settings with invalid percentage."""
        settings_data = {
            "percentage_saving": Decimal("150.00")  # Over 100%
        }

        with pytest.raises(ValidationError):
            UserSettingsCreate(**settings_data)

    def test_user_settings_invalid_interval(self):
        """Test user settings with invalid recurring interval."""
        settings_data = {
            "recurring_interval": "daily"  # Not allowed
        }

        with pytest.raises(ValidationError):
            UserSettingsCreate(**settings_data)


class TestCardSchemas:
    """Test Card schemas."""

    def test_card_create_valid(self):
        """Test valid card creation."""
        card_data = {
            "card_name": "Main Card",
            "card_type": "debit",
            "last_four": "1234",
            "spending_limit": Decimal("1000.00"),
        }

        card = CardCreate(**card_data)

        assert card.card_name == "Main Card"
        assert card.card_type == "debit"
        assert card.last_four == "1234"
        assert card.spending_limit == Decimal("1000.00")

    def test_card_create_invalid_type(self):
        """Test card creation with invalid type."""
        card_data = {"card_type": "invalid"}

        with pytest.raises(ValidationError):
            CardCreate(**card_data)

    def test_card_create_invalid_last_four(self):
        """Test card creation with invalid last four digits."""
        card_data = {
            "last_four": "12345"  # Too long
        }

        with pytest.raises(ValidationError):
            CardCreate(**card_data)


class TestTransactionSchemas:
    """Test Transaction schemas."""

    def test_transaction_create_valid(self):
        """Test valid transaction creation."""
        transaction_data = {
            "card_id": uuid.uuid4(),
            "amount": Decimal("25.50"),
            "merchant_name": "Coffee Shop",
            "category": "food",
        }

        transaction = TransactionCreate(**transaction_data)

        assert transaction.amount == Decimal("25.50")
        assert transaction.merchant_name == "Coffee Shop"
        assert transaction.category == "food"


class TestContributionSchemas:
    """Test Contribution schemas."""

    def test_contribution_create_valid(self):
        """Test valid contribution creation."""
        contribution_data = {
            "type": "roundup",
            "amount": Decimal("0.50"),
            "source_transaction_id": uuid.uuid4(),
        }

        contribution = ContributionCreate(**contribution_data)

        assert contribution.type == "roundup"
        assert contribution.amount == Decimal("0.50")

    def test_contribution_create_invalid_type(self):
        """Test contribution creation with invalid type."""
        contribution_data = {"type": "invalid", "amount": Decimal("0.50")}

        with pytest.raises(ValidationError):
            ContributionCreate(**contribution_data)


class TestGoalSchemas:
    """Test Goal schemas."""

    def test_goal_create_valid(self):
        """Test valid goal creation."""
        goal_data = {
            "name": "Vacation Fund",
            "target_amount": Decimal("5000.00"),
            "target_date": date(2024, 12, 31),
            "strategy": "standard",
        }

        goal = GoalCreate(**goal_data)

        assert goal.name == "Vacation Fund"
        assert goal.target_amount == Decimal("5000.00")
        assert goal.target_date == date(2024, 12, 31)
        assert goal.strategy == "standard"

    def test_goal_create_invalid_strategy(self):
        """Test goal creation with invalid strategy."""
        goal_data = {"strategy": "invalid"}

        with pytest.raises(ValidationError):
            GoalCreate(**goal_data)
