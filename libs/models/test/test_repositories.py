"""Test repository operations."""

import uuid
from decimal import Decimal

import pytest

from fintech.models import (
    CardRepository,
    TransactionRepository,
    UserRepository,
)

from .test_utils import unique_user_data


class TestUserRepository:
    """Test UserRepository."""

    def test_create_user(self, db_session):
        """Test creating a user."""
        repo = UserRepository()

        user_data = unique_user_data()

        user = repo.create(db_session, obj_in=user_data)

        assert user.id is not None
        assert user.email == user_data["email"]
        assert user.first_name == user_data["first_name"]
        assert user.last_name == user_data["last_name"]

    def test_get_user_by_email(self, db_session):
        """Test getting user by email."""
        repo = UserRepository()

        user_data = unique_user_data()

        created_user = repo.create(db_session, obj_in=user_data)
        found_user = repo.get_by_email(db_session, email=user_data["email"])

        assert found_user is not None
        assert found_user.id == created_user.id
        assert found_user.email == user_data["email"]

    def test_is_email_taken(self, db_session):
        """Test checking if email is taken."""
        repo = UserRepository()

        user_data = unique_user_data()

        # Email should not be taken initially
        assert not repo.is_email_taken(db_session, email=user_data["email"])

        # Create user
        repo.create(db_session, obj_in=user_data)

        # Email should now be taken
        assert repo.is_email_taken(db_session, email=user_data["email"])

    @pytest.mark.asyncio
    async def test_create_user_async(self, async_db_session):
        """Test creating a user asynchronously."""
        repo = UserRepository()

        user_data = unique_user_data()

        user = await repo.create_async(async_db_session, obj_in=user_data)

        assert user.id is not None
        assert user.email == user_data["email"]
        assert user.first_name == user_data["first_name"]
        assert user.last_name == user_data["last_name"]


class TestCardRepository:
    """Test CardRepository."""

    def test_get_cards_by_user_id(self, db_session):
        """Test getting cards by user ID."""
        user_repo = UserRepository()
        card_repo = CardRepository()

        # Create user with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = {"email": unique_email, "password_hash": "hashed_password"}
        user = user_repo.create(db_session, obj_in=user_data)

        # Create cards
        card1_data = {
            "user_id": user.id,
            "card_name": "Card 1",
            "card_type": "debit",
            "last_four": "1234",
        }
        card2_data = {
            "user_id": user.id,
            "card_name": "Card 2",
            "card_type": "credit",
            "last_four": "5678",
        }

        card_repo.create(db_session, obj_in=card1_data)
        card_repo.create(db_session, obj_in=card2_data)

        # Get cards by user ID
        cards = card_repo.get_by_user_id(db_session, user_id=user.id)

        assert len(cards) == 2
        assert cards[0].user_id == user.id
        assert cards[1].user_id == user.id


class TestTransactionRepository:
    """Test TransactionRepository."""

    def test_get_transactions_by_user_id(self, db_session):
        """Test getting transactions by user ID."""
        user_repo = UserRepository()
        transaction_repo = TransactionRepository()

        # Create user
        user_data = unique_user_data()
        user = user_repo.create(db_session, obj_in=user_data)

        # Create transactions
        transaction1_data = {
            "user_id": user.id,
            "amount": Decimal("25.50"),
            "merchant_name": "Coffee Shop",
        }
        transaction2_data = {
            "user_id": user.id,
            "amount": Decimal("100.00"),
            "merchant_name": "Grocery Store",
        }

        transaction_repo.create(db_session, obj_in=transaction1_data)
        transaction_repo.create(db_session, obj_in=transaction2_data)

        # Get transactions by user ID
        transactions = transaction_repo.get_by_user_id(db_session, user_id=user.id)

        assert len(transactions) == 2
        assert transactions[0].user_id == user.id
        assert transactions[1].user_id == user.id
        # Check that both transactions are present (order may vary due to timing)
        amounts = {t.amount for t in transactions}
        assert amounts == {Decimal("25.50"), Decimal("100.00")}
