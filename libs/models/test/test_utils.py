"""Test utilities for generating unique test data."""

import uuid
from decimal import Decimal


def unique_email() -> str:
    """Generate a unique email for testing."""
    return f"test-{uuid.uuid4().hex[:8]}@example.com"


def unique_user_data():
    """Generate unique user data for testing."""
    return {
        "email": unique_email(),
        "password_hash": "hashed_password",
        "first_name": "Test",
        "last_name": "User",
        "country": "US",
        "currency": "USD",
    }


def unique_card_data(user_id):
    """Generate unique card data for testing."""
    return {
        "user_id": user_id,
        "card_name": f"Test Card {uuid.uuid4().hex[:8]}",
        "card_type": "debit",
        "last_four": "1234",
        "spending_limit": Decimal("1000.00"),
    }


def unique_transaction_data(user_id, card_id=None):
    """Generate unique transaction data for testing."""
    return {
        "user_id": user_id,
        "card_id": card_id,
        "amount": Decimal("25.50"),
        "merchant_name": f"Test Merchant {uuid.uuid4().hex[:8]}",
        "category": "food",
    }


def unique_contribution_data(user_id, transaction_id=None):
    """Generate unique contribution data for testing."""
    return {
        "user_id": user_id,
        "source_transaction_id": transaction_id,
        "type": "roundup",
        "amount": Decimal("0.50"),
    }


def unique_goal_data(user_id):
    """Generate unique goal data for testing."""
    return {
        "user_id": user_id,
        "name": f"Test Goal {uuid.uuid4().hex[:8]}",
        "target_amount": Decimal("1000.00"),
        "strategy": "standard",
    }
