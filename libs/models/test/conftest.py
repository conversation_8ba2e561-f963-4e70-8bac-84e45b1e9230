"""Test configuration and fixtures."""

import asyncio
import subprocess
import time
from pathlib import Path

import pytest
import pytest_asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker

from fintech.models import Base


def pytest_configure(config):
    """Configure pytest to use PostgreSQL by default."""


# PostgreSQL fixtures (requires Docker)
@pytest.fixture(scope="session")
def postgres_container():
    """Start PostgreSQL container using Docker Compose V2."""

    # Get the directory containing this conftest.py file
    test_dir = Path(__file__).resolve().parent
    models_dir = test_dir.parent
    compose_file = models_dir / "docker-compose.test.yml"

    if not compose_file.exists():
        pytest.fail(f"docker-compose.test.yml not found at {compose_file}")

    try:
        # Start PostgreSQL container
        # Starting PostgreSQL container
        subprocess.run(
            ["docker", "compose", "-f", str(compose_file), "up", "-d", "--wait"],
            check=True,
            cwd=str(models_dir),
        )

        # Wait a bit more for the database to be ready
        time.sleep(2)

        # Return connection info
        container_info = {
            "host": "localhost",
            "port": 5433,
            "database": "fintech_test",
            "username": "test_user",
            "password": "test_password",
        }

        yield container_info

    except subprocess.CalledProcessError as e:
        pytest.fail(f"Failed to start PostgreSQL container: {e}")
    except (OSError, FileNotFoundError) as e:
        pytest.fail(f"Docker not available: {e}. Make sure Docker is running.")
    finally:
        # Clean up
        try:
            # Stopping PostgreSQL container
            subprocess.run(
                ["docker", "compose", "-f", str(compose_file), "down", "-v"],
                check=False,
                cwd=str(models_dir),
            )
        except (subprocess.CalledProcessError, OSError, FileNotFoundError):
            # Ignore cleanup errors
            pass


@pytest.fixture(scope="session")
def postgres_engine(postgres_container):
    """Create a PostgreSQL test database engine."""
    info = postgres_container
    database_url = f"postgresql://{info['username']}:{info['password']}@{info['host']}:{info['port']}/{info['database']}"
    engine = create_engine(database_url, echo=False)

    # Wait for database to be ready and create tables
    max_retries = 30
    for i in range(max_retries):
        try:
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            break
        except (OSError, ConnectionError) as e:
            if i == max_retries - 1:
                pytest.fail(
                    f"Could not connect to PostgreSQL after {max_retries} retries: {e}"
                )
            time.sleep(1)

    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="session")
def async_postgres_engine(postgres_container):
    """Create an async PostgreSQL test database engine."""
    info = postgres_container
    database_url = f"postgresql+asyncpg://{info['username']}:{info['password']}@{info['host']}:{info['port']}/{info['database']}"
    return create_async_engine(database_url, echo=False)


@pytest.fixture
def postgres_session(postgres_engine):
    """Create a PostgreSQL session for testing."""
    session_local = sessionmaker(
        autocommit=False, autoflush=False, bind=postgres_engine
    )
    session = session_local()
    try:
        yield session
        session.rollback()  # Rollback any changes for test isolation
    finally:
        session.close()


@pytest_asyncio.fixture
async def async_postgres_session(async_postgres_engine):
    """Create an async PostgreSQL session for testing."""
    # Create tables for async engine if not exists
    async with async_postgres_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async with AsyncSession(async_postgres_engine) as session:
        try:
            yield session
            await session.rollback()  # Rollback any changes for test isolation
        finally:
            await session.close()


# Default fixtures that use PostgreSQL
@pytest.fixture
def db_session(postgres_session):
    """Create a database session using PostgreSQL."""
    return postgres_session


@pytest_asyncio.fixture
async def async_db_session(postgres_container):
    """Create an async database session using PostgreSQL."""
    # Use PostgreSQL async session - create a new engine for each test to avoid conflicts
    info = postgres_container
    database_url = f"postgresql+asyncpg://{info['username']}:{info['password']}@{info['host']}:{info['port']}/{info['database']}"
    async_engine = create_async_engine(database_url, echo=False, pool_pre_ping=True)

    # Create tables if needed
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    async with AsyncSession(async_engine) as session:
        try:
            yield session
            await session.rollback()
        finally:
            await session.close()
            await async_engine.dispose()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
