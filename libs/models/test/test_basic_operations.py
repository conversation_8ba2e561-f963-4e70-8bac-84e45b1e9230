"""Test basic database operations with PostgreSQL."""

import uuid
from decimal import Decimal

from fintech.models import (
    CardRepository,
    TransactionRepository,
    UserCreate,
    UserRepository,
    UserService,
)


class TestBasicDatabaseOperations:
    """Test basic database operations."""

    def test_create_and_fetch_user(self, db_session):
        """Test creating and fetching a user."""
        user_service = UserService()

        # Create user with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = UserCreate(
            email=unique_email,
            password="password123",
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            country="US",
        )

        created_user = user_service.create_user(db_session, user_in=user_data)

        # Verify user was created
        assert created_user.id is not None
        assert created_user.email == unique_email
        assert created_user.first_name == "John"
        assert created_user.last_name == "Doe"
        assert created_user.country == "US"

        # Fetch user by ID
        user_repo = UserRepository()
        fetched_user = user_repo.get(db_session, record_id=created_user.id)

        assert fetched_user is not None
        assert fetched_user.id == created_user.id
        assert fetched_user.email == unique_email

        # Fetch user by email
        user_by_email = user_repo.get_by_email(db_session, email=unique_email)
        assert user_by_email is not None
        assert user_by_email.id == created_user.id

    def test_create_and_fetch_card(self, db_session):
        """Test creating and fetching a card."""
        user_service = UserService()
        card_repo = CardRepository()

        # Create user first with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = UserCreate(email=unique_email, password="password123")
        user = user_service.create_user(db_session, user_in=user_data)

        # Create card
        card_data = {
            "user_id": user.id,
            "card_name": "Test Card",
            "card_type": "debit",
            "last_four": "1234",
            "spending_limit": Decimal("1000.00"),
        }

        created_card = card_repo.create(db_session, obj_in=card_data)

        # Verify card was created
        assert created_card.id is not None
        assert created_card.user_id == user.id
        assert created_card.card_name == "Test Card"
        assert created_card.card_type == "debit"
        assert created_card.last_four == "1234"
        assert created_card.spending_limit == Decimal("1000.00")

        # Fetch card by ID
        fetched_card = card_repo.get(db_session, record_id=created_card.id)
        assert fetched_card is not None
        assert fetched_card.id == created_card.id

        # Fetch cards by user ID
        user_cards = card_repo.get_by_user_id(db_session, user_id=user.id)
        assert len(user_cards) == 1
        assert user_cards[0].id == created_card.id

    def test_create_and_fetch_transaction(self, db_session):
        """Test creating and fetching a transaction."""
        user_service = UserService()
        card_repo = CardRepository()
        transaction_repo = TransactionRepository()

        # Create user and card first with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = UserCreate(email=unique_email, password="password123")
        user = user_service.create_user(db_session, user_in=user_data)

        card_data = {
            "user_id": user.id,
            "card_name": "Test Card",
            "card_type": "debit",
            "last_four": "1234",
        }
        card = card_repo.create(db_session, obj_in=card_data)

        # Create transaction
        transaction_data = {
            "user_id": user.id,
            "card_id": card.id,
            "amount": Decimal("25.50"),
            "merchant_name": "Coffee Shop",
            "category": "food",
        }

        created_transaction = transaction_repo.create(
            db_session, obj_in=transaction_data
        )

        # Verify transaction was created
        assert created_transaction.id is not None
        assert created_transaction.user_id == user.id
        assert created_transaction.card_id == card.id
        assert created_transaction.amount == Decimal("25.50")
        assert created_transaction.merchant_name == "Coffee Shop"
        assert created_transaction.category == "food"

        # Fetch transaction by ID
        fetched_transaction = transaction_repo.get(
            db_session, record_id=created_transaction.id
        )
        assert fetched_transaction is not None
        assert fetched_transaction.id == created_transaction.id

        # Fetch transactions by user ID
        user_transactions = transaction_repo.get_by_user_id(db_session, user_id=user.id)
        assert len(user_transactions) == 1
        assert user_transactions[0].id == created_transaction.id

    def test_user_authentication(self, db_session):
        """Test user authentication."""
        user_service = UserService()

        # Create user with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = UserCreate(email=unique_email, password="password123")
        created_user = user_service.create_user(db_session, user_in=user_data)

        # Test successful authentication
        authenticated_user = user_service.authenticate_user(
            db_session, email=unique_email, password="password123"
        )
        assert authenticated_user is not None
        assert authenticated_user.id == created_user.id

        # Test failed authentication with wrong password
        failed_auth = user_service.authenticate_user(
            db_session, email=unique_email, password="wrongpassword"
        )
        assert failed_auth is None

        # Test failed authentication with wrong email
        failed_auth_email = user_service.authenticate_user(
            db_session, email="<EMAIL>", password="password123"
        )
        assert failed_auth_email is None

    def test_user_relationships(self, db_session):
        """Test user relationships with other entities."""
        user_service = UserService()
        card_repo = CardRepository()

        # Create user with unique email
        unique_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
        user_data = UserCreate(email=unique_email, password="password123")
        user = user_service.create_user(db_session, user_in=user_data)

        # Create multiple cards
        for i in range(3):
            card_data = {
                "user_id": user.id,
                "card_name": f"Card {i + 1}",
                "card_type": "debit",
                "last_four": f"123{i}",
            }
            card_repo.create(db_session, obj_in=card_data)

        # Fetch user with relationships
        user_repo = UserRepository()
        fetched_user = user_repo.get(db_session, record_id=user.id)

        # Check that user settings were created automatically
        assert fetched_user.settings is not None
        assert fetched_user.settings.enable_roundups is True

        # Check that cards are accessible through relationship
        assert len(fetched_user.cards) == 3
        assert all(card.user_id == user.id for card in fetched_user.cards)
