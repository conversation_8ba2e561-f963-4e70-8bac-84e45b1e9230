"""Contribution model."""

from datetime import datetime
from decimal import Decimal

from sqlalchemy import CheckConstraint, ForeignKey, Numeric, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, UUIDMixin


class Contribution(Base, UUIDMixin):
    """Contribution model for storing savings contributions."""

    __tablename__ = "contributions"

    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    source_transaction_id: Mapped[UUID | None] = mapped_column(
        ForeignKey("transactions.id")
    )

    # Contribution fields
    type: Mapped[str] = mapped_column(String(20), nullable=False)
    amount: Mapped[Decimal] = mapped_column(Numeric(10, 2), nullable=False)
    created_at: Mapped[datetime] = mapped_column(default=func.now(), nullable=False)

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "type IN ('roundup', 'percentage', 'recurring')",
            name="check_contribution_type",
        ),
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="contributions")
    source_transaction: Mapped["Transaction"] = relationship(
        "Transaction", back_populates="contributions"
    )
