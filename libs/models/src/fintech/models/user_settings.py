"""User settings model."""

from datetime import datetime
from decimal import Decimal

from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Integer, Numeric, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class UserSettings(Base):
    """User settings model for storing user preferences."""

    __tablename__ = "user_settings"

    # Primary key is user_id (one-to-one relationship)
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), primary_key=True
    )

    # Settings fields
    enable_roundups: Mapped[bool] = mapped_column(<PERSON>olean, default=True, nullable=False)
    enable_percentage_saving: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    enable_recurring: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )
    roundup_to: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    percentage_saving: Mapped[Decimal] = mapped_column(
        Numeric(5, 2), default=Decimal("5.00"), nullable=False
    )
    recurring_amount: Mapped[Decimal | None] = mapped_column(Numeric(10, 2))
    recurring_interval: Mapped[str | None] = mapped_column(String(20))

    # Timestamp
    last_updated: Mapped[datetime] = mapped_column(
        default=func.now(), onupdate=func.now(), nullable=False
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="settings")
