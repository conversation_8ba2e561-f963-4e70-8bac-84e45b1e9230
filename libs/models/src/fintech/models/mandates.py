"""Mandate model."""

from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, UUIDMixin


class Mandate(Base, UUIDMixin):
    """Mandate model for storing payment mandates."""

    __tablename__ = "mandates"

    # Foreign key
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    # Mandate fields
    provider: Mapped[str | None] = mapped_column(Text)
    mandate_reference: Mapped[str | None] = mapped_column(String(100), unique=True)
    signed_at: Mapped[datetime | None] = mapped_column()
    valid: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="mandates")
