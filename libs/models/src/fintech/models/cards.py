"""Card model."""

from decimal import Decimal

from sqlalchemy import Foreign<PERSON>ey, Numeric, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TimestampMixin, UUIDMixin


class Card(Base, UUIDMixin, TimestampMixin):
    """Card model for storing user payment cards."""

    __tablename__ = "cards"

    # Foreign key
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    # Card fields
    card_name: Mapped[str | None] = mapped_column(Text)
    card_type: Mapped[str | None] = mapped_column(String(10))  # debit, credit
    last_four: Mapped[str | None] = mapped_column(String(4))
    spending_limit: Mapped[Decimal | None] = mapped_column(Numeric(10, 2))

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="cards")
    transactions: Mapped[list["Transaction"]] = relationship(
        "Transaction", back_populates="card"
    )
