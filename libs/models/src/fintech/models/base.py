"""Base model class for SQLAlchemy models."""

import uuid
from datetime import datetime
from typing import ClassVar

from sqlalchemy import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    """Base class for all database models."""

    type_annotation_map: ClassVar = {
        uuid.UUID: UUID(as_uuid=True),
    }


class TimestampMixin:
    """Mixin for models that need created_at and updated_at timestamps."""

    created_at: Mapped[datetime] = mapped_column(
        "created_at", default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        "updated_at", default=func.now(), onupdate=func.now(), nullable=False
    )


class UUIDMixin:
    """Mixin for models that use UUID as primary key."""

    id: Mapped[uuid.UUID] = mapped_column(
        "id", primary_key=True, default=uuid.uuid4, nullable=False
    )
