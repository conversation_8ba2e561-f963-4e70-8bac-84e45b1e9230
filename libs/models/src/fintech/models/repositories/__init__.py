"""Repository layer for data access."""

from .base import BaseRepository
from .cards import CardRepository, card_repository
from .contributions import ContributionRepository, contribution_repository
from .goals import GoalRepository, goal_repository
from .investments import InvestmentRepository, investment_repository
from .mandates import MandateRepository, mandate_repository
from .transactions import TransactionRepository, transaction_repository
from .user_settings import UserSettingsRepository, user_settings_repository
from .users import UserRepository, user_repository

__all__ = [
    "BaseRepository",
    "UserRepository",
    "UserSettingsRepository",
    "CardRepository",
    "TransactionRepository",
    "ContributionRepository",
    "GoalRepository",
    "InvestmentRepository",
    "MandateRepository",
    "user_repository",
    "user_settings_repository",
    "card_repository",
    "transaction_repository",
    "contribution_repository",
    "goal_repository",
    "investment_repository",
    "mandate_repository",
]
