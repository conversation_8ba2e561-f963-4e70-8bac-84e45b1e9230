"""Mandate repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.mandates import Mandate

from .base import BaseRepository


class MandateRepository(BaseRepository[Mandate]):
    """Repository for Mandate model."""

    def __init__(self):
        super().__init__(Mandate)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Mandate]:
        """Get mandates by user ID."""
        return cast(
            "list[Mandate]", db.query(Mandate).filter(Mandate.user_id == user_id).all()
        )

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Mandate]:
        """Get mandates by user ID (async)."""
        result = await db.execute(select(Mandate).where(Mandate.user_id == user_id))
        return cast("list[Mandate]", list(result.scalars().all()))


# Global repository instance
mandate_repository = MandateRepository()
