"""User repository."""

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.users import User

from .base import BaseRepository


class UserRepository(BaseRepository[User]):
    """Repository for User model."""

    def __init__(self):
        super().__init__(User)

    def get_by_email(self, db: Session, *, email: str) -> User | None:
        """Get user by email."""
        return db.query(User).filter(User.email == email).first()

    async def get_by_email_async(self, db: AsyncSession, *, email: str) -> User | None:
        """Get user by email (async)."""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()

    def is_email_taken(self, db: Session, *, email: str) -> bool:
        """Check if email is already taken."""
        return db.query(User).filter(User.email == email).first() is not None

    async def is_email_taken_async(self, db: AsyncSession, *, email: str) -> bool:
        """Check if email is already taken (async)."""
        result = await db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none() is not None


# Global repository instance
user_repository = UserRepository()
