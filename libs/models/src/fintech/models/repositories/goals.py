"""Goal repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.goals import Goal

from .base import BaseRepository


class GoalRepository(BaseRepository[Goal]):
    """Repository for Goal model."""

    def __init__(self):
        super().__init__(Goal)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Goal]:
        """Get goals by user ID."""
        return cast("list[Goal]", db.query(Goal).filter(Goal.user_id == user_id).all())

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Goal]:
        """Get goals by user ID (async)."""
        result = await db.execute(select(Goal).where(Goal.user_id == user_id))
        return cast("list[Goal]", list(result.scalars().all()))


# Global repository instance
goal_repository = GoalRepository()
