"""User settings repository."""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.user_settings import UserSettings

from .base import BaseRepository


class UserSettingsRepository(BaseRepository[UserSettings]):
    """Repository for UserSettings model."""

    def __init__(self):
        super().__init__(UserSettings)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> UserSettings | None:
        """Get user settings by user ID."""
        return db.query(UserSettings).filter(UserSettings.user_id == user_id).first()

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> UserSettings | None:
        """Get user settings by user ID (async)."""
        result = await db.execute(
            select(UserSettings).where(UserSettings.user_id == user_id)
        )
        return result.scalar_one_or_none()


# Global repository instance
user_settings_repository = UserSettingsRepository()
