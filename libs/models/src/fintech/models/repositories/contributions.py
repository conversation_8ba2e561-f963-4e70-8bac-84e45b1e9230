"""Contribution repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.contributions import Contribution

from .base import BaseRepository


class ContributionRepository(BaseRepository[Contribution]):
    """Repository for Contribution model."""

    def __init__(self):
        super().__init__(Contribution)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Contribution]:
        """Get contributions by user ID."""
        return cast(
            "list[Contribution]",
            db.query(Contribution).filter(Contribution.user_id == user_id).all(),
        )

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Contribution]:
        """Get contributions by user ID (async)."""
        result = await db.execute(
            select(Contribution).where(Contribution.user_id == user_id)
        )
        return cast("list[Contribution]", list(result.scalars().all()))


# Global repository instance
contribution_repository = ContributionRepository()
