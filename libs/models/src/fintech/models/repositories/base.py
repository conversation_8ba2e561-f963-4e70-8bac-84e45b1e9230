"""Base repository class."""

from typing import Any, Generic, TypeVar, cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.base import Base

ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    """Base repository class with common CRUD operations."""

    def __init__(self, model: type[ModelType]):
        """Initialize repository with model class."""
        self.model = model

    def get(self, db: Session, record_id: UUID) -> ModelType | None:
        """Get a single record by ID."""
        return cast(
            "ModelType | None",
            db.query(self.model).filter(self.model.id == record_id).first(),
        )

    async def get_async(self, db: AsyncSession, record_id: UUID) -> ModelType | None:
        """Get a single record by ID (async)."""
        result = await db.execute(select(self.model).where(self.model.id == record_id))
        return cast("ModelType | None", result.scalar_one_or_none())

    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> list[ModelType]:
        """Get multiple records with pagination."""
        return cast(
            "list[ModelType]", db.query(self.model).offset(skip).limit(limit).all()
        )

    async def get_multi_async(
        self, db: AsyncSession, *, skip: int = 0, limit: int = 100
    ) -> list[ModelType]:
        """Get multiple records with pagination (async)."""
        result = await db.execute(select(self.model).offset(skip).limit(limit))
        return cast("list[ModelType]", list(result.scalars().all()))

    def create(self, db: Session, *, obj_in: dict[str, Any] | ModelType) -> ModelType:
        """Create a new record."""
        if isinstance(obj_in, dict):
            db_obj = cast("ModelType", self.model(**obj_in))
        else:
            db_obj = obj_in

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def create_async(
        self, db: AsyncSession, *, obj_in: dict[str, Any] | ModelType
    ) -> ModelType:
        """Create a new record (async)."""
        if isinstance(obj_in, dict):
            db_obj = cast("ModelType", self.model(**obj_in))
        else:
            db_obj = obj_in

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: dict[str, Any] | ModelType,
    ) -> ModelType:
        """Update an existing record."""
        if isinstance(obj_in, dict):
            update_data = obj_in
        # Handle Pydantic models with model_dump method
        elif hasattr(obj_in, "model_dump"):
            update_data = obj_in.model_dump(exclude_unset=True)
        else:
            # Handle SQLAlchemy models - convert to dict
            update_data = {
                c.name: getattr(obj_in, c.name) for c in obj_in.__table__.columns
            }

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    async def update_async(
        self,
        db: AsyncSession,
        *,
        db_obj: ModelType,
        obj_in: dict[str, Any] | ModelType,
    ) -> ModelType:
        """Update an existing record (async)."""
        if isinstance(obj_in, dict):
            update_data = obj_in
        # Handle Pydantic models with model_dump method
        elif hasattr(obj_in, "model_dump"):
            update_data = obj_in.model_dump(exclude_unset=True)
        else:
            # Handle SQLAlchemy models - convert to dict
            update_data = {
                c.name: getattr(obj_in, c.name) for c in obj_in.__table__.columns
            }

        for field, value in update_data.items():
            setattr(db_obj, field, value)

        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    def remove(self, db: Session, *, record_id: UUID) -> ModelType:
        """Remove a record by ID."""
        obj = cast("ModelType", db.query(self.model).get(record_id))
        if obj is None:
            msg = f"Record with id {record_id} not found"
            raise ValueError(msg)
        db.delete(obj)
        db.commit()
        return obj

    async def remove_async(self, db: AsyncSession, *, record_id: UUID) -> ModelType:
        """Remove a record by ID (async)."""
        result = await db.execute(select(self.model).where(self.model.id == record_id))
        obj = cast("ModelType", result.scalar_one())
        await db.delete(obj)
        await db.commit()
        return obj
