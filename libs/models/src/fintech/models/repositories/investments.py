"""Investment repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.investments import Investment

from .base import BaseRepository


class InvestmentRepository(BaseRepository[Investment]):
    """Repository for Investment model."""

    def __init__(self):
        super().__init__(Investment)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Investment]:
        """Get investments by user ID."""
        return cast(
            "list[Investment]",
            db.query(Investment).filter(Investment.user_id == user_id).all(),
        )

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Investment]:
        """Get investments by user ID (async)."""
        result = await db.execute(
            select(Investment).where(Investment.user_id == user_id)
        )
        return cast("list[Investment]", list(result.scalars().all()))


# Global repository instance
investment_repository = InvestmentRepository()
