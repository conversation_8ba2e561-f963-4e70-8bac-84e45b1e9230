"""Card repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.cards import Card

from .base import BaseRepository


class CardRepository(BaseRepository[Card]):
    """Repository for Card model."""

    def __init__(self):
        super().__init__(Card)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Card]:
        """Get cards by user ID."""
        return cast("list[Card]", db.query(Card).filter(Card.user_id == user_id).all())

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Card]:
        """Get cards by user ID (async)."""
        result = await db.execute(select(Card).where(Card.user_id == user_id))
        return cast("list[Card]", list(result.scalars().all()))


# Global repository instance
card_repository = CardRepository()
