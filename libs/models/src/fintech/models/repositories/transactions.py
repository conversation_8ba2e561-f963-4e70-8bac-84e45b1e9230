"""Transaction repository."""

from typing import cast
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.transactions import Transaction

from .base import BaseRepository


class TransactionRepository(BaseRepository[Transaction]):
    """Repository for Transaction model."""

    def __init__(self):
        super().__init__(Transaction)

    def get_by_user_id(self, db: Session, *, user_id: UUID) -> list[Transaction]:
        """Get transactions by user ID."""
        return cast(
            "list[Transaction]",
            db.query(Transaction).filter(Transaction.user_id == user_id).all(),
        )

    async def get_by_user_id_async(
        self, db: AsyncSession, *, user_id: UUID
    ) -> list[Transaction]:
        """Get transactions by user ID (async)."""
        result = await db.execute(
            select(Transaction).where(Transaction.user_id == user_id)
        )
        return cast("list[Transaction]", list(result.scalars().all()))


# Global repository instance
transaction_repository = TransactionRepository()
