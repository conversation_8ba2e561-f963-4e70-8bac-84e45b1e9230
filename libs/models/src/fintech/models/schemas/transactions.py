"""Transaction schemas."""

from datetime import datetime
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel


class TransactionBase(BaseModel):
    """Base transaction schema."""

    amount: Decimal | None = None
    merchant_name: str | None = None
    category: str | None = None


class TransactionCreate(TransactionBase):
    """Schema for creating a transaction."""

    card_id: UUID | None = None


class TransactionResponse(TransactionBase):
    """Schema for transaction response."""

    id: UUID
    user_id: UUID
    card_id: UUID | None = None
    timestamp: datetime

    model_config = {"from_attributes": True}
