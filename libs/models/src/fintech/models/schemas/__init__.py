"""Pydantic schemas for data validation."""

from .cards import CardCreate, CardResponse, CardUpdate
from .contributions import ContributionCreate, ContributionResponse
from .goals import GoalCreate, GoalResponse, GoalUpdate
from .investments import InvestmentCreate, InvestmentResponse
from .mandates import MandateCreate, MandateResponse, MandateUpdate
from .transactions import TransactionCreate, TransactionResponse
from .user_settings import UserSettingsCreate, UserSettingsResponse, UserSettingsUpdate
from .users import UserCreate, UserLogin, UserResponse, UserUpdate

__all__ = [
    # Users
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    # User Settings
    "UserSettingsCreate",
    "UserSettingsUpdate",
    "UserSettingsResponse",
    # Cards
    "CardCreate",
    "CardUpdate",
    "CardResponse",
    # Transactions
    "TransactionCreate",
    "TransactionResponse",
    # Contributions
    "ContributionCreate",
    "ContributionResponse",
    # Goals
    "GoalCreate",
    "GoalUpdate",
    "GoalResponse",
    # Investments
    "InvestmentCreate",
    "InvestmentResponse",
    # Mandates
    "MandateCreate",
    "MandateUpdate",
    "MandateResponse",
]
