"""User schemas for data validation."""

from uuid import UUID

from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """Base user schema."""

    email: EmailStr
    first_name: str | None = None
    last_name: str | None = None
    country: str | None = None
    currency: str = Field(default="EUR", max_length=3)


class UserCreate(UserBase):
    """Schema for creating a user."""

    password: str = Field(min_length=8, max_length=100)


class UserUpdate(BaseModel):
    """Schema for updating a user."""

    first_name: str | None = None
    last_name: str | None = None
    country: str | None = None
    currency: str | None = Field(None, max_length=3)


class UserLogin(BaseModel):
    """Schema for user login."""

    email: EmailStr
    password: str


class UserResponse(UserBase):
    """Schema for user response."""

    id: UUID

    model_config = {"from_attributes": True}
