"""User settings schemas."""

from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field


class UserSettingsBase(BaseModel):
    """Base user settings schema."""

    enable_roundups: bool = True
    enable_percentage_saving: bool = False
    enable_recurring: bool = False
    roundup_to: int = Field(default=1, ge=1, le=10)
    percentage_saving: Decimal = Field(default=Decimal("5.00"), ge=0, le=100)
    recurring_amount: Decimal | None = Field(None, ge=0)
    recurring_interval: str | None = Field(None, pattern="^(weekly|monthly|quarterly)$")


class UserSettingsCreate(UserSettingsBase):
    """Schema for creating user settings."""


class UserSettingsUpdate(BaseModel):
    """Schema for updating user settings."""

    enable_roundups: bool | None = None
    enable_percentage_saving: bool | None = None
    enable_recurring: bool | None = None
    roundup_to: int | None = Field(None, ge=1, le=10)
    percentage_saving: Decimal | None = Field(None, ge=0, le=100)
    recurring_amount: Decimal | None = Field(None, ge=0)
    recurring_interval: str | None = Field(None, pattern="^(weekly|monthly|quarterly)$")


class UserSettingsResponse(UserSettingsBase):
    """Schema for user settings response."""

    user_id: UUID

    model_config = {"from_attributes": True}
