"""Goal schemas."""

from datetime import date
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field


class GoalBase(BaseModel):
    """Base goal schema."""

    name: str | None = None
    target_amount: Decimal | None = Field(None, ge=0)
    target_date: date | None = None
    strategy: str | None = Field(None, pattern="^(fixed_income|standard|growth)$")


class GoalCreate(GoalBase):
    """Schema for creating a goal."""


class GoalUpdate(BaseModel):
    """Schema for updating a goal."""

    name: str | None = None
    target_amount: Decimal | None = Field(None, ge=0)
    target_date: date | None = None
    strategy: str | None = Field(None, pattern="^(fixed_income|standard|growth)$")


class GoalResponse(GoalBase):
    """Schema for goal response."""

    id: UUID
    user_id: UUID

    model_config = {"from_attributes": True}
