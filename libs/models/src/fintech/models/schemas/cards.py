"""Card schemas."""

from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field


class CardBase(BaseModel):
    """Base card schema."""

    card_name: str | None = None
    card_type: str | None = Field(None, pattern="^(debit|credit)$")
    last_four: str | None = Field(None, min_length=4, max_length=4)
    spending_limit: Decimal | None = Field(None, ge=0)


class CardCreate(CardBase):
    """Schema for creating a card."""


class CardUpdate(BaseModel):
    """Schema for updating a card."""

    card_name: str | None = None
    card_type: str | None = Field(None, pattern="^(debit|credit)$")
    spending_limit: Decimal | None = Field(None, ge=0)


class CardResponse(CardBase):
    """Schema for card response."""

    id: UUID
    user_id: UUID

    model_config = {"from_attributes": True}
