"""Mandate schemas."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel


class MandateBase(BaseModel):
    """Base mandate schema."""

    provider: str | None = None
    mandate_reference: str | None = None
    valid: bool = True


class MandateCreate(MandateBase):
    """Schema for creating a mandate."""


class MandateUpdate(BaseModel):
    """Schema for updating a mandate."""

    provider: str | None = None
    valid: bool | None = None


class MandateResponse(MandateBase):
    """Schema for mandate response."""

    id: UUID
    user_id: UUID
    signed_at: datetime | None = None

    model_config = {"from_attributes": True}
