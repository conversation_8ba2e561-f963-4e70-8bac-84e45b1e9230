"""Investment schemas."""

from datetime import datetime
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field


class InvestmentBase(BaseModel):
    """Base investment schema."""

    portfolio_type: str | None = Field(None, pattern="^(fixed_income|standard|growth)$")
    amount_invested: Decimal | None = Field(None, ge=0)
    projected_return: Decimal | None = Field(None, ge=0)


class InvestmentCreate(InvestmentBase):
    """Schema for creating an investment."""

    goal_id: UUID | None = None


class InvestmentResponse(InvestmentBase):
    """Schema for investment response."""

    id: UUID
    user_id: UUID
    goal_id: UUID | None = None
    invested_at: datetime

    model_config = {"from_attributes": True}
