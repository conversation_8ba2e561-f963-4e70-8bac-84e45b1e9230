"""Contribution schemas."""

from datetime import datetime
from decimal import Decimal
from uuid import UUID

from pydantic import BaseModel, Field


class ContributionBase(BaseModel):
    """Base contribution schema."""

    type: str = Field(pattern="^(roundup|percentage|recurring)$")
    amount: Decimal = Field(ge=0)


class ContributionCreate(ContributionBase):
    """Schema for creating a contribution."""

    source_transaction_id: UUID | None = None


class ContributionResponse(ContributionBase):
    """Schema for contribution response."""

    id: UUID
    user_id: UUID
    source_transaction_id: UUID | None = None
    created_at: datetime

    model_config = {"from_attributes": True}
