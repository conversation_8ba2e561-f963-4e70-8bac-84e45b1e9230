"""FinTech Models - Database models, schemas, repositories, and services."""

from .base import Base
from .cards import Card
from .config import DatabaseSettings, settings
from .contributions import Contribution
from .database import async_engine, engine, get_async_db, get_db
from .goals import Goal
from .investments import Investment
from .mandates import Mandate
from .repositories import *
from .schemas import *
from .services import *
from .transactions import Transaction
from .user_settings import UserSettings
from .users import User
from .utils.security import hash_password, verify_password

__all__ = [
    # Config
    "DatabaseSettings",
    "settings",
    # Database
    "Base",
    "engine",
    "async_engine",
    "get_db",
    "get_async_db",
    # Models
    "User",
    "UserSettings",
    "Card",
    "Transaction",
    "Contribution",
    "Goal",
    "Investment",
    "Mandate",
    # Repositories
    "UserRepository",
    "UserSettingsRepository",
    "CardRepository",
    "TransactionRepository",
    "ContributionRepository",
    "GoalRepository",
    "InvestmentRepository",
    "MandateRepository",
    "user_repository",
    "user_settings_repository",
    "card_repository",
    "transaction_repository",
    "contribution_repository",
    "goal_repository",
    "investment_repository",
    "mandate_repository",
    # Schemas
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "UserSettingsCreate",
    "UserSettingsUpdate",
    "UserSettingsResponse",
    "CardCreate",
    "CardUpdate",
    "CardResponse",
    "TransactionCreate",
    "TransactionResponse",
    "ContributionCreate",
    "ContributionResponse",
    "GoalCreate",
    "GoalUpdate",
    "GoalResponse",
    "InvestmentCreate",
    "InvestmentResponse",
    "MandateCreate",
    "MandateUpdate",
    "MandateResponse",
    # Services
    "UserService",
    "user_service",
    # Utils
    "hash_password",
    "verify_password",
]
