"""Investment model."""

from datetime import datetime
from decimal import Decimal

from sqlalchemy import CheckConstraint, ForeignKey, Numeric, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, UUIDMixin


class Investment(Base, UUIDMixin):
    """Investment model for storing investment records."""

    __tablename__ = "investments"

    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    goal_id: Mapped[UUID | None] = mapped_column(ForeignKey("goals.id"))

    # Investment fields
    portfolio_type: Mapped[str | None] = mapped_column(String(20))
    amount_invested: Mapped[Decimal | None] = mapped_column(Numeric(10, 2))
    projected_return: Mapped[Decimal | None] = mapped_column(Numeric(5, 2))
    invested_at: Mapped[datetime] = mapped_column(default=func.now(), nullable=False)

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "portfolio_type IN ('fixed_income', 'standard', 'growth')",
            name="check_investment_portfolio_type",
        ),
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="investments")
    goal: Mapped["Goal"] = relationship("Goal", back_populates="investments")
