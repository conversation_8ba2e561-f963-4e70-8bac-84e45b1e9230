"""Transaction model."""

from datetime import datetime
from decimal import Decimal

from sqlalchemy import ForeignKey, Numeric, String, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, UUIDMixin


class Transaction(Base, UUIDMixin):
    """Transaction model for storing financial transactions."""

    __tablename__ = "transactions"

    # Foreign keys
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )
    card_id: Mapped[UUID | None] = mapped_column(ForeignKey("cards.id"))

    # Transaction fields
    amount: Mapped[Decimal | None] = mapped_column(Numeric(10, 2))
    merchant_name: Mapped[str | None] = mapped_column(Text)
    category: Mapped[str | None] = mapped_column(String(50))
    timestamp: Mapped[datetime] = mapped_column(default=func.now(), nullable=False)
    original_transaction_id: Mapped[str | None] = mapped_column(Text)

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="transactions")
    card: Mapped["Card"] = relationship("Card", back_populates="transactions")
    contributions: Mapped[list["Contribution"]] = relationship(
        "Contribution", back_populates="source_transaction"
    )
