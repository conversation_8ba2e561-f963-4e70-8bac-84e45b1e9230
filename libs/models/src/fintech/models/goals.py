"""Goal model."""

from datetime import date
from decimal import Decimal

from sqlalchemy import CheckConstraint, Date, ForeignKey, Numeric, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, TimestampMixin, UUIDMixin


class Goal(Base, UUIDMixin, TimestampMixin):
    """Goal model for storing financial goals."""

    __tablename__ = "goals"

    # Foreign key
    user_id: Mapped[UUID] = mapped_column(
        ForeignKey("users.id", ondelete="CASCADE"), nullable=False
    )

    # Goal fields
    name: Mapped[str | None] = mapped_column(Text)
    target_amount: Mapped[Decimal | None] = mapped_column(Numeric(10, 2))
    target_date: Mapped[date | None] = mapped_column(Date)
    strategy: Mapped[str | None] = mapped_column(String(20))

    # Constraints
    __table_args__ = (
        CheckConstraint(
            "strategy IN ('fixed_income', 'standard', 'growth')",
            name="check_goal_strategy",
        ),
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="goals")
    investments: Mapped[list["Investment"]] = relationship(
        "Investment", back_populates="goal"
    )
