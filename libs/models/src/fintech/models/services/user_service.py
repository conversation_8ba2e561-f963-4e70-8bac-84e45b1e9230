"""User service for business logic."""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session

from fintech.models.repositories.users import user_repository
from fintech.models.schemas.users import UserCreate, UserUpdate
from fintech.models.user_settings import UserSettings
from fintech.models.users import User
from fintech.models.utils.security import hash_password, verify_password


class UserService:
    """Service for user-related business logic."""

    def create_user(self, db: Session, *, user_in: UserCreate) -> User:
        """Create a new user with hashed password and default settings."""
        # Check if email is already taken
        if user_repository.is_email_taken(db, email=user_in.email):
            msg = "Email already registered"
            raise ValueError(msg)

        # Hash password
        hashed_password = hash_password(user_in.password)

        # Create user
        user_data = user_in.model_dump(exclude={"password"})
        user_data["password_hash"] = hashed_password

        user = user_repository.create(db, obj_in=user_data)

        # Create default user settings
        settings_data = {
            "user_id": user.id,
            "enable_roundups": True,
            "enable_percentage_saving": False,
            "enable_recurring": False,
            "roundup_to": 1,
            "percentage_saving": 5.00,
        }

        settings = UserSettings(**settings_data)
        db.add(settings)
        db.commit()

        return user

    async def create_user_async(self, db: AsyncSession, *, user_in: UserCreate) -> User:
        """Create a new user with hashed password and default settings (async)."""
        # Check if email is already taken
        if await user_repository.is_email_taken_async(db, email=user_in.email):
            msg = "Email already registered"
            raise ValueError(msg)

        # Hash password
        hashed_password = hash_password(user_in.password)

        # Create user
        user_data = user_in.model_dump(exclude={"password"})
        user_data["password_hash"] = hashed_password

        user = await user_repository.create_async(db, obj_in=user_data)

        # Create default user settings
        settings_data = {
            "user_id": user.id,
            "enable_roundups": True,
            "enable_percentage_saving": False,
            "enable_recurring": False,
            "roundup_to": 1,
            "percentage_saving": 5.00,
        }

        settings = UserSettings(**settings_data)
        db.add(settings)
        await db.commit()

        return user

    def authenticate_user(
        self, db: Session, *, email: str, password: str
    ) -> User | None:
        """Authenticate user with email and password."""
        user = user_repository.get_by_email(db, email=email)
        if not user:
            return None

        if not verify_password(password, user.password_hash):
            return None

        return user

    async def authenticate_user_async(
        self, db: AsyncSession, *, email: str, password: str
    ) -> User | None:
        """Authenticate user with email and password (async)."""
        user = await user_repository.get_by_email_async(db, email=email)
        if not user:
            return None

        if not verify_password(password, user.password_hash):
            return None

        return user

    def update_user(self, db: Session, *, user: User, user_in: UserUpdate) -> User:
        """Update user information."""
        return user_repository.update(
            db, db_obj=user, obj_in=user_in.model_dump(exclude_unset=True)
        )

    async def update_user_async(
        self, db: AsyncSession, *, user: User, user_in: UserUpdate
    ) -> User:
        """Update user information (async)."""
        return await user_repository.update_async(
            db, db_obj=user, obj_in=user_in.model_dump(exclude_unset=True)
        )

    def update_user_password(
        self, db: Session, *, user: User, new_password: str
    ) -> User:
        """Update user password."""
        hashed_password = hash_password(new_password)
        return user_repository.update(
            db, db_obj=user, obj_in={"password_hash": hashed_password}
        )

    async def update_user_password_async(
        self, db: AsyncSession, *, user: User, new_password: str
    ) -> User:
        """Update user password (async)."""
        hashed_password = hash_password(new_password)
        return await user_repository.update_async(
            db, db_obj=user, obj_in={"password_hash": hashed_password}
        )


# Global service instance
user_service = UserService()
