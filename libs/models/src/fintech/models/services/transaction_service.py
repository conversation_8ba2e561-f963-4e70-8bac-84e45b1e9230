"""Transaction service for business logic."""

from datetime import datetime, timed<PERSON><PERSON>
from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.repositories.transactions import transaction_repository
from fintech.models.schemas.transactions import TransactionCreate
from fintech.models.transactions import Transaction


class TransactionService:
    """Service for transaction-related business logic."""

    def get_user_transactions(
        self,
        db: Session,
        *,
        user_id: UUID,
        skip: int = 0,
        limit: int = 100
    ) -> list[Transaction]:
        """Get transactions for a user with pagination."""
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)
        # Apply pagination manually since repository doesn't support it
        return transactions[skip:skip + limit]

    def get_transaction_by_id(
        self, db: Session, *, transaction_id: UUID, user_id: UUID
    ) -> Transaction | None:
        """Get a specific transaction by ID, ensuring it belongs to the user."""
        transaction = transaction_repository.get(db, record_id=transaction_id)
        if transaction and transaction.user_id == user_id:
            return transaction
        return None

    def create_transaction(
        self, db: Session, *, transaction_in: TransactionCreate, user_id: UUID
    ) -> Transaction:
        """Create a new transaction for a user."""
        # Validate transaction data
        if transaction_in.amount and transaction_in.amount <= 0:
            raise ValueError("Transaction amount must be positive")

        # Validate card belongs to user if provided
        if transaction_in.card_id:
            from fintech.models.repositories.cards import card_repository
            card = card_repository.get(db, record_id=transaction_in.card_id)
            if not card or card.user_id != user_id:
                raise ValueError("Card does not belong to user")

        transaction_data = transaction_in.model_dump()
        transaction_data["user_id"] = user_id

        return transaction_repository.create(db, obj_in=transaction_data)

    def get_transaction_statistics(
        self, db: Session, *, user_id: UUID
    ) -> dict[str, any]:
        """Get comprehensive transaction statistics for a user."""
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        if not transactions:
            return {
                "total_transactions": 0,
                "total_amount": 0.0,
                "average_amount": 0.0,
                "categories": {},
                "monthly_spending": {},
                "recent_activity": []
            }

        # Calculate basic statistics
        total_amount = sum(float(t.amount or 0) for t in transactions)
        total_count = len(transactions)
        average_amount = total_amount / total_count if total_count > 0 else 0

        # Category breakdown
        categories = {}
        for transaction in transactions:
            category = transaction.category or "Uncategorized"
            if category not in categories:
                categories[category] = {"count": 0, "amount": 0.0}
            categories[category]["count"] += 1
            categories[category]["amount"] += float(transaction.amount or 0)

        # Monthly spending (last 12 months)
        monthly_spending = {}
        for transaction in transactions:
            if transaction.timestamp:
                month_key = transaction.timestamp.strftime("%Y-%m")
                if month_key not in monthly_spending:
                    monthly_spending[month_key] = 0.0
                monthly_spending[month_key] += float(transaction.amount or 0)

        # Recent activity (last 5 transactions)
        recent_transactions = sorted(
            transactions, key=lambda x: x.timestamp or datetime.min, reverse=True
        )[:5]

        recent_activity = [
            {
                "id": str(t.id),
                "amount": float(t.amount or 0),
                "merchant_name": t.merchant_name,
                "category": t.category,
                "timestamp": t.timestamp.isoformat() if t.timestamp else None,
            }
            for t in recent_transactions
        ]

        return {
            "total_transactions": total_count,
            "total_amount": round(total_amount, 2),
            "average_amount": round(average_amount, 2),
            "categories": categories,
            "monthly_spending": monthly_spending,
            "recent_activity": recent_activity,
        }

    def get_spending_by_category(
        self, db: Session, *, user_id: UUID, days: int = 30
    ) -> dict[str, float]:
        """Get spending breakdown by category for the last N days."""
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        # Filter transactions from the last N days
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_transactions = [
            t for t in transactions
            if t.timestamp and t.timestamp >= cutoff_date
        ]

        # Group by category
        category_spending = {}
        for transaction in recent_transactions:
            category = transaction.category or "Uncategorized"
            if category not in category_spending:
                category_spending[category] = 0.0
            category_spending[category] += float(transaction.amount or 0)

        return {
            category: round(amount, 2)
            for category, amount in category_spending.items()
        }

    def get_monthly_spending_trend(
        self, db: Session, *, user_id: UUID, months: int = 6
    ) -> dict[str, float]:
        """Get monthly spending trend for the last N months."""
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        # Calculate cutoff date
        cutoff_date = datetime.now() - timedelta(days=months * 30)
        recent_transactions = [
            t for t in transactions
            if t.timestamp and t.timestamp >= cutoff_date
        ]

        # Group by month
        monthly_spending = {}
        for transaction in recent_transactions:
            if transaction.timestamp:
                month_key = transaction.timestamp.strftime("%Y-%m")
                if month_key not in monthly_spending:
                    monthly_spending[month_key] = 0.0
                monthly_spending[month_key] += float(transaction.amount or 0)

        return {
            month: round(amount, 2)
            for month, amount in sorted(monthly_spending.items())
        }


# Global service instance
transaction_service = TransactionService()
