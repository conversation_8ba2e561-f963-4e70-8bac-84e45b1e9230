"""Card service for business logic."""

from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.cards import Card
from fintech.models.repositories.cards import card_repository
from fintech.models.schemas.cards import CardCreate, CardUpdate


class CardService:
    """Service for card-related business logic."""

    def get_user_cards(self, db: Session, *, user_id: UUID) -> list[Card]:
        """Get all cards for a specific user."""
        return card_repository.get_by_user_id(db, user_id=user_id)

    def get_card_by_id(self, db: Session, *, card_id: UUID, user_id: UUID) -> Card | None:
        """Get a specific card by ID, ensuring it belongs to the user."""
        card = card_repository.get(db, record_id=card_id)
        if card and card.user_id == user_id:
            return card
        return None

    def create_card(self, db: Session, *, card_in: CardCreate, user_id: UUID) -> Card:
        """Create a new card for a user."""
        # Validate card data and apply business rules
        if card_in.spending_limit and card_in.spending_limit <= 0:
            raise ValueError("Spending limit must be positive")

        # Ensure last_four is exactly 4 digits
        if card_in.last_four and len(card_in.last_four) != 4:
            raise ValueError("Last four digits must be exactly 4 characters")

        # Check if user already has a card with the same last four digits
        existing_cards = self.get_user_cards(db, user_id=user_id)
        if any(card.last_four == card_in.last_four for card in existing_cards):
            raise ValueError("Card with these last four digits already exists")

        card_data = card_in.model_dump()
        card_data["user_id"] = user_id

        return card_repository.create(db, obj_in=card_data)

    def update_card(
        self, db: Session, *, card: Card, card_in: CardUpdate, user_id: UUID
    ) -> Card:
        """Update card information."""
        # Ensure the card belongs to the user
        if card.user_id != user_id:
            raise ValueError("Card does not belong to user")

        # Validate spending limit
        if card_in.spending_limit and card_in.spending_limit <= 0:
            raise ValueError("Spending limit must be positive")

        return card_repository.update(
            db, db_obj=card, obj_in=card_in.model_dump(exclude_unset=True)
        )

    def delete_card(self, db: Session, *, card_id: UUID, user_id: UUID) -> bool:
        """Delete a card, ensuring it belongs to the user."""
        card = self.get_card_by_id(db, card_id=card_id, user_id=user_id)
        if not card:
            return False

        card_repository.remove(db, record_id=card_id)
        return True

    def get_card_spending_summary(
        self, db: Session, *, card_id: UUID, user_id: UUID
    ) -> dict:
        """Get spending summary for a specific card."""
        card = self.get_card_by_id(db, card_id=card_id, user_id=user_id)
        if not card:
            raise ValueError("Card not found or does not belong to user")

        # Import here to avoid circular imports
        from fintech.models.repositories.transactions import transaction_repository

        transactions = transaction_repository.get_by_card_id(db, card_id=card_id)

        total_spent = sum(float(t.amount or 0) for t in transactions)
        transaction_count = len(transactions)

        spending_limit = float(card.spending_limit or 0)
        remaining_limit = max(0, spending_limit - total_spent)
        utilization_percentage = (
            (total_spent / spending_limit * 100) if spending_limit > 0 else 0
        )

        return {
            "card_id": str(card.id),
            "card_name": card.card_name,
            "total_spent": round(total_spent, 2),
            "transaction_count": transaction_count,
            "spending_limit": spending_limit,
            "remaining_limit": round(remaining_limit, 2),
            "utilization_percentage": round(utilization_percentage, 2),
        }


# Global service instance
card_service = CardService()
