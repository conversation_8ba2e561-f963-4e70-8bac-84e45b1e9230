[project]
name = "fintech-models"
version = "0.0.0"

dependencies = [
    # Core dependencies
    "sqlalchemy>=2.0.23",
    "pydantic[email]>=2.5.0",
    "pydantic-settings>=2.1.0",
    "psycopg2-binary>=2.9.9",
    "asyncpg>=0.29.0",
    "bcrypt>=4.1.2",
    "greenlet>=3.0.0",
    "alembic>=1.13.1",
]

[project.optional-dependencies]
test = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "testcontainers>=3.7.1",
    "ruff>=0.1.0",
]
typing = [
    "types-psycopg2",
    "mypy>=1.8.0",
]
dev = [
    "ruff>=0.1.0",
    "mypy>=1.8.0",
]

# No external sources needed for testing

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[dependency-groups]
test = [
    "fastapi>=0.115.12",
    "httpx>=0.28.1",
]
