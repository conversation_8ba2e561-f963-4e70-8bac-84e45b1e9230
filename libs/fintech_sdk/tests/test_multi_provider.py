"""Tests for multi-provider and regional functionality."""

import os
import pytest
from unittest.mock import patch

from fintech_sdk import ProviderFactory
from fintech_sdk.config import FinTechSDKConfig
from fintech_sdk.exceptions import ConfigurationError


class TestMultiProviderSupport:
    """Test multi-provider functionality."""
    
    def test_default_provider_selection(self):
        """Test default provider selection."""
        config = FinTechSDKConfig(provider="mock")
        factory = ProviderFactory(config)
        
        assert factory._get_effective_provider_name() == "mock"
    
    def test_regional_provider_override(self):
        """Test regional provider override via environment variable."""
        config = FinTechSDKConfig(provider="mock")
        factory = ProviderFactory(config, region="switzerland")
        
        # Without regional override, should use default
        assert factory._get_effective_provider_name() == "mock"
        
        # With regional override
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER_SWITZERLAND': 'swiss_bank'}):
            assert factory._get_effective_provider_name() == "swiss_bank"
    
    def test_multiple_regions(self):
        """Test different providers for different regions."""
        config = FinTechSDKConfig(provider="mock")
        
        # Switzerland region
        swiss_factory = ProviderFactory(config, region="switzerland")
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER_SWITZERLAND': 'swiss_bank'}):
            assert swiss_factory._get_effective_provider_name() == "swiss_bank"
        
        # Germany region
        german_factory = ProviderFactory(config, region="germany")
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER_GERMANY': 'german_bank'}):
            assert german_factory._get_effective_provider_name() == "german_bank"
        
        # Default region (no override)
        default_factory = ProviderFactory(config)
        assert default_factory._get_effective_provider_name() == "mock"
    
    @pytest.mark.asyncio
    async def test_swiss_provider_functionality(self):
        """Test Swiss provider functionality."""
        config = FinTechSDKConfig(provider="swiss_bank")
        factory = ProviderFactory(config, region="switzerland")
        
        # Test account provider
        account_provider = factory.create_account_provider()
        accounts = await account_provider.get_account_list()
        
        assert len(accounts) == 2
        assert accounts[0].currency == "CHF"
        assert "Swiss" in accounts[0].name
        assert accounts[0].metadata["country"] == "Switzerland"
        
        # Test account balance
        balance = await account_provider.get_account_balance(accounts[0].id)
        assert balance.currency == "CHF"
        
        # Test transactions
        transaction_provider = factory.create_transaction_provider()
        transactions = await transaction_provider.get_transactions(accounts[0].id)
        
        assert len(transactions) > 0
        assert transactions[0].currency == "CHF"
        assert transactions[0].metadata["country"] == "Switzerland"
    
    @pytest.mark.asyncio
    async def test_german_provider_functionality(self):
        """Test German provider functionality."""
        config = FinTechSDKConfig(provider="german_bank")
        factory = ProviderFactory(config, region="germany")
        
        # Test account provider
        account_provider = factory.create_account_provider()
        accounts = await account_provider.get_account_list()
        
        assert len(accounts) == 2
        assert accounts[0].currency == "EUR"
        assert "German" in accounts[0].name
        assert accounts[0].metadata["country"] == "Germany"
        
        # Test account balance
        balance = await account_provider.get_account_balance(accounts[0].id)
        assert balance.currency == "EUR"
        
        # Test transactions
        transaction_provider = factory.create_transaction_provider()
        transactions = await transaction_provider.get_transactions(accounts[0].id)
        
        assert len(transactions) > 0
        assert transactions[0].currency == "EUR"
        assert transactions[0].metadata["country"] == "Germany"
    
    @pytest.mark.asyncio
    async def test_provider_switching_workflow(self):
        """Test complete workflow with provider switching."""
        base_config = FinTechSDKConfig(provider="mock")
        
        # Test Switzerland workflow
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER_SWITZERLAND': 'swiss_bank'}):
            swiss_sdk = ProviderFactory(base_config, region="switzerland")
            swiss_accounts = await swiss_sdk.account.get_account_list()
            
            assert len(swiss_accounts) == 2
            assert all(acc.currency == "CHF" for acc in swiss_accounts)
            
            swiss_balance = await swiss_sdk.account.get_account_balance(swiss_accounts[0].id)
            assert swiss_balance.currency == "CHF"
        
        # Test Germany workflow
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER_GERMANY': 'german_bank'}):
            german_sdk = ProviderFactory(base_config, region="germany")
            german_accounts = await german_sdk.account.get_account_list()
            
            assert len(german_accounts) == 2
            assert all(acc.currency == "EUR" for acc in german_accounts)
            
            german_balance = await german_sdk.account.get_account_balance(german_accounts[0].id)
            assert german_balance.currency == "EUR"
        
        # Test default (mock) workflow
        default_sdk = ProviderFactory(base_config)
        default_accounts = await default_sdk.account.get_account_list()
        
        assert len(default_accounts) == 4  # Mock provider has 4 accounts
        assert all(acc.currency == "USD" for acc in default_accounts)
    
    def test_load_from_env_with_region(self):
        """Test loading factory from environment with region."""
        with patch.dict(os.environ, {
            'FINTECH_SDK_PROVIDER': 'mock',
            'FINTECH_SDK_PROVIDER_SWITZERLAND': 'swiss_bank'
        }):
            # Default region
            default_factory = ProviderFactory.load_from_env()
            assert default_factory._get_effective_provider_name() == "mock"
            
            # Switzerland region
            swiss_factory = ProviderFactory.load_from_env(region="switzerland")
            assert swiss_factory._get_effective_provider_name() == "swiss_bank"
    
    def test_provider_registry_lists_all_providers(self):
        """Test that provider registry includes all providers."""
        factory = ProviderFactory(FinTechSDKConfig(provider="mock"))
        providers = factory.list_providers()
        
        # Check that all providers are registered
        expected_providers = ["mock", "plaid", "stripe", "tink", "swiss_bank", "german_bank"]
        for provider in expected_providers:
            assert provider in providers
        
        # Check that regional providers have correct interfaces
        assert "account" in providers["swiss_bank"]
        assert "transaction" in providers["swiss_bank"]
        assert "account" in providers["german_bank"]
        assert "transaction" in providers["german_bank"]


class TestRegionalConfiguration:
    """Test regional configuration scenarios."""
    
    @pytest.mark.asyncio
    async def test_real_world_scenario_switzerland(self):
        """Test real-world scenario for Switzerland."""
        # Simulate a Swiss fintech app configuration
        with patch.dict(os.environ, {
            'FINTECH_SDK_PROVIDER': 'mock',  # Default fallback
            'FINTECH_SDK_PROVIDER_SWITZERLAND': 'swiss_bank',  # Swiss-specific
            'FINTECH_SDK_ENVIRONMENT': 'production',
            'FINTECH_SDK_TIMEOUT': '45'
        }):
            # Create SDK for Swiss operations
            swiss_sdk = ProviderFactory.load_from_env(region="switzerland")
            
            # Verify correct provider is selected
            assert swiss_sdk._get_effective_provider_name() == "swiss_bank"
            
            # Test Swiss banking operations
            accounts = await swiss_sdk.account.get_account_list()
            assert len(accounts) == 2
            
            # Verify Swiss-specific data
            checking_account = next(acc for acc in accounts if acc.account_type.value == "checking")
            assert checking_account.currency == "CHF"
            assert "iban" in checking_account.metadata
            assert checking_account.metadata["country"] == "Switzerland"
            
            # Test transactions with Swiss merchants
            transactions = await swiss_sdk.transaction.get_transactions(checking_account.id)
            assert len(transactions) > 0
            
            swiss_transaction = transactions[0]
            assert swiss_transaction.currency == "CHF"
            assert "Migros" in swiss_transaction.description or "SBB" in swiss_transaction.description
    
    @pytest.mark.asyncio
    async def test_real_world_scenario_germany(self):
        """Test real-world scenario for Germany."""
        # Simulate a German fintech app configuration
        with patch.dict(os.environ, {
            'FINTECH_SDK_PROVIDER': 'mock',  # Default fallback
            'FINTECH_SDK_PROVIDER_GERMANY': 'german_bank',  # German-specific
            'FINTECH_SDK_ENVIRONMENT': 'production',
            'FINTECH_SDK_TIMEOUT': '30'
        }):
            # Create SDK for German operations
            german_sdk = ProviderFactory.load_from_env(region="germany")
            
            # Verify correct provider is selected
            assert german_sdk._get_effective_provider_name() == "german_bank"
            
            # Test German banking operations
            accounts = await german_sdk.account.get_account_list()
            assert len(accounts) == 2
            
            # Verify German-specific data
            checking_account = next(acc for acc in accounts if acc.account_type.value == "checking")
            assert checking_account.currency == "EUR"
            assert "iban" in checking_account.metadata
            assert checking_account.metadata["country"] == "Germany"
            
            # Test transactions with German merchants
            transactions = await german_sdk.transaction.get_transactions(checking_account.id)
            assert len(transactions) > 0
            
            german_transaction = transactions[0]
            assert german_transaction.currency == "EUR"
            assert "REWE" in german_transaction.description or "Deutsche Bahn" in german_transaction.description
    
    @pytest.mark.asyncio
    async def test_fallback_to_default_provider(self):
        """Test fallback to default provider when regional provider is not configured."""
        # Only set default provider, no regional overrides
        with patch.dict(os.environ, {'FINTECH_SDK_PROVIDER': 'mock'}):
            # Try to create SDK for a region without specific provider
            italy_sdk = ProviderFactory.load_from_env(region="italy")
            
            # Should fallback to default provider
            assert italy_sdk._get_effective_provider_name() == "mock"
            
            # Should work with mock provider
            accounts = await italy_sdk.account.get_account_list()
            assert len(accounts) == 4  # Mock provider accounts
            assert all(acc.currency == "USD" for acc in accounts)


class TestProviderRegistration:
    """Test provider registration functionality."""
    
    def test_register_custom_provider(self):
        """Test registering a custom provider."""
        from fintech_sdk.factory import register_provider, _registry
        from fintech_sdk.interfaces import AccountProvider
        
        @register_provider("custom_test")
        class CustomTestProvider(AccountProvider):
            def __init__(self, config=None, sdk_config=None):
                super().__init__()
                self.config = config
                self.sdk_config = sdk_config

            async def get_account_list(self):
                return []

            async def get_account_balance(self, account_id: str):
                pass

            async def get_account(self, account_id: str):
                pass
        
        # Verify provider was registered
        providers = _registry.list_providers()
        assert "custom_test" in providers
        assert "account" in providers["custom_test"]
        
        # Verify we can create the provider
        config = FinTechSDKConfig(provider="custom_test")
        factory = ProviderFactory(config)
        provider = factory.create_account_provider()
        assert isinstance(provider, CustomTestProvider)
