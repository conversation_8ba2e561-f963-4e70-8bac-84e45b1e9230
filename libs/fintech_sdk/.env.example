# FinTech SDK Configuration

# Provider Selection
FINTECH_SDK_PROVIDER=mock
FINTECH_SDK_ENVIRONMENT=sandbox
FINTECH_SDK_TIMEOUT=30
FINTECH_SDK_LOG_LEVEL=INFO
FINTECH_SDK_LOG_REQUESTS=false

# Mock Provider (for development/testing)
MOCK_DELAY=0.1
MOCK_ERROR_RATE=0.0

# Plaid Configuration
PLAID_CLIENT_ID=your_plaid_client_id
PLAID_SECRET=your_plaid_secret
PLAID_ENVIRONMENT=sandbox

# Stripe Configuration
STRIPE_API_KEY=sk_test_your_stripe_api_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Tink Configuration
TINK_CLIENT_ID=your_tink_client_id
TINK_CLIENT_SECRET=your_tink_client_secret
TINK_ENVIRONMENT=sandbox
