"""Provider factory for fintech SDK."""

import json
import os
from typing import Any

from .config import PROVIDER_CONFIGS, FinTechSDKConfig
from .exceptions import ConfigurationError
from .interfaces import (
    AccountProvider,
    InvestmentProvider,
    PaymentProvider,
    TransactionProvider,
)


class ProviderRegistry:
    """Registry for provider implementations."""

    def __init__(self):
        self._providers: dict[str, dict[str, type]] = {}

    def register(
        self,
        name: str,
        account: type[AccountProvider] = None,
        transaction: type[TransactionProvider] = None,
        payment: type[PaymentProvider] = None,
        investment: type[InvestmentProvider] = None,
    ):
        """Register provider implementations."""
        self._providers[name] = {}
        if account:
            self._providers[name]["account"] = account
        if transaction:
            self._providers[name]["transaction"] = transaction
        if payment:
            self._providers[name]["payment"] = payment
        if investment:
            self._providers[name]["investment"] = investment

    def get_provider(self, name: str, interface: str) -> type:
        """Get provider implementation."""
        if name not in self._providers:
            raise ConfigurationError(f"Provider '{name}' not registered")

        if interface not in self._providers[name]:
            raise ConfigurationError(
                f"Interface '{interface}' not available for provider '{name}'"
            )

        return self._providers[name][interface]

    def list_providers(self) -> dict[str, list]:
        """List all registered providers and their interfaces."""
        return {
            name: list(interfaces.keys())
            for name, interfaces in self._providers.items()
        }


# Global registry instance
_registry = ProviderRegistry()


def register_provider(name: str):
    """Decorator to register a provider."""

    def decorator(cls):
        # Determine which interfaces the class implements
        interfaces = {}
        if issubclass(cls, AccountProvider):
            interfaces["account"] = cls
        if issubclass(cls, TransactionProvider):
            interfaces["transaction"] = cls
        if issubclass(cls, PaymentProvider):
            interfaces["payment"] = cls
        if issubclass(cls, InvestmentProvider):
            interfaces["investment"] = cls

        _registry.register(name, **interfaces)
        return cls

    return decorator


class ProviderFactory:
    """Factory for creating provider instances."""

    def __init__(self, config: FinTechSDKConfig, region: str = None):
        self.config = config
        self.region = region
        self._load_builtin_providers()

    def _load_builtin_providers(self):
        """Load built-in providers."""
        try:
            # Import built-in providers to trigger registration
            from .providers import mock, plaid, stripe, tink  # noqa: F401
        except ImportError:
            # Some providers might not be available
            pass

    @classmethod
    def load_from_env(cls, region: str = None) -> "ProviderFactory":
        """Load factory from environment variables."""
        config = FinTechSDKConfig()
        return cls(config, region=region)

    @classmethod
    def load_from_config(
        cls, config_path: str, region: str = None
    ) -> "ProviderFactory":
        """Load factory from JSON config file."""
        if not os.path.exists(config_path):
            raise ConfigurationError(f"Config file not found: {config_path}")

        with open(config_path) as f:
            config_data = json.load(f)

        config = FinTechSDKConfig(**config_data)
        return cls(config, region=region)

    def _get_effective_provider_name(self) -> str:
        """Get the effective provider name based on region and configuration."""
        if self.region:
            # Check for region-specific provider override
            region_provider_env = f"FINTECH_SDK_PROVIDER_{self.region.upper()}"
            region_provider = os.environ.get(region_provider_env)
            if region_provider:
                return region_provider

        return self.config.provider

    def _get_provider_config(self, provider_name: str) -> Any:
        """Get provider-specific configuration."""
        if provider_name not in PROVIDER_CONFIGS:
            return None

        config_class = PROVIDER_CONFIGS[provider_name]
        try:
            return config_class()
        except Exception as e:
            raise ConfigurationError(
                f"Failed to load configuration for provider '{provider_name}': {e}"
            )

    def create_account_provider(self) -> AccountProvider:
        """Create account provider instance."""
        provider_name = self._get_effective_provider_name()
        provider_class = _registry.get_provider(provider_name, "account")
        provider_config = self._get_provider_config(provider_name)
        return provider_class(config=provider_config, sdk_config=self.config)

    def create_transaction_provider(self) -> TransactionProvider:
        """Create transaction provider instance."""
        provider_name = self._get_effective_provider_name()
        provider_class = _registry.get_provider(provider_name, "transaction")
        provider_config = self._get_provider_config(provider_name)
        return provider_class(config=provider_config, sdk_config=self.config)

    def create_payment_provider(self) -> PaymentProvider:
        """Create payment provider instance."""
        provider_name = self._get_effective_provider_name()
        provider_class = _registry.get_provider(provider_name, "payment")
        provider_config = self._get_provider_config(provider_name)
        return provider_class(config=provider_config, sdk_config=self.config)

    def create_investment_provider(self) -> InvestmentProvider:
        """Create investment provider instance."""
        provider_name = self._get_effective_provider_name()
        provider_class = _registry.get_provider(provider_name, "investment")
        provider_config = self._get_provider_config(provider_name)
        return provider_class(config=provider_config, sdk_config=self.config)

    @property
    def account(self) -> AccountProvider:
        """Get account provider instance."""
        return self.create_account_provider()

    @property
    def transaction(self) -> TransactionProvider:
        """Get transaction provider instance."""
        return self.create_transaction_provider()

    @property
    def payment(self) -> PaymentProvider:
        """Get payment provider instance."""
        return self.create_payment_provider()

    @property
    def investment(self) -> InvestmentProvider:
        """Get investment provider instance."""
        return self.create_investment_provider()

    def list_providers(self) -> dict[str, list]:
        """List all available providers."""
        return _registry.list_providers()
