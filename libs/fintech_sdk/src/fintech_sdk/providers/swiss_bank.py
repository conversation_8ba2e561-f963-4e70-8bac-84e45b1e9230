"""Swiss Bank provider implementation for Switzerland region."""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional

from ..config import FinTechSDKConfig
from ..exceptions import AccountNotFoundError, TransactionNotFoundError
from ..factory import register_provider
from ..interfaces import AccountProvider, TransactionProvider
from ..models import Account, AccountBalance, AccountType, Transaction, TransactionType
from .base import BaseProvider


@register_provider("swiss_bank")
class SwissBankProvider(BaseProvider, AccountProvider, TransactionProvider):
    """Swiss Bank provider for Switzerland region."""
    
    def __init__(self, config=None, **kwargs):
        super().__init__(config, **kwargs)
        self.base_url = "https://api.swissbank.ch/v1"
    
    async def get_account_list(self) -> List[Account]:
        """Get list of accounts from Swiss Bank."""
        # Mock Swiss Bank accounts with CHF currency
        mock_accounts = [
            {
                "account_id": "ch_checking_001",
                "name": "Swiss Checking Account",
                "type": "checking",
                "currency": "CHF",
                "iban": "CH93 0076 2011 6238 5295 7"
            },
            {
                "account_id": "ch_savings_001", 
                "name": "Swiss Savings Account",
                "type": "savings",
                "currency": "CHF",
                "iban": "CH56 0483 5012 3456 7800 9"
            }
        ]
        
        accounts = []
        for acc_data in mock_accounts:
            account_type = AccountType.CHECKING if acc_data["type"] == "checking" else AccountType.SAVINGS
            
            account = Account(
                id=acc_data["account_id"],
                name=acc_data["name"],
                account_type=account_type,
                currency=acc_data["currency"],
                provider_id=acc_data["account_id"],
                metadata={
                    "iban": acc_data["iban"],
                    "bank": "Swiss Bank",
                    "country": "Switzerland"
                }
            )
            accounts.append(account)
        
        return accounts
    
    async def get_account_balance(self, account_id: str) -> AccountBalance:
        """Get account balance from Swiss Bank."""
        if account_id == "ch_checking_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("5000.50"),
                current=Decimal("5200.75"),
                currency="CHF",
                last_updated=datetime.now()
            )
        elif account_id == "ch_savings_001":
            return AccountBalance(
                account_id=account_id,
                available=Decimal("25000.00"),
                current=Decimal("25000.00"),
                currency="CHF",
                last_updated=datetime.now()
            )
        else:
            raise AccountNotFoundError(f"Account {account_id} not found", provider="swiss_bank")
    
    async def get_account(self, account_id: str) -> Account:
        """Get account details from Swiss Bank."""
        accounts = await self.get_account_list()
        account = next((acc for acc in accounts if acc.id == account_id), None)
        
        if not account:
            raise AccountNotFoundError(f"Account {account_id} not found", provider="swiss_bank")
        
        return account
    
    async def get_transactions(
        self,
        account_id: str,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = None,
    ) -> List[Transaction]:
        """Get transactions from Swiss Bank."""
        # Mock Swiss transactions
        mock_transactions = [
            {
                "transaction_id": "ch_txn_001",
                "account_id": account_id,
                "amount": -45.80,
                "currency": "CHF",
                "date": "2024-01-15",
                "description": "Migros Supermarket",
                "category": "Groceries"
            },
            {
                "transaction_id": "ch_txn_002",
                "account_id": account_id,
                "amount": -120.00,
                "currency": "CHF",
                "date": "2024-01-14",
                "description": "SBB Train Ticket",
                "category": "Transportation"
            }
        ]
        
        transactions = []
        for txn_data in mock_transactions:
            transaction = Transaction(
                id=txn_data["transaction_id"],
                account_id=txn_data["account_id"],
                amount=Decimal(str(abs(txn_data["amount"]))),
                currency=txn_data["currency"],
                transaction_type=TransactionType.DEBIT if txn_data["amount"] < 0 else TransactionType.CREDIT,
                description=txn_data["description"],
                date=datetime.fromisoformat(txn_data["date"]),
                category=txn_data["category"],
                merchant=txn_data["description"],
                provider_id=txn_data["transaction_id"],
                metadata={
                    "country": "Switzerland",
                    "original_amount": txn_data["amount"]
                }
            )
            transactions.append(transaction)
        
        return transactions
    
    async def get_transaction(self, transaction_id: str) -> Transaction:
        """Get a specific transaction from Swiss Bank."""
        if transaction_id.startswith("ch_txn_"):
            return Transaction(
                id=transaction_id,
                account_id="ch_checking_001",
                amount=Decimal("45.80"),
                currency="CHF",
                transaction_type=TransactionType.DEBIT,
                description="Swiss Bank Transaction",
                date=datetime.now(),
                category="general",
                merchant="Swiss Merchant",
                provider_id=transaction_id,
                metadata={"provider": "swiss_bank", "country": "Switzerland"}
            )
        else:
            raise TransactionNotFoundError(f"Transaction {transaction_id} not found", provider="swiss_bank")
