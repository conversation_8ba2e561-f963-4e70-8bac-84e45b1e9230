"""Pydantic models for fintech SDK."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from pydantic import BaseModel, ConfigDict, Field, field_validator


class AccountType(str, Enum):
    """Account types."""

    CHECKING = "checking"
    SAVINGS = "savings"
    CREDIT = "credit"
    INVESTMENT = "investment"
    LOAN = "loan"


class TransactionType(str, Enum):
    """Transaction types."""

    DEBIT = "debit"
    CREDIT = "credit"


class PaymentStatus(str, Enum):
    """Payment status."""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class InvestmentOrderType(str, Enum):
    """Investment order types."""

    BUY = "buy"
    SELL = "sell"


class InvestmentOrderStatus(str, Enum):
    """Investment order status."""

    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"


class Account(BaseModel):
    """Account model."""

    id: str = Field(..., description="Unique account identifier")
    name: str = Field(..., description="Account name")
    account_type: AccountType = Field(..., description="Type of account")
    currency: str = Field(..., description="Account currency (ISO 4217)")
    provider_id: str = Field(..., description="Provider-specific account ID")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    model_config = ConfigDict(json_encoders={Decimal: str})


class AccountBalance(BaseModel):
    """Account balance model."""

    account_id: str = Field(..., description="Account identifier")
    available: Decimal = Field(..., description="Available balance")
    current: Decimal = Field(..., description="Current balance")
    currency: str = Field(..., description="Balance currency (ISO 4217)")
    last_updated: datetime = Field(..., description="Last update timestamp")

    @field_validator("available", "current", mode="before")
    @classmethod
    def validate_decimal(cls, v):
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v


class Transaction(BaseModel):
    """Transaction model."""

    id: str = Field(..., description="Unique transaction identifier")
    account_id: str = Field(..., description="Account identifier")
    amount: Decimal = Field(..., description="Transaction amount")
    currency: str = Field(..., description="Transaction currency (ISO 4217)")
    transaction_type: TransactionType = Field(..., description="Transaction type")
    description: str = Field(..., description="Transaction description")
    date: datetime = Field(..., description="Transaction date")
    category: str | None = Field(None, description="Transaction category")
    merchant: str | None = Field(None, description="Merchant name")
    provider_id: str = Field(..., description="Provider-specific transaction ID")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @field_validator("amount", mode="before")
    @classmethod
    def validate_amount(cls, v):
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v


class PaymentRequest(BaseModel):
    """Payment request model."""

    to_account: str = Field(..., description="Recipient account identifier")
    amount: Decimal = Field(..., description="Payment amount")
    currency: str = Field(..., description="Payment currency (ISO 4217)")
    description: str | None = Field(None, description="Payment description")
    reference: str | None = Field(None, description="Payment reference")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @field_validator("amount", mode="before")
    @classmethod
    def validate_amount(cls, v):
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v


class Payment(BaseModel):
    """Payment model."""

    id: str = Field(..., description="Unique payment identifier")
    from_account: str = Field(..., description="Source account identifier")
    to_account: str = Field(..., description="Recipient account identifier")
    amount: Decimal = Field(..., description="Payment amount")
    currency: str = Field(..., description="Payment currency (ISO 4217)")
    status: PaymentStatus = Field(..., description="Payment status")
    description: str | None = Field(None, description="Payment description")
    reference: str | None = Field(None, description="Payment reference")
    created_at: datetime = Field(..., description="Payment creation timestamp")
    updated_at: datetime = Field(..., description="Payment last update timestamp")
    provider_id: str = Field(..., description="Provider-specific payment ID")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @field_validator("amount", mode="before")
    @classmethod
    def validate_amount(cls, v):
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v


class InvestmentOrder(BaseModel):
    """Investment order model."""

    symbol: str = Field(..., description="Investment symbol (e.g., AAPL, BTC)")
    quantity: Decimal = Field(..., description="Order quantity")
    order_type: InvestmentOrderType = Field(..., description="Order type")
    account_id: str = Field(..., description="Investment account identifier")
    price: Decimal | None = Field(None, description="Order price (for limit orders)")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @field_validator("quantity", "price", mode="before")
    @classmethod
    def validate_decimal(cls, v):
        if v is None:
            return v
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v


class Investment(BaseModel):
    """Investment model."""

    id: str = Field(..., description="Unique investment identifier")
    symbol: str = Field(..., description="Investment symbol")
    quantity: Decimal = Field(..., description="Investment quantity")
    order_type: InvestmentOrderType = Field(..., description="Order type")
    status: InvestmentOrderStatus = Field(..., description="Investment status")
    account_id: str = Field(..., description="Investment account identifier")
    price: Decimal | None = Field(None, description="Execution price")
    created_at: datetime = Field(..., description="Investment creation timestamp")
    updated_at: datetime = Field(..., description="Investment last update timestamp")
    provider_id: str = Field(..., description="Provider-specific investment ID")
    metadata: dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata"
    )

    @field_validator("quantity", "price", mode="before")
    @classmethod
    def validate_decimal(cls, v):
        if v is None:
            return v
        if isinstance(v, int | float | str):
            return Decimal(str(v))
        return v
