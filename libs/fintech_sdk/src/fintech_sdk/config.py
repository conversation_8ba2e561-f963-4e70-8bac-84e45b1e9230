"""Configuration for fintech SDK."""


from pydantic import Field, ConfigDict
from pydantic_settings import BaseSettings


class FinTechSDKConfig(BaseSettings):
    """Configuration for FinTech SDK."""

    # Provider selection
    provider: str = Field(default="mock", description="Provider to use")

    # HTTP settings
    timeout: int = Field(default=30, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum number of retries")
    retry_delay: float = Field(default=1.0, description="Delay between retries in seconds")

    # Environment
    environment: str = Field(default="sandbox", description="Environment (sandbox/production)")

    # Logging
    log_level: str = Field(default="INFO", description="Log level")
    log_requests: bool = Field(default=False, description="Log HTTP requests")

    class Config:
        env_prefix = "FINTECH_SDK_"
        env_file = ".env"
        env_file_encoding = "utf-8"


class PlaidConfig(BaseSettings):
    """Configuration for Plaid provider."""

    client_id: str = Field(..., description="Plaid client ID")
    secret: str = Field(..., description="Plaid secret")
    environment: str = Field(default="sandbox", description="Plaid environment")
    base_url: str | None = Field(None, description="Custom base URL")

    class Config:
        env_prefix = "PLAID_"
        env_file = ".env"
        env_file_encoding = "utf-8"


class StripeConfig(BaseSettings):
    """Configuration for Stripe provider."""

    api_key: str = Field(..., description="Stripe API key")
    webhook_secret: str | None = Field(None, description="Stripe webhook secret")
    base_url: str | None = Field(None, description="Custom base URL")

    class Config:
        env_prefix = "STRIPE_"
        env_file = ".env"
        env_file_encoding = "utf-8"


class TinkConfig(BaseSettings):
    """Configuration for Tink provider."""

    client_id: str = Field(..., description="Tink client ID")
    client_secret: str = Field(..., description="Tink client secret")
    environment: str = Field(default="sandbox", description="Tink environment")
    base_url: str | None = Field(None, description="Custom base URL")

    class Config:
        env_prefix = "TINK_"
        env_file = ".env"
        env_file_encoding = "utf-8"


class MockConfig(BaseSettings):
    """Configuration for Mock provider."""

    delay: float = Field(default=0.1, description="Simulated delay in seconds")
    error_rate: float = Field(default=0.0, description="Simulated error rate (0.0-1.0)")

    class Config:
        env_prefix = "MOCK_"
        env_file = ".env"
        env_file_encoding = "utf-8"


# Provider config mapping
PROVIDER_CONFIGS: dict[str, type] = {
    "plaid": PlaidConfig,
    "stripe": StripeConfig,
    "tink": TinkConfig,
    "mock": MockConfig,
}
