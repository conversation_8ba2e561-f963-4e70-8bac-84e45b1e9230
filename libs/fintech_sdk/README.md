# FinTech SDK

A provider-agnostic fintech integrations SDK that makes it trivial to swap between different financial service providers.

## Features

- **Provider-agnostic**: Switch between providers (Plaid, Stripe, Tink, etc.) with minimal code changes
- **Type-safe**: Full typing support with Pydantic models and Python type hints
- **Async/await**: Modern async Python API for high performance
- **Extensible**: Easy to add new providers via plugin system
- **Production-ready**: Comprehensive error handling, logging, and configuration
- **Well-tested**: Extensive test suite with mocked responses

## Quick Start

### Installation

```bash
# Install the SDK
pip install fintech-sdk

# Or install with development dependencies
pip install fintech-sdk[dev]
```

### Basic Usage

```python
import asyncio
from fintech_sdk import ProviderFactory

async def main():
    # Load SDK with provider from environment
    sdk = ProviderFactory.load_from_env()
    
    # Get accounts
    accounts = await sdk.account.get_account_list()
    print(f"Found {len(accounts)} accounts")
    
    # Get account balance
    balance = await sdk.account.get_account_balance(accounts[0].id)
    print(f"Balance: {balance.current} {balance.currency}")
    
    # Get recent transactions
    transactions = await sdk.transaction.get_transactions(
        accounts[0].id, 
        limit=10
    )
    print(f"Recent transactions: {len(transactions)}")

if __name__ == "__main__":
    asyncio.run(main())
```

## Configuration

### Environment Variables

Set the provider and configuration via environment variables:

```bash
# Provider selection
export FINTECH_SDK_PROVIDER=mock  # Default provider

# Regional provider overrides
export FINTECH_SDK_PROVIDER_SWITZERLAND=swiss_bank
export FINTECH_SDK_PROVIDER_GERMANY=german_bank

# Provider-specific configuration
export PLAID_CLIENT_ID=your_client_id
export PLAID_SECRET=your_secret
export PLAID_ENVIRONMENT=sandbox

export STRIPE_API_KEY=your_api_key

export TINK_CLIENT_ID=your_client_id
export TINK_CLIENT_SECRET=your_client_secret
```

### Configuration File

Alternatively, use a JSON configuration file:

```json
{
  "provider": "plaid",
  "timeout": 30,
  "environment": "sandbox"
}
```

```python
sdk = ProviderFactory.load_from_config("config.json")
```

### Regional Provider Selection

Use different providers for different regions:

```python
# Switzerland operations
swiss_sdk = ProviderFactory.load_from_env(region="switzerland")

# Germany operations
german_sdk = ProviderFactory.load_from_env(region="germany")

# Default operations
default_sdk = ProviderFactory.load_from_env()
```

### Programmatic Configuration

```python
from fintech_sdk import ProviderFactory
from fintech_sdk.config import FinTechSDKConfig, PlaidConfig

sdk_config = FinTechSDKConfig(provider="plaid", timeout=30)
sdk = ProviderFactory(sdk_config)
```

## Supported Providers

### Mock Provider (Development/Testing)
- **Purpose**: Development and testing
- **Features**: All interfaces with realistic mock data
- **Configuration**: `MOCK_DELAY`, `MOCK_ERROR_RATE`

### Plaid (Account Aggregation)
- **Purpose**: Account and transaction data
- **Interfaces**: `AccountProvider`, `TransactionProvider`
- **Configuration**: `PLAID_CLIENT_ID`, `PLAID_SECRET`, `PLAID_ENVIRONMENT`

### Stripe (Payments)
- **Purpose**: Payment processing
- **Interfaces**: `PaymentProvider`
- **Configuration**: `STRIPE_API_KEY`, `STRIPE_WEBHOOK_SECRET`

### Tink (Investments)
- **Purpose**: Investment services
- **Interfaces**: `InvestmentProvider`
- **Configuration**: `TINK_CLIENT_ID`, `TINK_CLIENT_SECRET`, `TINK_ENVIRONMENT`

### Swiss Bank (Regional)
- **Purpose**: Swiss banking services
- **Interfaces**: `AccountProvider`, `TransactionProvider`
- **Currency**: CHF
- **Region**: Switzerland

### German Bank (Regional)
- **Purpose**: German banking services
- **Interfaces**: `AccountProvider`, `TransactionProvider`
- **Currency**: EUR
- **Region**: Germany

## API Reference

### Core Interfaces

#### AccountProvider
```python
async def get_account_list() -> List[Account]
async def get_account_balance(account_id: str) -> AccountBalance
async def get_account(account_id: str) -> Account
```

#### TransactionProvider
```python
async def get_transactions(
    account_id: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    limit: Optional[int] = None,
    offset: Optional[int] = None,
) -> List[Transaction]

async def get_transaction(transaction_id: str) -> Transaction
```

#### PaymentProvider
```python
async def create_payment(
    from_account: str, 
    payment_request: PaymentRequest
) -> Payment

async def get_payment(payment_id: str) -> Payment
async def get_payments(account_id: str, limit: int = 100, offset: int = 0) -> List[Payment]
async def cancel_payment(payment_id: str) -> Payment
```

#### InvestmentProvider
```python
async def place_order(order: InvestmentOrder) -> Investment
async def get_investment(investment_id: str) -> Investment
async def get_investments(account_id: str, limit: int = 100, offset: int = 0) -> List[Investment]
async def cancel_order(investment_id: str) -> Investment
```

### Data Models

All data models are Pydantic models with full type validation:

- `Account`: Account information
- `AccountBalance`: Account balance data
- `Transaction`: Transaction details
- `Payment`: Payment information
- `PaymentRequest`: Payment creation request
- `Investment`: Investment order details
- `InvestmentOrder`: Investment order request

## Adding a New Provider

### 1. Create Provider Implementation

```python
# src/fintech_sdk/providers/my_provider.py
from fintech_sdk.factory import register_provider
from fintech_sdk.interfaces import AccountProvider
from fintech_sdk.providers.base import BaseProvider

@register_provider("my_provider")
class MyProvider(BaseProvider, AccountProvider):
    async def get_account_list(self):
        response = await self._make_request("GET", "/accounts")
        data = await self._handle_response(response)
        return [Account(**account) for account in data["accounts"]]
```

### 2. Add Configuration

```python
# src/fintech_sdk/config.py
class MyProviderConfig(BaseSettings):
    api_key: str = Field(..., description="API key")
    base_url: str = Field(default="https://api.myprovider.com")
    
    class Config:
        env_prefix = "MY_PROVIDER_"

# Add to PROVIDER_CONFIGS
PROVIDER_CONFIGS["my_provider"] = MyProviderConfig
```

### 3. Register Provider

```python
# src/fintech_sdk/providers/__init__.py
from . import my_provider
```

### 4. Add Tests

```python
# tests/test_my_provider.py
@pytest.mark.asyncio
async def test_my_provider_accounts():
    # Test implementation
    pass
```

## Development

### Setup

```bash
# Clone repository
git clone <repository-url>
cd fintech-sdk

# Install dependencies
pip install -e .[dev]

# Install pre-commit hooks
pre-commit install
```

### Testing

```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=fintech_sdk

# Run specific test file
pytest tests/test_providers.py
```

### Linting and Type Checking

```bash
# Format code
black src tests

# Lint code
ruff check src tests

# Type check
mypy src
```

## License

MIT License - see LICENSE file for details.
