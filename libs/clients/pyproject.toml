[project]
name = "fintech-clients"
version = "0.0.0"

dependencies = [
    "boto3",
    "requests",
    "fastavro",
    "pyjwt",
    #Fintech:
    "fintech-app",
    "fintech-observability",
]

[project.optional-dependencies]
test = ["fintech-testutils"]
typing = ["mypy>=1.0.0", "types-boto3", "types-requests", "boto3-stubs[s3]"]

[tool.uv.sources]
fintech-app = { path = "../app", editable = true }
fintech-testutils = { path = "../testutils", editable = true }
fintech-observability = { path = "../observability", editable = true }

[tool.pytest.ini_options]
pythonpath = ["src"]

[tool.hatch.build.targets.wheel]
packages = ["src/fintech"]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
