import json
from datetime import datetime, timezone
from typing import Any

import boto3
import jwt
from botocore.exceptions import ClientError
from pydantic import BaseModel


class AuthToken(BaseModel):
    value: str

    @property
    def is_expired(self) -> bool:
        if not self.value:
            return True

        decoded_token = jwt.decode(self.value, options={"verify_signature": False})
        exp = decoded_token.get("exp")
        if exp is None:
            return True
        exp_time = datetime.fromtimestamp(exp, tz=timezone.utc)  # noqa: UP017

        return datetime.now(timezone.utc) > exp_time  # noqa: UP017


class AuthTokenStore:
    _s3_client: Any  # boto3 S3 client
    _bucket: str
    _key: str

    def __init__(self, bucket: str, key: str) -> None:
        self._s3_client = boto3.client("s3")
        self._bucket = bucket
        self._key = key

    def fetch(self) -> AuthToken | None:
        try:
            response = self._s3_client.get_object(Bucket=self._bucket, Key=self._key)
            content = response["Body"].read().decode("utf-8")
            if not content:
                return None

            return AuthToken(value=json.loads(content))
        except ClientError as e:
            error_code = e.response.get("Error", {}).get("Code")
            if error_code == "NoSuchKey":
                return None

            raise

    def override_s3_file(self, token: str) -> None:
        self._s3_client.put_object(
            Bucket=self._bucket,
            Key=self._key,
            Body=json.dumps(token),
            ContentType="application/json",
        )
