from datetime import datetime, timedelta, timezone

import jwt

from fintech.client.auth_token_store import AuthToken


def test_is_expired_with_no_value():
    token = AuthToken(value="")
    assert token.is_expired is True


def test_is_expired_with_expired_token():
    expired_time = datetime.now(timezone.utc) - timedelta(days=1)  # noqa: UP017
    expired_token = jwt.encode(
        {"exp": expired_time.timestamp()}, "secret", algorithm="HS256"
    )
    token = AuthToken(value=expired_token)
    assert token.is_expired is True


def test_is_expired_with_valid_token():
    valid_time = datetime.now(timezone.utc) + timedelta(days=1)  # noqa: UP017
    valid_token = jwt.encode(
        {"exp": valid_time.timestamp()}, "secret", algorithm="HS256"
    )
    token = AuthToken(value=valid_token)
    assert token.is_expired is False
