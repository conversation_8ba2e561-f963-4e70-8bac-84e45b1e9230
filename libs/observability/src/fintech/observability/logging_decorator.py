import logging
import time
from collections.abc import Callable
from functools import wraps


def logged(
    func_or_prop: Callable | None = None,
    *,
    service: str = "",
    level: int = logging.INFO,
) -> Callable:
    def decorator_logged(original_func: Callable) -> Callable:
        match original_func:
            case property():
                return property(decorator_logged(original_func.fget))
            case classmethod():
                return classmethod(decorator_logged(original_func.__func__))
            case staticmethod():
                return staticmethod(decorator_logged(original_func.__func__))

        @wraps(original_func)
        def wrapper(*args, **kwargs):
            reference = (
                f"{service} - {original_func.__name__}"
                if service
                else original_func.__name__
            )
            logging.log(level, "%s started.", reference)
            start_time = time.time()
            result = original_func(*args, **kwargs)
            logging.log(
                level,
                "%s ended. Execution time: %.2f seconds.",
                reference,
                time.time() - start_time,
            )
            return result

        return wrapper

    return decorator_logged(func_or_prop) if func_or_prop else decorator_logged
