"""Account service for application layer business logic."""

from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.repositories import (
    card_repository,
    transaction_repository,
    user_repository,
    user_settings_repository,
)


class AccountService:
    """Application service for account-related operations.

    This service coordinates domain logic and acts as the application layer
    between the presentation layer (routes) and the domain layer (repositories).
    """

    def get_account_summary(self, db: Session, user_id: UUID) -> dict[str, Any]:
        """Get comprehensive account summary with all user data.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            Comprehensive account summary

        Raises:
            ValueError: If user not found
        """
        # Get user profile
        user = user_repository.get(db, record_id=user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Get user settings
        user_settings = user_settings_repository.get_by_user_id(db, user_id=user_id)

        # Get all user data using repositories
        cards = card_repository.get_by_user_id(db, user_id=user_id)
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        # Business logic: Calculate financial metrics
        financial_overview = self._calculate_financial_overview(transactions)

        # Business logic: Analyze cards
        cards_summary = self._analyze_cards(cards, transactions)

        # Business logic: Transaction analytics
        transactions_summary = self._analyze_transactions(transactions)

        # Business logic: Recent activity
        recent_activity = self._get_recent_activity(transactions)

        return {
            "user_profile": user,  # Return raw user object, let API handle response model
            "user_settings": self._format_user_settings(user_settings),
            "financial_overview": financial_overview,
            "cards_summary": cards_summary,
            "transactions_summary": transactions_summary,
            "recent_activity": recent_activity,
        }

    def get_dashboard_data(self, db: Session, user_id: UUID) -> dict[str, Any]:
        """Get essential dashboard data for quick overview.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            Essential dashboard data

        Raises:
            ValueError: If user not found
        """
        user = user_repository.get(db, record_id=user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Get essential data
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)
        cards = card_repository.get_by_user_id(db, user_id=user_id)

        # Business logic: Quick calculations
        total_spent = sum(float(t.amount or 0) for t in transactions)

        # Business logic: Recent transactions (last 3)
        recent_transactions = sorted(
            transactions, key=lambda x: x.timestamp, reverse=True
        )[:3]

        # Domain logic: Create welcome message
        welcome_message = f"Welcome back, {user.first_name or 'User'}!"

        return {
            "welcome_message": welcome_message,
            "quick_stats": {
                "total_spent": round(total_spent, 2),
                "active_cards": len(cards),
                "recent_transactions": len(recent_transactions),
            },
            "recent_transactions": [
                {
                    "id": str(t.id),
                    "merchant_name": t.merchant_name,
                    "amount": float(t.amount or 0),
                    "timestamp": t.timestamp.isoformat() if t.timestamp else None,
                    "category": t.category,
                }
                for t in recent_transactions
            ],
        }

    def _calculate_financial_overview(self, transactions: list[Any]) -> dict[str, Any]:
        """Calculate financial overview metrics (domain logic)."""
        total_spent = sum(float(t.amount or 0) for t in transactions)
        transaction_count = len(transactions)
        average_transaction = (
            total_spent / transaction_count if transaction_count > 0 else 0
        )

        return {
            "total_spent": round(total_spent, 2),
            "transaction_count": transaction_count,
            "average_transaction": round(average_transaction, 2),
        }

    def _analyze_cards(self, cards: list[Any], transactions: list[Any]) -> dict[str, Any]:
        """Analyze cards and their usage (domain logic)."""
        card_summary = []
        for card in cards:
            card_transactions = [t for t in transactions if t.card_id == card.id]
            transaction_count = len(card_transactions)

            card_summary.append(
                {
                    "id": str(card.id),
                    "name": card.card_name,
                    "type": card.card_type,
                    "last_four": card.last_four,
                    "spending_limit": float(card.spending_limit or 0),
                    "transaction_count": transaction_count,
                }
            )

        return {
            "total_cards": len(cards),
            "cards": card_summary,
        }

    def _analyze_transactions(self, transactions: list[Any]) -> dict[str, Any]:
        """Analyze transaction patterns (domain logic)."""
        total_amount = sum(float(t.amount or 0) for t in transactions)
        total_count = len(transactions)
        average_amount = total_amount / total_count if total_count > 0 else 0

        return {
            "total_transactions": total_count,
            "total_amount": round(total_amount, 2),
            "average_transaction": round(average_amount, 2),
        }

    def _get_recent_activity(self, transactions: list[Any]) -> list[dict[str, Any]]:
        """Get recent transaction activity (domain logic)."""
        recent_transactions = sorted(
            transactions, key=lambda x: x.timestamp, reverse=True
        )[:8]

        return [
            {
                "id": str(t.id),
                "type": "transaction",
                "description": f"Purchase at {t.merchant_name or 'Unknown Merchant'}",
                "amount": float(t.amount or 0),
                "timestamp": t.timestamp.isoformat() if t.timestamp else None,
                "category": t.category,
            }
            for t in recent_transactions
        ]

    def _format_user_settings(self, user_settings: Any) -> dict[str, Any] | None:
        """Format user settings for response (domain logic)."""
        if not user_settings:
            return None

        return {
            "enable_roundups": user_settings.enable_roundups,
            "enable_percentage_saving": user_settings.enable_percentage_saving,
            "enable_recurring": user_settings.enable_recurring,
            "roundup_to": float(user_settings.roundup_to or 1),
            "percentage_saving": float(user_settings.percentage_saving or 5),
            "recurring_amount": float(user_settings.recurring_amount or 0),
        }
