"""User service for application layer business logic."""

from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.repositories import user_repository


class UserService:
    """Application service for user-related operations.

    This service coordinates domain logic and acts as the application layer
    between the presentation layer (routes) and the domain layer (repositories).
    """

    def get_user_profile(self, db: Session, user_id: UUID) -> Any:
        """Get user profile information.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            User profile response

        Raises:
            ValueError: If user not found
        """
        user = user_repository.get(db, record_id=user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        return user  # Return raw user object, let API handle response model

    def get_user_summary(self, db: Session, user_id: UUID) -> dict[str, Any]:
        """Get comprehensive user summary with welcome message.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            User summary with profile and welcome message

        Raises:
            ValueError: If user not found
        """
        user = user_repository.get(db, record_id=user_id)
        if not user:
            raise ValueError(f"User with ID {user_id} not found")

        # Business logic: Create personalized welcome message
        welcome_message = self._create_welcome_message(user)

        return {
            "profile": user,  # Return raw user object, let API handle response model
            "welcome_message": welcome_message,
            "account_status": "active",  # Domain logic for account status
        }

    def _create_welcome_message(self, user: Any) -> str:
        """Create personalized welcome message (domain logic).

        Args:
            user: User entity

        Returns:
            Personalized welcome message
        """
        name = user.first_name or "User"
        return f"Welcome back, {name}!"
