"""FinTech Services Library - Application Layer for Business Logic.

This module provides the application service layer that coordinates domain logic
and acts as a bridge between the presentation layer (routes) and the domain layer
(repositories and entities).

Following DDD principles:
- Services encapsulate business logic
- Services coordinate repository operations
- Services maintain separation of concerns
- Services provide abstractions for the presentation layer

Usage:
    from fintech.services import user_service, card_service

    # Get user profile
    profile = user_service.get_user_profile(db, user_id)

    # Get card spending summary
    summary = card_service.get_card_spending_summary(db, card_id, user_id)
"""

from .account_service import AccountService
from .card_service import CardService
from .transaction_service import TransactionService
from .user_service import UserService

# Service instances (dependency injection containers)
user_service = UserService()
card_service = CardService()
transaction_service = TransactionService()
account_service = AccountService()

__all__ = [
    "UserService",
    "CardService",
    "TransactionService",
    "AccountService",
    "user_service",
    "card_service",
    "transaction_service",
    "account_service",
]
