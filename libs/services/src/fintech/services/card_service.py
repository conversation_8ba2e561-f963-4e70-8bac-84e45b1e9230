"""Card service for application layer business logic."""

from typing import Any, cast
from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.repositories import card_repository, transaction_repository


class CardService:
    """Application service for card-related operations.

    This service coordinates domain logic and acts as the application layer
    between the presentation layer (routes) and the domain layer (repositories).
    """

    def get_user_cards(self, db: Session, user_id: UUID) -> list[Any]:
        """Get all cards for a specific user.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            List of user's cards
        """
        cards = card_repository.get_by_user_id(db, user_id=user_id)
        return cast(list[Any], cards)  # Return raw card objects, let API handle response models

    def get_card_details(self, db: Session, card_id: UUID, user_id: UUID) -> Any:
        """Get specific card details with ownership validation.

        Args:
            db: Database session
            card_id: Card identifier
            user_id: User identifier for ownership validation

        Returns:
            Card details

        Raises:
            ValueError: If card not found or doesn't belong to user
        """
        card = card_repository.get(db, record_id=card_id)
        if not card:
            raise ValueError(f"Card with ID {card_id} not found")

        # Domain logic: Validate ownership
        if card.user_id != user_id:
            raise ValueError("Card does not belong to user")

        return card  # Return raw card object, let API handle response model

    def get_card_spending_summary(
        self, db: Session, card_id: UUID, user_id: UUID
    ) -> dict[str, Any]:
        """Get spending summary for a specific card.

        Args:
            db: Database session
            card_id: Card identifier
            user_id: User identifier for ownership validation

        Returns:
            Card spending summary with business metrics

        Raises:
            ValueError: If card not found or doesn't belong to user
        """
        # Validate card ownership
        card = card_repository.get(db, record_id=card_id)
        if not card or card.user_id != user_id:
            raise ValueError("Card not found or does not belong to user")

        # Get transactions for this card
        transactions = transaction_repository.get_by_card_id(db, card_id=card_id)

        # Business logic: Calculate spending metrics
        total_spent = sum(float(t.amount or 0) for t in transactions)
        transaction_count = len(transactions)

        spending_limit = float(card.spending_limit or 0)
        remaining_limit = max(0, spending_limit - total_spent)
        utilization_percentage = (
            (total_spent / spending_limit * 100) if spending_limit > 0 else 0
        )

        # Domain logic: Determine spending status
        spending_status = self._determine_spending_status(utilization_percentage)

        return {
            "card": card,  # Return raw card object, let API handle response model
            "spending_metrics": {
                "total_spent": round(total_spent, 2),
                "transaction_count": transaction_count,
                "spending_limit": spending_limit,
                "remaining_limit": round(remaining_limit, 2),
                "utilization_percentage": round(utilization_percentage, 2),
                "spending_status": spending_status,
            },
        }

    def _determine_spending_status(self, utilization_percentage: float) -> str:
        """Determine spending status based on utilization (domain logic).

        Args:
            utilization_percentage: Card utilization percentage

        Returns:
            Spending status classification
        """
        if utilization_percentage >= 90:
            return "critical"
        if utilization_percentage >= 75:
            return "high"
        if utilization_percentage >= 50:
            return "moderate"
        return "low"
