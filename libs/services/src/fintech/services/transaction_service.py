"""Transaction service for application layer business logic."""

from datetime import datetime, timed<PERSON><PERSON>
from typing import Any
from uuid import UUID

from sqlalchemy.orm import Session

from fintech.models.repositories import transaction_repository


class TransactionService:
    """Application service for transaction-related operations.

    This service coordinates domain logic and acts as the application layer
    between the presentation layer (routes) and the domain layer (repositories).
    """

    def get_user_transactions(
        self, db: Session, user_id: UUID, skip: int = 0, limit: int = 100
    ) -> list[Any]:
        """Get transactions for a user with pagination.

        Args:
            db: Database session
            user_id: User identifier
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            List of user's transactions
        """
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        # Business logic: Apply pagination and sorting
        sorted_transactions = sorted(
            transactions, key=lambda x: x.timestamp or datetime.min, reverse=True
        )
        paginated_transactions = sorted_transactions[skip : skip + limit]

        return (
            paginated_transactions  # Return raw objects, let API handle response models
        )

    def get_transaction_details(
        self, db: Session, transaction_id: UUID, user_id: UUID
    ) -> Any:
        """Get specific transaction details with ownership validation.

        Args:
            db: Database session
            transaction_id: Transaction identifier
            user_id: User identifier for ownership validation

        Returns:
            Transaction details

        Raises:
            ValueError: If transaction not found or doesn't belong to user
        """
        transaction = transaction_repository.get(db, record_id=transaction_id)
        if not transaction:
            raise ValueError(f"Transaction with ID {transaction_id} not found")

        # Domain logic: Validate ownership
        if transaction.user_id != user_id:
            raise ValueError("Transaction does not belong to user")

        return transaction  # Return raw object, let API handle response model

    def get_transaction_statistics(self, db: Session, user_id: UUID) -> dict[str, Any]:
        """Get comprehensive transaction statistics for a user.

        Args:
            db: Database session
            user_id: User identifier

        Returns:
            Transaction statistics and analytics
        """
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        if not transactions:
            return self._empty_statistics()

        # Business logic: Calculate comprehensive statistics
        total_amount = sum(float(t.amount or 0) for t in transactions)
        total_count = len(transactions)
        average_amount = total_amount / total_count if total_count > 0 else 0

        # Domain logic: Category analysis
        categories = self._analyze_categories(transactions)

        # Domain logic: Time-based analysis
        monthly_spending = self._analyze_monthly_spending(transactions)

        # Domain logic: Recent activity
        recent_activity = self._get_recent_activity(transactions)

        # Domain logic: Spending insights
        insights = self._generate_spending_insights(
            transactions, total_amount, average_amount
        )

        return {
            "summary": {
                "total_transactions": total_count,
                "total_amount": round(total_amount, 2),
                "average_amount": round(average_amount, 2),
            },
            "categories": categories,
            "monthly_spending": monthly_spending,
            "recent_activity": recent_activity,
            "insights": insights,
        }

    def get_spending_by_category(
        self, db: Session, user_id: UUID, days: int = 30
    ) -> dict[str, float]:
        """Get spending breakdown by category for the last N days.

        Args:
            db: Database session
            user_id: User identifier
            days: Number of days to analyze

        Returns:
            Category spending breakdown
        """
        transactions = transaction_repository.get_by_user_id(db, user_id=user_id)

        # Business logic: Filter by date range
        cutoff_date = datetime.now() - timedelta(days=days)
        recent_transactions = [
            t for t in transactions if t.timestamp and t.timestamp >= cutoff_date
        ]

        # Group by category and return only amounts
        category_spending = {}
        for transaction in recent_transactions:
            category = transaction.category or "Uncategorized"
            if category not in category_spending:
                category_spending[category] = 0.0
            category_spending[category] += float(transaction.amount or 0)

        return {
            category: round(amount, 2) for category, amount in category_spending.items()
        }

    def _empty_statistics(self) -> dict[str, Any]:
        """Return empty statistics structure."""
        return {
            "summary": {
                "total_transactions": 0,
                "total_amount": 0.0,
                "average_amount": 0.0,
            },
            "categories": {},
            "monthly_spending": {},
            "recent_activity": [],
            "insights": [],
        }

    def _analyze_categories(self, transactions: list) -> dict[str, dict[str, Any]]:
        """Analyze spending by category (domain logic)."""
        categories = {}
        for transaction in transactions:
            category = transaction.category or "Uncategorized"
            if category not in categories:
                categories[category] = {"count": 0, "amount": 0.0}
            categories[category]["count"] += 1
            categories[category]["amount"] += float(transaction.amount or 0)

        # Round amounts for presentation
        for category_data in categories.values():
            category_data["amount"] = round(category_data["amount"], 2)

        return categories

    def _analyze_monthly_spending(self, transactions: list) -> dict[str, float]:
        """Analyze monthly spending trends (domain logic)."""
        monthly_spending = {}
        for transaction in transactions:
            if transaction.timestamp:
                month_key = transaction.timestamp.strftime("%Y-%m")
                if month_key not in monthly_spending:
                    monthly_spending[month_key] = 0.0
                monthly_spending[month_key] += float(transaction.amount or 0)

        return {
            month: round(amount, 2)
            for month, amount in sorted(monthly_spending.items())
        }

    def _get_recent_activity(self, transactions: list[Any]) -> list[dict[str, Any]]:
        """Get recent transaction activity (domain logic)."""
        recent_transactions = sorted(
            transactions, key=lambda x: x.timestamp or datetime.min, reverse=True
        )[:5]

        return [
            {
                "id": str(t.id),
                "amount": float(t.amount or 0),
                "merchant_name": t.merchant_name,
                "category": t.category,
                "timestamp": t.timestamp.isoformat() if t.timestamp else None,
            }
            for t in recent_transactions
        ]

    def _generate_spending_insights(
        self,
        transactions: list,
        total_amount: float,
        average_amount: float,
    ) -> list[str]:
        """Generate spending insights (domain logic)."""
        insights = []

        if total_amount > 1000:
            insights.append("High spending detected this period")

        if average_amount > 100:
            insights.append("Above average transaction amounts")

        # Category insights
        categories = self._analyze_categories(transactions)
        if categories:
            top_category = max(categories.items(), key=lambda x: x[1]["amount"])
            insights.append(f"Top spending category: {top_category[0]}")

        return insights
