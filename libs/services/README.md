# FinTech Services Library

The FinTech Services Library provides the application layer for business logic coordination in the FinTech platform. This library implements Domain-Driven Design (DDD) principles and acts as the bridge between the presentation layer (API routes) and the domain layer (models and repositories).

## Architecture

This library follows DDD layered architecture:

- **Application Services**: Coordinate domain logic and orchestrate repository operations
- **Domain Logic**: Business rules and calculations encapsulated in service methods
- **Repository Integration**: Uses fintech-models library for data access
- **Clean Separation**: No infrastructure concerns leak into business logic

## Services

### UserService
- User profile management
- Welcome message generation
- User summary coordination

### CardService  
- Card management operations
- Spending analysis and status determination
- Card ownership validation

### TransactionService
- Transaction analytics and statistics
- Category analysis and insights
- Spending pattern recognition

### AccountService
- Comprehensive account summaries
- Dashboard data coordination
- Financial overview calculations

## Installation

```bash
# Install from local wheel
pip install dist/fintech_services-*.whl

# Install in development mode
pip install -e .

# Install with development dependencies
pip install -e ".[dev]"
```

## Usage

```python
from fintech.services import (
    user_service,
    card_service, 
    transaction_service,
    account_service
)

# Get user profile
user_profile = user_service.get_user_profile(db, user_id)

# Get card spending summary
spending_summary = card_service.get_card_spending_summary(db, card_id, user_id)

# Get transaction statistics
stats = transaction_service.get_transaction_statistics(db, user_id)

# Get account summary
summary = account_service.get_account_summary(db, user_id)
```

## Development

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src --cov-report=html

# Run specific test types
pytest -m unit
pytest -m integration
```

### Code Quality

```bash
# Type checking
mypy src/

# Linting
ruff check src/

# Formatting
ruff format src/
```

## Dependencies

- **fintech-models**: Domain models and repositories
- **pydantic**: Data validation and serialization
- **sqlalchemy**: Database ORM integration

## Design Principles

### Domain-Driven Design (DDD)
- Services encapsulate business logic
- Clear separation between layers
- Domain logic isolated from infrastructure

### Single Responsibility
- Each service handles one domain area
- Methods have focused responsibilities
- Clear interfaces and contracts

### Dependency Inversion
- Services depend on repository abstractions
- No direct database access
- Testable through dependency injection

## Testing Strategy

### Unit Tests
- Service method testing with mocked repositories
- Domain logic validation
- Error handling verification

### Integration Tests  
- Service coordination testing
- Repository integration validation
- End-to-end business flow testing

## Contributing

1. Follow DDD principles
2. Write comprehensive tests
3. Maintain clean interfaces
4. Document business logic
5. Use type hints throughout
