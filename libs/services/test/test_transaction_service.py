"""Unit tests for TransactionService."""

# Mock the fintech.models module to avoid import issues
import sys
from datetime import datetime, timedelta
from unittest.mock import MagicMock, Mock
from uuid import uuid4

import pytest

mock_models = MagicMock()
mock_models.Transaction = MagicMock()
mock_models.TransactionResponse = MagicMock()
mock_models.repositories = MagicMock()
sys.modules['fintech.models'] = mock_models
sys.modules['fintech.models.repositories'] = mock_models.repositories

from fintech.services.transaction_service import TransactionService


class TestTransactionService:
    """Test the TransactionService following DDD principles."""

    def setup_method(self):
        """Set up test fixtures."""
        self.transaction_service = TransactionService()
        self.mock_db = Mock()
        self.test_user_id = uuid4()
        self.test_transaction_id = uuid4()

        # Reset mocks for each test
        mock_models.repositories.transaction_repository.reset_mock()

    def test_get_user_transactions_success(self):
        """Test successful user transactions retrieval."""
        # Arrange
        mock_transactions = [<PERSON><PERSON>(), <PERSON><PERSON>(), <PERSON><PERSON>()]
        for i, t in enumerate(mock_transactions):
            t.timestamp = datetime.now() - timedelta(days=i)

        mock_models.repositories.transaction_repository.get_by_user_id.return_value = mock_transactions

        # Act
        result = self.transaction_service.get_user_transactions(
            self.mock_db, self.test_user_id, skip=0, limit=10
        )

        # Assert
        mock_models.repositories.transaction_repository.get_by_user_id.assert_called_once_with(
            self.mock_db, user_id=self.test_user_id
        )
        assert len(result) == 3  # Service returns raw transaction objects

    def test_get_user_transactions_with_pagination(self):
        """Test user transactions retrieval with pagination."""
        # Arrange
        mock_transactions = [Mock() for _ in range(5)]
        for i, t in enumerate(mock_transactions):
            t.timestamp = datetime.now() - timedelta(days=i)

        mock_models.repositories.transaction_repository.get_by_user_id.return_value = mock_transactions

        # Act
        result = self.transaction_service.get_user_transactions(
            self.mock_db, self.test_user_id, skip=2, limit=2
        )

        # Assert
        assert len(result) == 2  # Pagination should return 2 items

    def test_get_transaction_details_success(self):
        """Test successful transaction details retrieval."""
        # Arrange
        mock_transaction = Mock()
        mock_transaction.user_id = self.test_user_id
        mock_models.repositories.transaction_repository.get.return_value = mock_transaction

        # Act
        result = self.transaction_service.get_transaction_details(
            self.mock_db, self.test_transaction_id, self.test_user_id
        )

        # Assert
        assert result == mock_transaction  # Service returns raw transaction object

    def test_get_transaction_details_not_found(self):
        """Test transaction details retrieval when transaction not found."""
        # Arrange
        mock_models.repositories.transaction_repository.get.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="Transaction with ID .* not found"):
            self.transaction_service.get_transaction_details(
                self.mock_db, self.test_transaction_id, self.test_user_id
            )

    def test_get_transaction_details_wrong_owner(self):
        """Test transaction details retrieval when transaction belongs to different user."""
        # Arrange
        mock_transaction = Mock()
        mock_transaction.user_id = uuid4()  # Different user
        mock_models.repositories.transaction_repository.get.return_value = mock_transaction

        # Act & Assert
        with pytest.raises(ValueError, match="Transaction does not belong to user"):
            self.transaction_service.get_transaction_details(
                self.mock_db, self.test_transaction_id, self.test_user_id
            )

    def test_get_transaction_statistics_empty(self):
        """Test transaction statistics with no transactions."""
        # Arrange
        mock_models.repositories.transaction_repository.get_by_user_id.return_value = []

        # Act
        result = self.transaction_service.get_transaction_statistics(
            self.mock_db, self.test_user_id
        )

        # Assert
        assert result["summary"]["total_transactions"] == 0
        assert result["summary"]["total_amount"] == 0.0
        assert result["summary"]["average_amount"] == 0.0
        assert result["categories"] == {}
        assert result["insights"] == []

    def test_get_transaction_statistics_with_data(self):
        """Test transaction statistics with transaction data."""
        # Arrange
        mock_transactions = [Mock(), Mock()]
        mock_transactions[0].amount = 100.0
        mock_transactions[0].category = "Food"
        mock_transactions[0].timestamp = datetime.now()
        mock_transactions[1].amount = 200.0
        mock_transactions[1].category = "Shopping"
        mock_transactions[1].timestamp = datetime.now()

        mock_models.repositories.transaction_repository.get_by_user_id.return_value = mock_transactions

        # Act
        result = self.transaction_service.get_transaction_statistics(
            self.mock_db, self.test_user_id
        )

        # Assert
        assert result["summary"]["total_transactions"] == 2
        assert result["summary"]["total_amount"] == 300.0
        assert result["summary"]["average_amount"] == 150.0
        assert "Food" in result["categories"]
        assert "Shopping" in result["categories"]

    def test_get_spending_by_category_success(self):
        """Test spending by category analysis."""
        # Arrange
        now = datetime.now()
        mock_transactions = [Mock(), Mock()]
        mock_transactions[0].amount = 50.0
        mock_transactions[0].category = "Food"
        mock_transactions[0].timestamp = now
        mock_transactions[1].amount = 100.0
        mock_transactions[1].category = "Food"
        mock_transactions[1].timestamp = now

        mock_models.repositories.transaction_repository.get_by_user_id.return_value = mock_transactions

        # Act
        result = self.transaction_service.get_spending_by_category(
            self.mock_db, self.test_user_id, days=30
        )

        # Assert
        assert "Food" in result
        assert result["Food"] == 150.0

    def test_analyze_categories(self):
        """Test category analysis domain logic."""
        # Arrange
        mock_transactions = [Mock(), Mock(), Mock()]
        mock_transactions[0].category = "Food"
        mock_transactions[0].amount = 25.0
        mock_transactions[1].category = "Food"
        mock_transactions[1].amount = 35.0
        mock_transactions[2].category = "Shopping"
        mock_transactions[2].amount = 100.0

        # Act
        result = self.transaction_service._analyze_categories(mock_transactions)

        # Assert
        assert "Food" in result
        assert "Shopping" in result
        assert result["Food"]["count"] == 2
        assert result["Food"]["amount"] == 60.0
        assert result["Shopping"]["count"] == 1
        assert result["Shopping"]["amount"] == 100.0

    def test_generate_spending_insights_high_spending(self):
        """Test spending insights generation for high spending."""
        # Arrange
        mock_transactions = []

        # Act
        result = self.transaction_service._generate_spending_insights(
            mock_transactions, 1500.0, 150.0
        )

        # Assert
        assert "High spending detected this period" in result
        assert "Above average transaction amounts" in result

    def test_generate_spending_insights_low_spending(self):
        """Test spending insights generation for low spending."""
        # Arrange
        mock_transactions = []

        # Act
        result = self.transaction_service._generate_spending_insights(
            mock_transactions, 500.0, 50.0
        )

        # Assert
        assert "High spending detected this period" not in result
        assert "Above average transaction amounts" not in result

    def test_generate_spending_insights_with_categories(self):
        """Test spending insights generation with category analysis."""
        # Arrange
        mock_transactions = [Mock()]
        mock_transactions[0].category = "Electronics"
        mock_transactions[0].amount = 500.0

        # Act
        result = self.transaction_service._generate_spending_insights(
            mock_transactions, 500.0, 500.0
        )

        # Assert
        assert any("Top spending category: Electronics" in insight for insight in result)
