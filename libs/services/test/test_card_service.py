"""Unit tests for CardService."""

# Mock the fintech.models module to avoid import issues
import sys
from unittest.mock import MagicMock, <PERSON>ck
from uuid import uuid4

import pytest

mock_models = MagicMock()
mock_models.Card = MagicMock()
mock_models.CardResponse = MagicMock()
mock_models.repositories = MagicMock()
sys.modules['fintech.models'] = mock_models
sys.modules['fintech.models.repositories'] = mock_models.repositories

from fintech.services.card_service import CardService


class TestCardService:
    """Test the CardService following DDD principles."""

    def setup_method(self):
        """Set up test fixtures."""
        self.card_service = CardService()
        self.mock_db = Mock()
        self.test_user_id = uuid4()
        self.test_card_id = uuid4()

    def test_get_user_cards_success(self):
        """Test successful user cards retrieval."""
        # Arrange
        mock_cards = [<PERSON><PERSON>(), <PERSON><PERSON>()]
        mock_models.repositories.card_repository.get_by_user_id.return_value = mock_cards
        mock_models.CardResponse.model_validate.side_effect = lambda x: x

        # Act
        result = self.card_service.get_user_cards(self.mock_db, self.test_user_id)

        # Assert
        mock_models.repositories.card_repository.get_by_user_id.assert_called_once_with(
            self.mock_db, user_id=self.test_user_id
        )
        assert len(result) == 2

    def test_get_user_cards_empty(self):
        """Test user cards retrieval when no cards exist."""
        # Arrange
        mock_models.repositories.card_repository.get_by_user_id.return_value = []

        # Act
        result = self.card_service.get_user_cards(self.mock_db, self.test_user_id)

        # Assert
        assert result == []

    def test_get_card_details_success(self):
        """Test successful card details retrieval."""
        # Arrange
        mock_card = Mock()
        mock_card.user_id = self.test_user_id
        mock_models.repositories.card_repository.get.return_value = mock_card
        mock_models.CardResponse.model_validate.return_value = mock_card

        # Act
        result = self.card_service.get_card_details(
            self.mock_db, self.test_card_id, self.test_user_id
        )

        # Assert
        assert result == mock_card

    def test_get_card_details_not_found(self):
        """Test card details retrieval when card not found."""
        # Arrange
        mock_models.repositories.card_repository.get.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="Card with ID .* not found"):
            self.card_service.get_card_details(
                self.mock_db, self.test_card_id, self.test_user_id
            )

    def test_get_card_details_wrong_owner(self):
        """Test card details retrieval when card belongs to different user."""
        # Arrange
        mock_card = Mock()
        mock_card.user_id = uuid4()  # Different user
        mock_models.repositories.card_repository.get.return_value = mock_card

        # Act & Assert
        with pytest.raises(ValueError, match="Card does not belong to user"):
            self.card_service.get_card_details(
                self.mock_db, self.test_card_id, self.test_user_id
            )

    def test_get_card_spending_summary_success(self):
        """Test successful card spending summary retrieval."""
        # Arrange
        mock_card = Mock()
        mock_card.user_id = self.test_user_id
        mock_card.spending_limit = 1000.0
        mock_models.repositories.card_repository.get.return_value = mock_card
        mock_models.CardResponse.model_validate.return_value = mock_card

        mock_transactions = [Mock(), Mock()]
        mock_transactions[0].amount = 100.0
        mock_transactions[1].amount = 200.0
        mock_models.repositories.transaction_repository.get_by_card_id.return_value = mock_transactions

        # Act
        result = self.card_service.get_card_spending_summary(
            self.mock_db, self.test_card_id, self.test_user_id
        )

        # Assert
        assert "card" in result
        assert "spending_metrics" in result
        metrics = result["spending_metrics"]
        assert metrics["total_spent"] == 300.0
        assert metrics["transaction_count"] == 2
        assert metrics["spending_limit"] == 1000.0
        assert metrics["remaining_limit"] == 700.0
        assert metrics["utilization_percentage"] == 30.0
        assert metrics["spending_status"] == "low"

    def test_get_card_spending_summary_not_found(self):
        """Test card spending summary when card not found."""
        # Arrange
        mock_models.repositories.card_repository.get.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="Card not found or does not belong to user"):
            self.card_service.get_card_spending_summary(
                self.mock_db, self.test_card_id, self.test_user_id
            )

    def test_determine_spending_status_critical(self):
        """Test spending status determination for critical utilization."""
        # Act
        result = self.card_service._determine_spending_status(95.0)

        # Assert
        assert result == "critical"

    def test_determine_spending_status_high(self):
        """Test spending status determination for high utilization."""
        # Act
        result = self.card_service._determine_spending_status(80.0)

        # Assert
        assert result == "high"

    def test_determine_spending_status_moderate(self):
        """Test spending status determination for moderate utilization."""
        # Act
        result = self.card_service._determine_spending_status(60.0)

        # Assert
        assert result == "moderate"

    def test_determine_spending_status_low(self):
        """Test spending status determination for low utilization."""
        # Act
        result = self.card_service._determine_spending_status(30.0)

        # Assert
        assert result == "low"

    def test_determine_spending_status_zero(self):
        """Test spending status determination for zero utilization."""
        # Act
        result = self.card_service._determine_spending_status(0.0)

        # Assert
        assert result == "low"
