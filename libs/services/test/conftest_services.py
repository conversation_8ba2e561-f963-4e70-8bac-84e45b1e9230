"""Pytest configuration and fixtures for services library tests."""

import sys
from unittest.mock import MagicMock

import pytest


@pytest.fixture(autouse=True)
def mock_fintech_models():
    """Automatically mock fintech.models for all tests."""
    # Create fresh mocks for each test
    mock_models = MagicMock()
    mock_models.User = MagicMock()
    mock_models.Card = MagicMock()
    mock_models.Transaction = MagicMock()
    mock_models.UserResponse = MagicMock()
    mock_models.CardResponse = MagicMock()
    mock_models.TransactionResponse = MagicMock()
    mock_models.repositories = MagicMock()

    # Mock the individual repositories
    mock_user_repo = MagicMock()
    mock_card_repo = MagicMock()
    mock_transaction_repo = MagicMock()

    mock_models.repositories.user_repository = mock_user_repo
    mock_models.repositories.card_repository = mock_card_repo
    mock_models.repositories.transaction_repository = mock_transaction_repo

    # Install the mocks in sys.modules
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    # Also mock the individual repository imports
    sys.modules['fintech.models.repositories.user_repository'] = mock_user_repo
    sys.modules['fintech.models.repositories.card_repository'] = mock_card_repo
    sys.modules['fintech.models.repositories.transaction_repository'] = mock_transaction_repo

    yield mock_models

    # Clean up after test (optional, but good practice)
    # The mocks will be recreated for the next test anyway


@pytest.fixture
def mock_user():
    """Create a mock user for testing."""
    from unittest.mock import Mock
    user = Mock()
    user.id = "test-user-id"
    user.email = "<EMAIL>"
    user.first_name = "John"
    user.last_name = "Doe"
    return user


@pytest.fixture
def mock_card():
    """Create a mock card for testing."""
    from unittest.mock import Mock
    card = Mock()
    card.id = "test-card-id"
    card.user_id = "test-user-id"
    card.card_number = "**** **** **** 1234"
    card.card_type = "credit"
    card.credit_limit = 5000.0
    card.current_balance = 1500.0
    return card


@pytest.fixture
def mock_transaction():
    """Create a mock transaction for testing."""
    from datetime import datetime
    from unittest.mock import Mock

    transaction = Mock()
    transaction.id = "test-transaction-id"
    transaction.user_id = "test-user-id"
    transaction.amount = 100.0
    transaction.merchant_name = "Test Merchant"
    transaction.category = "Food"
    transaction.timestamp = datetime.now()
    return transaction


@pytest.fixture
def mock_db():
    """Create a mock database session."""
    from unittest.mock import Mock
    return Mock()
