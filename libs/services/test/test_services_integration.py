#!/usr/bin/env python3
"""Integration tests for services library that work reliably in PyCharm."""

import sys
from pathlib import Path
from unittest.mock import MagicMock, Mock


def test_services_library_structure():
    """Test that services library has correct structure."""
    # Get the services directory relative to this test file
    test_file = Path(__file__)
    services_dir = test_file.parent.parent

    # Check main files exist
    assert (services_dir / "pyproject.toml").exists(), "pyproject.toml should exist"
    assert (services_dir / "README.md").exists(), "README.md should exist"
    assert (services_dir / "LICENSE").exists(), "LICENSE should exist"

    # Check source structure
    src_dir = services_dir / "src" / "fintech" / "services"
    assert src_dir.exists(), "Services source directory should exist"
    assert (src_dir / "__init__.py").exists(), "Services __init__.py should exist"

    # Check service files
    service_files = [
        "user_service.py",
        "card_service.py",
        "transaction_service.py",
        "account_service.py"
    ]

    for service_file in service_files:
        service_path = src_dir / service_file
        assert service_path.exists(), f"{service_file} should exist"

        # Check that file contains expected class
        content = service_path.read_text()
        service_class = service_file.replace("_service.py", "").title().replace("_", "") + "Service"
        assert f"class {service_class}" in content, f"Should have {service_class} class"


def test_services_can_be_imported():
    """Test that services can be imported with mocked dependencies."""
    # Mock fintech.models to avoid dependency issues
    mock_models = MagicMock()
    mock_models.repositories = MagicMock()
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    try:
        # Import services - this should work
        from fintech.services import (
            AccountService,
            CardService,
            TransactionService,
            UserService,
            account_service,
            card_service,
            transaction_service,
            user_service,
        )

        # Test that classes exist
        assert UserService is not None
        assert CardService is not None
        assert TransactionService is not None
        assert AccountService is not None

        # Test that instances exist
        assert user_service is not None
        assert card_service is not None
        assert transaction_service is not None
        assert account_service is not None

        # Import successful

    except ImportError as e:
        assert False, f"Failed to import services: {e}"


def test_user_service_domain_logic():
    """Test UserService domain logic without complex mocking."""
    # Mock fintech.models
    mock_models = MagicMock()
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    from fintech.services.user_service import UserService

    user_service = UserService()

    # Test domain logic method directly
    mock_user = Mock()
    mock_user.first_name = "John"

    welcome_msg = user_service._create_welcome_message(mock_user)
    assert welcome_msg == "Welcome back, John!"

    # Test with no first name
    mock_user.first_name = None
    welcome_msg = user_service._create_welcome_message(mock_user)
    assert welcome_msg == "Welcome back, User!"


def test_card_service_domain_logic():
    """Test CardService domain logic without complex mocking."""
    # Mock fintech.models
    mock_models = MagicMock()
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    from fintech.services.card_service import CardService

    card_service = CardService()

    # Test spending status logic
    assert card_service._determine_spending_status(95.0) == "critical"
    assert card_service._determine_spending_status(85.0) == "high"
    assert card_service._determine_spending_status(65.0) == "moderate"
    assert card_service._determine_spending_status(30.0) == "low"
    assert card_service._determine_spending_status(0.0) == "low"


def test_transaction_service_domain_logic():
    """Test TransactionService domain logic without complex mocking."""
    # Mock fintech.models
    mock_models = MagicMock()
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    from fintech.services.transaction_service import TransactionService

    transaction_service = TransactionService()

    # Test category analysis
    mock_transactions = [Mock(), Mock(), Mock()]
    mock_transactions[0].category = "Food"
    mock_transactions[0].amount = 25.0
    mock_transactions[1].category = "Food"
    mock_transactions[1].amount = 35.0
    mock_transactions[2].category = "Shopping"
    mock_transactions[2].amount = 100.0

    result = transaction_service._analyze_categories(mock_transactions)

    assert "Food" in result
    assert "Shopping" in result
    assert result["Food"]["count"] == 2
    assert result["Food"]["amount"] == 60.0
    assert result["Shopping"]["count"] == 1
    assert result["Shopping"]["amount"] == 100.0


def test_account_service_domain_logic():
    """Test AccountService domain logic without complex mocking."""
    # Mock fintech.models
    mock_models = MagicMock()
    sys.modules['fintech.models'] = mock_models
    sys.modules['fintech.models.repositories'] = mock_models.repositories

    from fintech.services.account_service import AccountService

    account_service = AccountService()

    # Test financial overview calculation
    mock_transactions = [Mock(), Mock()]
    mock_transactions[0].amount = 100.0
    mock_transactions[1].amount = 200.0

    overview = account_service._calculate_financial_overview(mock_transactions)

    assert "total_spent" in overview
    assert "transaction_count" in overview
    assert "average_transaction" in overview
    assert overview["total_spent"] == 300.0
    assert overview["transaction_count"] == 2
    assert overview["average_transaction"] == 150.0


def test_services_follow_ddd_principles():
    """Test that services follow DDD principles."""
    test_file = Path(__file__)
    services_dir = test_file.parent.parent / "src" / "fintech" / "services"

    service_files = [
        "user_service.py",
        "card_service.py",
        "transaction_service.py",
        "account_service.py"
    ]

    for service_file in service_files:
        service_path = services_dir / service_file
        content = service_path.read_text()

        # Should have class definition
        service_class = service_file.replace("_service.py", "").title().replace("_", "") + "Service"
        assert f"class {service_class}" in content, f"Should have {service_class} class"

        # Should have domain logic methods (private methods starting with _)
        assert "def _" in content, f"{service_file} should have domain logic methods"

        # Should import from repositories
        assert "from fintech.models.repositories import" in content, f"{service_file} should import repositories"

        # Should not use response models directly (clean separation)
        assert "Response.model_validate" not in content, f"{service_file} should not use response models directly"


def test_services_library_documentation():
    """Test that services library has proper documentation."""
    test_file = Path(__file__)
    services_dir = test_file.parent.parent

    # Check that README exists and has key content
    readme_path = services_dir / "README.md"
    assert readme_path.exists(), "README.md should exist"

    readme_content = readme_path.read_text()
    assert "DDD" in readme_content or "Domain-Driven Design" in readme_content, "README should mention DDD"
    assert "application layer" in readme_content.lower(), "README should mention application layer"
    assert "business logic" in readme_content.lower(), "README should mention business logic"

    # Check that pyproject.toml exists
    pyproject_path = services_dir / "pyproject.toml"
    assert pyproject_path.exists(), "pyproject.toml should exist"


if __name__ == "__main__":
    """Run tests when executed directly."""
    import traceback

    tests = [
        test_services_library_structure,
        test_services_can_be_imported,
        test_user_service_domain_logic,
        test_card_service_domain_logic,
        test_transaction_service_domain_logic,
        test_account_service_domain_logic,
        test_services_follow_ddd_principles,
        test_services_library_documentation,
    ]

    print("🧪 Running Services Library Integration Tests")
    print("=" * 60)

    passed = 0
    failed = 0

    for test in tests:
        try:
            test()
            print(f"✅ {test.__name__}")
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: {e}")
            traceback.print_exc()
            failed += 1

    print("\n" + "=" * 60)
    print(f"📊 Results: {passed} passed, {failed} failed")

    if failed == 0:
        print("🎉 All integration tests passed!")
        print("\n📋 Summary:")
        print("✅ Services library structure correct")
        print("✅ Services can be imported successfully")
        print("✅ Domain logic working correctly")
        print("✅ Services follow DDD principles")
        print("✅ Documentation is comprehensive")
        print("\n🚀 Services library is ready for development!")
    else:
        print(f"⚠️  {failed} tests failed - check output above")
