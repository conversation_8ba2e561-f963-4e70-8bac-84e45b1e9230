"""Unit tests for UserService."""

from unittest.mock import Mock
from uuid import uuid4

import pytest

from fintech.services.user_service import UserService


class TestUserService:
    """Test the UserService following DDD principles."""

    def setup_method(self):
        """Set up test fixtures."""
        self.user_service = UserService()
        self.test_user_id = uuid4()

    def test_get_user_profile_success(self, mock_fintech_models, mock_db):
        """Test successful user profile retrieval."""
        # Arrange
        mock_user = Mock()
        mock_user.id = self.test_user_id
        mock_user.email = "<EMAIL>"
        mock_user.first_name = "Test"
        mock_user.last_name = "User"

        mock_fintech_models.repositories.user_repository.get.return_value = mock_user

        # Act
        result = self.user_service.get_user_profile(mock_db, self.test_user_id)

        # Assert
        mock_fintech_models.repositories.user_repository.get.assert_called_once_with(
            mock_db, record_id=self.test_user_id
        )
        # Service now returns raw user object, not response model
        assert result == mock_user

    def test_get_user_profile_not_found(self, mock_fintech_models, mock_db):
        """Test user profile retrieval when user not found."""
        # Arrange
        mock_fintech_models.repositories.user_repository.get.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="User with ID .* not found"):
            self.user_service.get_user_profile(mock_db, self.test_user_id)

    def test_get_user_summary_success(self, mock_fintech_models, mock_db):
        """Test successful user summary retrieval."""
        # Arrange
        mock_user = Mock()
        mock_user.first_name = "John"
        mock_fintech_models.repositories.user_repository.get.return_value = mock_user

        # Act
        result = self.user_service.get_user_summary(mock_db, self.test_user_id)

        # Assert
        assert "profile" in result
        assert "welcome_message" in result
        assert "account_status" in result
        assert result["welcome_message"] == "Welcome back, John!"
        assert result["account_status"] == "active"
        assert result["profile"] == mock_user  # Service returns raw user object

    def test_get_user_summary_not_found(self, mock_fintech_models, mock_db):
        """Test user summary retrieval when user not found."""
        # Arrange
        mock_fintech_models.repositories.user_repository.get.return_value = None

        # Act & Assert
        with pytest.raises(ValueError, match="User with ID .* not found"):
            self.user_service.get_user_summary(mock_db, self.test_user_id)

    def test_create_welcome_message_with_name(self):
        """Test welcome message creation with first name."""
        # Arrange
        mock_user = Mock()
        mock_user.first_name = "John"

        # Act
        result = self.user_service._create_welcome_message(mock_user)

        # Assert
        assert result == "Welcome back, John!"

    def test_create_welcome_message_without_name(self):
        """Test welcome message creation without first name."""
        # Arrange
        mock_user = Mock()
        mock_user.first_name = None

        # Act
        result = self.user_service._create_welcome_message(mock_user)

        # Assert
        assert result == "Welcome back, User!"

    def test_create_welcome_message_empty_name(self):
        """Test welcome message creation with empty first name."""
        # Arrange
        mock_user = Mock()
        mock_user.first_name = ""

        # Act
        result = self.user_service._create_welcome_message(mock_user)

        # Assert
        assert result == "Welcome back, User!"
