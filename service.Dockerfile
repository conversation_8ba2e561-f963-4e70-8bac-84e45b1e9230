ARG PYTHON_BASE=3.12

FROM --platform=linux/amd64 public.ecr.aws/lambda/python:$PYTHON_BASE AS builder
ARG APP_SERVICE

# Ensure the service has been specified during build:
RUN : "${FINTECH_SERVICE:?--build-arg APP_SERVICE=<service-name> must be specified during build}"

# Install UV (the build tool) - just the pure Debian binaries
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/

# Copy all the potentially deployable code into the builder image.
# After the build is done and all the dependencies are in place (including the code ones),
# we'll only need to retain the service in the $LAMBDA_TASK_ROOT directory, dropping the rest.
COPY libs /tmp/fintech/libs
COPY services/$FINTECH_SERVICE /tmp/fintech/services/$FINTECH_SERVICE

# Work in the context of the installation directory. It should contain all the resources already.
WORKDIR /tmp/fintech/services/$FINTECH_SERVICE

# Install all the dependencies into Lambda's Python directories. Lambda doesn't use virtualenvs.
# Note: we skip installing the service itself, because that is going to be available directly.
ENV UV_PROJECT_ENVIRONMENT /var/lang
RUN uv sync --frozen --no-editable --no-install-workspace

# The service image is just a clean Lambda image with the dependencies and service code retained.
FROM --platform=linux/amd64 public.ecr.aws/lambda/python:$PYTHON_BASE AS service
ARG FINTECH_SERVICE
ARG PYTHON_BASE
ENV FINTECH_SERVICE $FINTECH_SERVICE

# Retain the complete Python library set from builder:
COPY --from=builder /var/lang/lib/python$PYTHON_BASE/site-packages /var/lang/lib/python$PYTHON_BASE/site-packages

# Retain the service itself:
COPY --from=builder /tmp/fintech/services/$FINTECH_SERVICE/src $LAMBDA_TASK_ROOT

# Because CMD [] doesn't support variable substitution, we need to replace the entire entrypoint.
ENTRYPOINT /lambda-entrypoint.sh fintech.services.$FINTECH_SERVICE.main.handler
